# Requirements Document

## Introduction

The referee dues system in the pay-connect application currently has multiple issues that prevent it from working properly. This feature aims to fix all the broken functionality in the staff dues system, improve the user experience, and ensure referees can properly view and pay their dues. The system needs to handle payment processing, status tracking, notifications, and provide a comprehensive dashboard experience for referee payments.

## Requirements

### Requirement 1: Fix Staff Dues Backend API

**User Story:** As a staff member (referee), I want to view my dues accurately so that I can see what payments I owe and their current status.

#### Acceptance Criteria

1. WHEN a staff member requests their dues THEN the system SHALL return accurate dues data from the database
2. WHEN filtering dues by status THEN the system SHALL correctly filter results based on Paid, Pending, or Overdue status
3. WHEN searching dues THEN the system SHALL search across firstname, email, and game_title fields
4. WHEN calculating due amounts THEN the system SHALL use the correct wage calculation logic based on wage_unit_id
5. WHEN determining due status THEN the system SHALL correctly identify Overdue dues based on final_day_of_payment

### Requirement 2: Fix Staff Dues Payment Processing

**User Story:** As a staff member, I want to pay my dues using my wallet so that I can fulfill my payment obligations.

#### Acceptance Criteria

1. WHEN a staff member initiates a payment THEN the system SHALL verify sufficient wallet balance
2. WHEN processing a payment THEN the system SHALL create proper wallet transactions for both payer and recipient
3. WHEN a payment is successful THEN the system SHALL update the payment status in player_team_payment table
4. WHEN a payment fails THEN the system SHALL provide clear error messages and not modify any balances
5. WHEN a payment is completed THEN the system SHALL send appropriate notifications to both parties

### Requirement 3: Create Missing Routes and Controllers

**User Story:** As a developer, I want proper API endpoints for dues functionality so that the frontend can communicate with the backend effectively.

#### Acceptance Criteria

1. WHEN the application starts THEN the system SHALL have dedicated dues routes separate from payment routes
2. WHEN API endpoints are called THEN the system SHALL use proper authentication and authorization
3. WHEN handling dues requests THEN the system SHALL have dedicated controllers with proper error handling
4. WHEN processing payments THEN the system SHALL have endpoints for both individual and bulk payments
5. WHEN managing dues THEN the system SHALL support CRUD operations with proper validation

### Requirement 4: Fix Frontend Referee Dues Components

**User Story:** As a referee, I want the dues interface to work smoothly so that I can manage my payments without errors.

#### Acceptance Criteria

1. WHEN loading the dues page THEN the system SHALL display accurate summary cards with real referee dues data
2. WHEN viewing the referee dues tab THEN the system SHALL load appropriate data for referee payments
3. WHEN selecting dues for payment THEN the system SHALL enable bulk payment functionality
4. WHEN a payment is processed THEN the system SHALL update the UI in real-time
5. WHEN errors occur THEN the system SHALL display user-friendly error messages

### Requirement 6: Implement Real-time Status Updates

**User Story:** As a user, I want to see payment status updates immediately so that I know when my payments are processed.

#### Acceptance Criteria

1. WHEN a payment is initiated THEN the system SHALL show loading states and progress indicators
2. WHEN a payment is completed THEN the system SHALL update the dues status immediately
3. WHEN viewing dues lists THEN the system SHALL refresh data automatically after payments
4. WHEN payment status changes THEN the system SHALL update summary cards in real-time
5. WHEN errors occur during payment THEN the system SHALL provide immediate feedback

### Requirement 7: Fix Database Integration Issues

**User Story:** As a system administrator, I want the dues system to work with the existing database schema so that data integrity is maintained.

#### Acceptance Criteria

1. WHEN querying dues data THEN the system SHALL use correct table joins and relationships
2. WHEN updating payment status THEN the system SHALL maintain referential integrity
3. WHEN processing transactions THEN the system SHALL use database transactions for consistency
4. WHEN handling concurrent payments THEN the system SHALL prevent race conditions
5. WHEN storing payment records THEN the system SHALL include all required metadata

### Requirement 8: Implement Comprehensive Error Handling

**User Story:** As a user, I want clear error messages when something goes wrong so that I can understand and resolve issues.

#### Acceptance Criteria

1. WHEN insufficient funds are available THEN the system SHALL display a clear insufficient balance message
2. WHEN network errors occur THEN the system SHALL provide retry options and fallback behavior
3. WHEN validation fails THEN the system SHALL highlight specific fields with clear error messages
4. WHEN server errors occur THEN the system SHALL log errors properly and show user-friendly messages
5. WHEN payment processing fails THEN the system SHALL rollback any partial changes

### Requirement 9: Add Payment Notifications System

**User Story:** As a user, I want to receive notifications about payment status so that I stay informed about my dues.

#### Acceptance Criteria

1. WHEN a payment is successful THEN the system SHALL send success notifications to the payer
2. WHEN a payment fails THEN the system SHALL send failure notifications with reason
3. WHEN dues are overdue THEN the system SHALL send reminder notifications
4. WHEN bulk payments are processed THEN the system SHALL provide summary notifications
5. WHEN payment status changes THEN the system SHALL update notification badges

### Requirement 10: Implement Bulk Payment Functionality

**User Story:** As a user, I want to pay multiple dues at once so that I can efficiently manage my payments.

#### Acceptance Criteria

1. WHEN selecting multiple dues THEN the system SHALL enable bulk payment options
2. WHEN processing bulk payments THEN the system SHALL handle individual payment failures gracefully
3. WHEN bulk payment completes THEN the system SHALL provide detailed results for each payment
4. WHEN bulk payment fails THEN the system SHALL show which payments succeeded and which failed
5. WHEN calculating bulk totals THEN the system SHALL display accurate total amounts and fees