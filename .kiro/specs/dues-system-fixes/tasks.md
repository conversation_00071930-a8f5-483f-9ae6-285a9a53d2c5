# Implementation Plan

- [x] 1. Create dedicated dues routes and controller following staff pattern
  - Create new duesRoutes.ts file using same pattern as staffRoutes.ts with authenticateTokenAndSession middleware
  - Implement duesController.ts following staffController.ts pattern with team_connect_user_id extraction
  - Update app.ts to use dedicated dues routes instead of payment routes
  - Use same response patterns (sendSuccess, sendError, sendUserSuccess, sendUserError)
  - _Requirements: 3.1, 3.2, 3.3_

- [x] 2. Fix and enhance dues service backend logic following staff service patterns
  - [x] 2.1 Fix getStaffDues function with proper database queries
    - Use executeSecondaryQuery pattern like getCurrentUserStaff function
    - Correct the SQL query to properly join all required tables (match_staff, users, matches, etc.)
    - Fix amount calculation logic based on wage_unit_id using CASE statements
    - Implement proper status determination (Paid/Pending/Overdue) with date comparisons
    - Add proper filtering and pagination support using query parameters
    - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

  - [x] 2.2 Fix getStaffDuesSummary function for accurate calculations
    - Follow getUserDetails pattern for data aggregation
    - Implement correct summary calculations for totalDues, paidDues, pendingDues, overdueDues
    - Fix totalAmountDue and totalAmountPaid calculations using array filtering and reduce
    - Add recentPayments functionality similar to getUserTransactionHistory
    - _Requirements: 1.1, 1.4, 1.5_

  - [x] 2.3 Implement robust payment processing following wallet patterns
    - Fix payStaffDueWithWallet function using createWalletTransaction pattern from staffService
    - Add wallet balance verification using getUserMasterWallet function
    - Implement proper database transaction using executeUpdate for multiple operations
    - Add payment status updates in player_team_payment table with proper error handling
    - _Requirements: 2.1, 2.2, 2.3, 2.4, 7.3_

- [ ] 3. Implement bulk payment functionality following staff transfer patterns
  - [ ] 3.1 Create bulk payment processing logic
    - Implement payBulkStaffDues function similar to transferAmountToStaff pattern
    - Add individual payment validation using PIN verification like staff transfers
    - Handle partial success scenarios gracefully with detailed error tracking
    - Use same transaction patterns as staff bank transfers
    - _Requirements: 10.1, 10.2, 10.3, 10.4, 10.5_

  - [ ] 3.2 Add bulk payment controller endpoint following staff controller pattern
    - Create POST /api/dues/pay-bulk endpoint using same auth pattern as staff routes
    - Extract team_connect_user_id from authenticated request like staff controllers
    - Implement proper request validation following staff controller validation patterns
    - Use sendUserSuccess/sendUserError response patterns for bulk operations
    - _Requirements: 10.1, 10.2, 10.3_

- [ ] 4. Fix frontend dues service integration following staff service patterns
  - [ ] 4.1 Update dues service API calls to match staff API patterns
    - Fix getDues function to use /api/dues/staff-dues endpoint with proper query parameters
    - Update getDuesSummary to use /api/dues/staff-dues-summary endpoint
    - Implement proper error handling and fallback mechanisms following staff service error patterns
    - Use same API response structure as staff endpoints (success/error format)
    - _Requirements: 4.1, 4.4, 8.2, 8.4_

  - [ ] 4.2 Implement enhanced payment functions following staff transfer patterns
    - Fix payDueWithWallet function to use /api/dues/pay-due endpoint with PIN validation
    - Add payMultipleDuesWithWallet for bulk payments using /api/dues/pay-bulk endpoint
    - Implement real-time status checking functionality similar to staff transfer status
    - Use same request/response patterns as staff bank transfers
    - _Requirements: 2.1, 2.4, 6.1, 6.2, 6.3_

- [ ] 5. Enhance frontend components for better UX
  - [ ] 5.1 Fix MyDuesTable component
    - Update to use real API data instead of mock data
    - Fix pagination and filtering functionality
    - Implement proper loading states and error handling
    - Add bulk selection and payment capabilities
    - _Requirements: 4.1, 4.3, 4.4, 4.5_

  - [ ] 5.2 Update Dues page component
    - Remove platform dues tab and focus only on staff dues
    - Fix summary card data binding with real API data
    - Implement proper state management for payments
    - Add real-time updates after payment completion
    - _Requirements: 4.1, 4.2, 4.4, 6.4_

  - [ ] 5.3 Enhance DuePaymentModal component
    - Add proper PIN validation and wallet balance checking
    - Implement payment progress indicators
    - Add comprehensive error handling and user feedback
    - _Requirements: 6.1, 8.1, 8.3_

- [ ] 6. Implement notification system
  - [ ] 6.1 Create notification service functions
    - Implement sendPaymentSuccessNotification function
    - Add sendPaymentFailedNotification with error details
    - Create sendDueReminderNotification for overdue payments
    - _Requirements: 9.1, 9.2, 9.3_

  - [ ] 6.2 Integrate notifications with payment processing
    - Add notification calls to payment success/failure flows
    - Implement notification for bulk payment results
    - Add notification badge updates in frontend
    - _Requirements: 9.1, 9.2, 9.4, 9.5_

- [ ] 7. Add comprehensive error handling
  - [ ] 7.1 Implement backend error handling
    - Add proper error categories and response formats
    - Implement database transaction rollback on errors
    - Add logging for all error scenarios
    - _Requirements: 8.4, 8.5, 7.4_

  - [ ] 7.2 Enhance frontend error handling
    - Add user-friendly error messages for all scenarios
    - Implement retry mechanisms for network errors
    - Add validation error highlighting
    - _Requirements: 8.1, 8.2, 8.3_

- [ ] 8. Implement real-time status updates
  - [ ] 8.1 Add payment status tracking
    - Implement checkPaymentStatus function
    - Add refreshDueStatus functionality
    - Create real-time monitoring for payment completion
    - _Requirements: 6.1, 6.2, 6.3_

  - [ ] 8.2 Update UI components for real-time updates
    - Add automatic data refresh after payments
    - Implement optimistic UI updates
    - Add loading states and progress indicators
    - _Requirements: 6.1, 6.4, 4.4_

- [ ] 9. Fix database integration issues
  - [ ] 9.1 Optimize database queries
    - Add proper indexes for frequently queried columns
    - Optimize JOIN operations in dues queries
    - Implement query result caching where appropriate
    - _Requirements: 7.1, 7.2_

  - [ ] 9.2 Ensure transaction integrity
    - Implement proper database transactions for payments
    - Add concurrent payment handling
    - Ensure referential integrity in all operations
    - _Requirements: 7.3, 7.4, 7.5_

- [ ] 10. Add comprehensive testing
  - [ ] 10.1 Create unit tests for service functions
    - Test dues calculation logic
    - Test payment processing workflows
    - Test error handling scenarios
    - _Requirements: All requirements validation_

  - [ ] 10.2 Add integration tests for API endpoints
    - Test full request-response cycles
    - Test authentication and authorization
    - Test error propagation and handling
    - _Requirements: All requirements validation_

  - [ ] 10.3 Create frontend component tests
    - Test component rendering and interaction
    - Test API integration and state management
    - Test user workflow validation
    - _Requirements: All requirements validation_

- [ ] 11. Final integration and testing
  - [ ] 11.1 End-to-end testing
    - Test complete dues viewing workflow
    - Test individual payment processing
    - Test bulk payment functionality
    - Verify real-time updates and notifications
    - _Requirements: All requirements validation_

  - [ ] 11.2 Performance optimization
    - Optimize database queries and API responses
    - Implement frontend performance improvements
    - Add monitoring and logging
    - _Requirements: All requirements validation_