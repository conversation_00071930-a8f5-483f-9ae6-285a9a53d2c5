# Design Document

## Overview

This design document outlines the comprehensive solution for fixing the referee dues system in the pay-connect application. The current system has broken API endpoints, incomplete payment processing, missing routes, and frontend issues that prevent referees from properly viewing and paying their dues. This design provides a complete solution to make the referee dues system fully functional.

## Architecture

### System Components

```mermaid
graph TB
    A[Frontend - Dues Page] --> B[Dues Service Layer]
    B --> C[API Routes]
    C --> D[Dues Controller]
    D --> E[Dues Service]
    E --> F[Database Layer]
    E --> G[Wallet Service]
    E --> H[Notification Service]
    
    F --> I[(Primary DB - Wallet)]
    F --> J[(Secondary DB - Dues)]
    
    G --> K[Payment Processing]
    H --> L[Email Notifications]
```

### Data Flow

1. **Dues Retrieval Flow:**
   - Frontend requests dues data
   - API validates authentication
   - Service queries secondary database for referee assignments
   - System calculates amounts based on wage settings
   - Response includes status, amounts, and metadata

2. **Payment Processing Flow:**
   - User initiates payment with PIN
   - System validates PIN and wallet balance
   - Creates wallet transactions in primary database
   - Updates payment status in secondary database
   - Sends notifications to relevant parties

## Components and Interfaces

### Backend Components

#### 1. Dues Routes (`/server/src/routes/duesRoutes.ts`)

```typescript
interface DuesRoutes {
  // Staff dues endpoints
  GET /api/dues/staff-dues
  GET /api/dues/staff-dues-summary
  POST /api/dues/pay-due
  POST /api/dues/pay-bulk-dues
  
  // Utility endpoints
  GET /api/dues/game-seasons
  GET /api/dues/team/:id
}
```

#### 2. Dues Controller (`/server/src/controllers/duesController.ts`)

```typescript
interface DuesController {
  getStaffDues(req: AuthenticatedRequest, res: Response): Promise<void>
  getStaffDuesSummary(req: AuthenticatedRequest, res: Response): Promise<void>
  payDue(req: AuthenticatedRequest, res: Response): Promise<void>
  payBulkDues(req: AuthenticatedRequest, res: Response): Promise<void>
  getGameSeasons(req: AuthenticatedRequest, res: Response): Promise<void>
  getTeamDetail(req: AuthenticatedRequest, res: Response): Promise<void>
}
```

#### 3. Enhanced Dues Service (`/server/src/services/duesService.ts`)

```typescript
interface DuesService {
  // Core dues operations
  getStaffDues(userId: string, filters: DuesFilter): Promise<DuesResponse>
  getStaffDuesSummary(userId: string): Promise<DuesSummary>
  
  // Payment operations
  processStaffDuePayment(request: PaymentRequest): Promise<PaymentResponse>
  processBulkDuePayments(requests: BulkPaymentRequest): Promise<BulkPaymentResponse>
  
  // Utility operations
  getGameSeasons(userId: string): Promise<GameSeason[]>
  getTeamDetail(teamId: string): Promise<TeamDetail>
}
```

### Frontend Components

#### 1. Enhanced Dues Page (`/pay-connect/src/pages/Dues.tsx`)

```typescript
interface DuesPageState {
  activeTab: 'staff' // Only staff dues now
  summary: StaffDuesSummary
  loading: boolean
  error: string | null
  selectedDues: Set<string>
  paymentModal: PaymentModalState
}
```

#### 2. Staff Dues Table (`/pay-connect/src/components/Dues/MyDuesTable.tsx`)

```typescript
interface MyDuesTableProps {
  search: string
  statusFilter: string
  onDataChange: () => void
  onPayNow: (due: Due) => void
  onBulkPay: (dues: Due[]) => void
}
```

#### 3. Payment Modal (`/pay-connect/src/components/Dues/DuePaymentModal.tsx`)

```typescript
interface DuePaymentModalProps {
  isOpen: boolean
  onClose: () => void
  dueId: string
  dueName: string
  prefilledAmount: number
  onSuccess: () => void
}
```

## Data Models

### Database Schema Updates

#### 1. Enhanced Dues Query Structure

```sql
-- Main dues query with proper joins and calculations
SELECT 
  u.id,
  u.firstname,
  u.email,
  u.contact,
  u.profile_pic as avatar_url,
  g.game_title,
  s.season_name,
  s.id as season_id,
  g.id as game_id,
  us.match_id,
  
  -- Amount calculation based on wage settings
  CASE 
    WHEN pw.wage_unit_id = 1 THEN pw.amount
    WHEN pw.wage_unit_id IS NOT NULL THEN pw.amount * IFNULL(pw.unit_count, 0)
    ELSE 100.00
  END AS amount,
  
  -- Status determination
  CASE 
    WHEN ptp.payment_status = 'paid' THEN 'Paid'
    WHEN sp.final_day_of_payment < CURDATE() THEN 'Overdue'
    ELSE 'Pending'
  END AS status,
  
  sp.final_day_of_payment AS due_date,
  ptp.payment_date,
  ptp.transaction_reference,
  g.user_id as organizer_id
  
FROM match_staff us
JOIN users u ON u.id = us.user_id 
JOIN user_roles ur ON ur.user_id = u.id AND ur.role_id = 5
JOIN matches m ON m.id = us.match_id
JOIN game_groups_matches ggm ON m.game_group_id = ggm.id
JOIN seasons s ON s.id = ggm.season_id
JOIN games g ON g.id = ggm.game_id
JOIN season_plans sp ON sp.season_id = s.id
LEFT JOIN paywages pw ON pw.user_id = u.id
LEFT JOIN player_team_payment ptp ON ptp.player_id = u.id AND ptp.season_id = s.id
```

#### 2. Payment Transaction Structure

```sql
-- Wallet transaction for payer (debit)
INSERT INTO tbl_wallet_transactions (
  user_id, type, amount, reference_id, 
  payment_provider, description, status_id, 
  created_at, meta_data
) VALUES (?, 'debit', ?, ?, 'wallet_transfer', ?, '1', NOW(), ?)

-- Wallet transaction for recipient (credit)
INSERT INTO tbl_wallet_transactions (
  user_id, type, amount, reference_id, 
  payment_provider, description, status_id, 
  created_at, meta_data
) VALUES (?, 'credit', ?, ?, 'wallet_transfer', ?, '1', NOW(), ?)

-- Payment status update
INSERT INTO player_team_payment (
  player_id, season_id, team_id, player_charged_amount, 
  payment_date, payment_status, transaction_reference
) VALUES (?, ?, ?, ?, NOW(), 'paid', ?)
ON DUPLICATE KEY UPDATE 
  payment_status = 'paid', 
  payment_date = NOW(), 
  transaction_reference = ?
```

### TypeScript Interfaces

#### 1. Core Due Interface

```typescript
interface Due {
  id: string
  firstname: string
  email: string
  contact?: string
  avatar_url?: string
  game_title: string
  season_name: string
  amount: number
  due_date: string
  status: 'Paid' | 'Pending' | 'Overdue'
  user_id: string
  season_id: string
  game_id: string
  match_id: string
  organizer_id: string
  payment_date?: string
  transaction_reference?: string
  created_at: string
}
```

#### 2. Payment Request Interface

```typescript
interface PaymentRequest {
  dueId: string
  amount: number
  payerUserId: string
  recipientUserId: string
  pin: string
  description?: string
}

interface BulkPaymentRequest {
  payments: PaymentRequest[]
  pin: string
}
```

#### 3. Response Interfaces

```typescript
interface DuesResponse {
  data: Due[]
  totalRecords: number
  page: number
  pageSize: number
}

interface DuesSummary {
  totalDues: number
  paidDues: number
  pendingDues: number
  overdueDues: number
  totalAmountDue: number
  totalAmountPaid: number
  recentPayments: Due[]
}

interface PaymentResponse {
  success: boolean
  message: string
  transactionId?: string
  newBalance?: number
  paymentDetails?: {
    amount: number
    recipient: string
    timestamp: string
    reference: string
  }
}
```

## Error Handling

### Error Categories

1. **Authentication Errors**
   - Missing or invalid user ID
   - Invalid session tokens
   - Unauthorized access attempts

2. **Validation Errors**
   - Invalid PIN format or verification
   - Missing required fields
   - Invalid amount values

3. **Business Logic Errors**
   - Insufficient wallet balance
   - Due already paid
   - Invalid due status for payment

4. **Database Errors**
   - Connection failures
   - Query execution errors
   - Transaction rollback scenarios

5. **Payment Processing Errors**
   - Wallet service failures
   - Concurrent payment conflicts
   - Notification delivery failures

### Error Response Format

```typescript
interface ErrorResponse {
  success: false
  error: {
    code: string
    category: 'AUTH' | 'VALIDATION' | 'BUSINESS' | 'DATABASE' | 'PAYMENT'
    message: string
    details?: any
  }
  timestamp: string
  requestId: string
}
```

## Testing Strategy

### Unit Tests

1. **Service Layer Tests**
   - Dues calculation logic
   - Payment processing workflows
   - Status determination algorithms
   - Error handling scenarios

2. **Controller Tests**
   - Request validation
   - Response formatting
   - Authentication checks
   - Error response handling

3. **Database Tests**
   - Query correctness
   - Transaction integrity
   - Concurrent access handling
   - Data consistency checks

### Integration Tests

1. **API Endpoint Tests**
   - Full request-response cycles
   - Authentication middleware
   - Error propagation
   - Performance benchmarks

2. **Payment Flow Tests**
   - End-to-end payment processing
   - Wallet balance updates
   - Status synchronization
   - Notification delivery

3. **Frontend Integration Tests**
   - Component interaction
   - State management
   - API communication
   - User workflow validation

### Test Data Setup

```typescript
interface TestDataSetup {
  users: TestUser[]
  games: TestGame[]
  seasons: TestSeason[]
  matches: TestMatch[]
  wages: TestWage[]
  wallets: TestWallet[]
}
```

## Performance Considerations

### Database Optimization

1. **Query Optimization**
   - Proper indexing on frequently queried columns
   - Efficient JOIN operations
   - Pagination for large result sets
   - Query result caching

2. **Connection Management**
   - Connection pooling for both databases
   - Proper connection cleanup
   - Timeout handling
   - Retry mechanisms

### Frontend Performance

1. **Data Loading**
   - Lazy loading for large dues lists
   - Debounced search functionality
   - Optimistic UI updates
   - Background data refresh

2. **State Management**
   - Efficient state updates
   - Memoized calculations
   - Component optimization
   - Memory leak prevention

## Security Measures

### Authentication & Authorization

1. **Session Management**
   - Token validation on all endpoints
   - Session timeout handling
   - Concurrent session limits
   - Secure token storage

2. **PIN Verification**
   - Secure PIN hashing
   - Rate limiting for PIN attempts
   - Account lockout mechanisms
   - Audit logging

### Data Protection

1. **Input Validation**
   - SQL injection prevention
   - XSS protection
   - Parameter sanitization
   - Type validation

2. **Transaction Security**
   - Atomic operations
   - Rollback mechanisms
   - Audit trails
   - Fraud detection

## Deployment Strategy

### Database Changes

1. **Migration Scripts**
   - Index creation for performance
   - Data consistency checks
   - Backup procedures
   - Rollback plans

2. **Configuration Updates**
   - Environment variables
   - Database connections
   - API endpoints
   - Feature flags

### Application Deployment

1. **Backend Deployment**
   - Service updates
   - Route registration
   - Dependency updates
   - Health checks

2. **Frontend Deployment**
   - Component updates
   - Service integration
   - Build optimization
   - Cache invalidation

## Monitoring and Logging

### Application Monitoring

1. **Performance Metrics**
   - API response times
   - Database query performance
   - Payment processing times
   - Error rates

2. **Business Metrics**
   - Payment success rates
   - User engagement
   - System usage patterns
   - Revenue tracking

### Logging Strategy

1. **Structured Logging**
   - Request/response logging
   - Error tracking
   - Performance monitoring
   - Security events

2. **Log Analysis**
   - Error pattern detection
   - Performance bottlenecks
   - User behavior analysis
   - System health monitoring