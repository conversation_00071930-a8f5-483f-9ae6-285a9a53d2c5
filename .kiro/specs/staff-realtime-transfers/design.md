# Design Document: Staff Real-Time Transfers (Wallet-Only)

## Overview

This design document outlines the implementation of wallet-only real-time transfers for staff payments using Plaid's ACH transfer API. The feature will enable organizers to pay their staff members directly from their wallet balance only, with real-time status updates and proper error handling. Bank transfer functionality has been removed to centralize all payments through the wallet system.

## Architecture

The wallet-only staff transfers feature will be built on top of the existing payment infrastructure, leveraging Plaid's Transfer API for ACH transfers from the platform's account to staff bank accounts. The system will use the following components:

1. **Frontend Components**:
   - Enhanced `StaffBankTransferModal` component for initiating wallet-based transfers
   - Status tracking UI for monitoring transfer progress
   - ACH transfer method selection interface
   - Wallet balance validation UI

2. **Backend Services**:
   - `plaidTransferService.ts`: Core service for handling Plaid transfers
   - `staffService.ts`: Service for staff-related operations
   - `walletService.ts`: Service for wallet operations and balance management

3. **Database Tables**:
   - `tbl_wallet_transactions`: For tracking wallet transactions
   - `tbl_staff_bank_accounts`: For storing staff bank account information
   - `tbl_platform_bank_accounts`: For platform-level bank accounts used for transfers

4. **External APIs**:
   - Plaid Transfer API: For initiating and tracking ACH transfers from platform account

## Components and Interfaces

### 1. Enhanced plaidTransferService

We'll add a new function `createStaffBankTransfer` to the `plaidTransferService.ts` file that will handle the complete flow of staff payments:

```typescript
export async function createStaffBankTransfer(
  organizerId: number,
  staffId: number,
  amount: number,
  description: string,
  staffBankAccountId: string,
  paymentMethodType: string = 'ach',
  pin?: string,
  paymentSource: 'wallet' | 'bank' = 'wallet',
  bankAccountId?: string
): Promise<PlaidTransferResult> {
  // Implementation details
}
```

This function will:
1. Validate the payment source (wallet or bank)
2. Verify wallet PIN or bank account details
3. Retrieve staff bank account information
4. Create a transfer using Plaid's Transfer API
5. Update transaction records
6. Return transfer status

### 2. Staff Bank Transfer Modal

Enhance the existing `StaffBankTransferModal` component to support:
- Payment source selection (wallet or bank)
- Payment method selection (standard ACH, same-day ACH)
- Real-time status updates
- Error handling and display

### 3. Transaction Status Tracking

Implement a status tracking system that:
- Displays initial "Pending" status
- Polls for status updates from Plaid
- Updates UI when status changes
- Provides detailed error information if transfer fails

## Data Models

### 1. Enhanced Wallet Transaction

```typescript
interface WalletTransaction {
  id: number;
  user_id: number;
  type: 'credit' | 'debit';
  amount: number;
  reference_id: string;
  payment_provider: string;
  description: string;
  status_id: number; // 1=completed, 2=pending, 3=failed
  created_at: Date;
  last_updated: Date;
  meta_data: {
    staffId?: number;
    staffName?: string;
    staffEmail?: string;
    bankAccount?: string; // Last 4 digits
    bankName?: string;
    accountType?: string;
    accountHolderName?: string;
    organizerId?: number;
    transferType?: string;
    paymentSource?: 'wallet' | 'bank';
    paymentMethodType?: string;
    plaid_status?: string;
    plaid_transfer_id?: string;
    failure_reason?: string;
    description?: string;
  };
}
```

### 2. Staff Bank Account

```typescript
interface StaffBankAccount {
  id: number;
  staff_id: number;
  organizer_id: number;
  bank_name: string;
  account_number: string;
  routing_number: string;
  account_type: 'checking' | 'savings';
  account_holder_name: string;
  status: 'active' | 'inactive';
  created_at: Date;
}
```

### 3. Payment Method

```typescript
interface PaymentMethod {
  id: number;
  method_id: string;
  name: string;
  description: string;
  processing_time: string;
  fee: number;
  type: 'withdrawal' | 'deposit' | 'transfer';
  is_active: boolean;
}
```

## Error Handling

The system will handle various error scenarios:

1. **Wallet Verification Errors**:
   - Insufficient balance
   - Invalid PIN
   - Wallet not found

2. **Bank Account Errors**:
   - Bank account not found
   - Bank account not properly linked with Plaid
   - Insufficient funds in bank account

3. **Plaid Transfer Errors**:
   - Authorization failed
   - Transfer creation failed
   - Transfer status updates failed

4. **Staff Bank Account Errors**:
   - Staff bank account not found
   - Invalid bank account information

Each error will have a specific error code and user-friendly message that will be displayed to the user.

## Testing Strategy

1. **Unit Tests**:
   - Test `createStaffBankTransfer` function with various inputs
   - Test wallet PIN verification
   - Test bank account validation
   - Test error handling

2. **Integration Tests**:
   - Test the complete flow from frontend to backend
   - Test interaction with Plaid API using sandbox environment
   - Test transaction status updates

3. **UI Tests**:
   - Test payment source selection
   - Test payment method selection
   - Test error message display
   - Test status updates in UI

4. **Edge Cases**:
   - Test with very small and very large amounts
   - Test with invalid bank account information
   - Test with network failures
   - Test with Plaid API timeouts

## Security Considerations

1. **PIN Security**:
   - PIN should never be stored in plaintext
   - PIN should be transmitted securely (HTTPS)
   - PIN verification should use secure comparison methods

2. **Bank Account Information**:
   - Bank account numbers should be stored securely
   - Only the last 4 digits should be displayed in the UI
   - All sensitive data should be encrypted at rest

3. **Transfer Authorization**:
   - Only authorized users should be able to initiate transfers
   - Transfers should be logged for audit purposes
   - Large transfers may require additional verification

## Implementation Plan

1. Enhance `plaidTransferService.ts` with `createStaffBankTransfer` function
2. Update `staffController.ts` to use the new function
3. Enhance `StaffBankTransferModal` component
4. Implement status tracking UI
5. Add payment method selection
6. Implement error handling and display
7. Add unit and integration tests
8. Deploy and monitor

## Future Enhancements

1. **Batch Transfers**: Support for paying multiple staff members in a single operation
2. **Scheduled Transfers**: Support for scheduling recurring payments
3. **Transfer Limits**: Configurable limits for transfer amounts
4. **Advanced Notifications**: Email and SMS notifications for transfer status updates
5. **Transfer Analytics**: Reporting and analytics for staff payments