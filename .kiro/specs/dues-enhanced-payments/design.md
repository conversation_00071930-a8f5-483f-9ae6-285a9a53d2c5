# Design Document: Enhanced Dues Payment System

## Overview

This design document outlines the enhancement of the dues payment system to match the functionality and reliability of the staff payment system. The enhanced system will support flexible payment amounts, comprehensive transaction details, bank account management, and real-time status updates while leveraging the same proven payment infrastructure used for staff payments.

## Architecture

The enhanced dues payment system will be built by extending the existing dues payment infrastructure and integrating it with the staff payment APIs and services. The system will maintain consistency with the staff payment flow while adapting to the specific needs of dues payments.

### Key Components:

1. **Frontend Components**:
   - Enhanced `PaymentOffcanvas` component with flexible amount input
   - Bank account management interface similar to staff payments
   - Payment method selection with processing times and fees
   - Real-time status tracking and transaction details
   - Comprehensive payment breakdown display

2. **Backend Services**:
   - Integration with existing `plaidTransferService.ts` for ACH transfers
   - Enhanced `duesService.ts` with flexible payment processing
   - Shared `walletService.ts` for wallet operations
   - Unified transaction models and status tracking

3. **Database Integration**:
   - Reuse existing `tbl_wallet_transactions` table structure
   - Extend dues-related tables to support flexible amounts
   - Integrate with staff payment method and bank account tables

4. **API Integration**:
   - Leverage existing Plaid Transfer API integration
   - Use same payment method and bank account management APIs
   - Unified transaction status tracking and monitoring

## Components and Interfaces

### 1. Enhanced PaymentOffcanvas Component

The existing `PaymentOffcanvas` component will be enhanced to support:

```typescript
interface EnhancedPaymentOffcanvasProps {
  isOpen: boolean;
  onClose: () => void;
  due: Due | null;
  onPaymentSuccess: () => void;
  allowCustomAmount?: boolean; // New prop to enable custom amounts
  showBankPaymentOption?: boolean; // New prop to enable bank payments
}

interface PaymentFormData {
  amount: number; // Editable amount (prefilled with due amount)
  paymentSource: 'wallet' | 'bank';
  selectedBankAccount?: BankAccount;
  selectedPaymentMethod?: PaymentMethod;
  pin: string;
  description?: string;
}
```

Key enhancements:
- Editable amount field with validation
- Payment source selection (wallet/bank)
- Bank account selection dropdown
- Payment method selection with fees
- Real-time fee calculation and total display
- Enhanced transaction status tracking

### 2. Bank Account Management Integration

Reuse the existing staff bank account management system:

```typescript
// Extend existing interfaces for dues context
interface DuesBankAccount extends StaffBankAccount {
  userId: string; // Link to user instead of staff
  isVerifiedForDues: boolean;
}

// Reuse existing bank account management functions
import {
  addStaffBankAccount,
  getStaffBankAccountsById,
  setPrimaryBankAccount,
  updateBankAccountStatus
} from '../services/staff';

// Adapt for dues context
export const addUserBankAccount = (userId: string, bankData: BankAccountData) => 
  addStaffBankAccount(userId, bankData);

export const getUserBankAccounts = (userId: string) => 
  getStaffBankAccountsById(userId);
```

### 3. Enhanced Payment Processing

Integrate with existing payment infrastructure:

```typescript
// Enhanced dues payment function
export async function payDueWithFlexibleAmount(
  dueId: string,
  userId: string,
  amount: number, // Custom amount (can be different from due amount)
  paymentSource: 'wallet' | 'bank',
  paymentMethodId?: string,
  bankAccountId?: string,
  pin?: string,
  description?: string
): Promise<PaymentResponse> {
  // Leverage existing plaidTransferService for consistency
  if (paymentSource === 'bank') {
    return await createStaffBankTransfer(
      userId,
      'dues_recipient', // Special recipient for dues
      amount,
      description || `Dues payment for ${dueId}`,
      bankAccountId!,
      paymentMethodId || 'ach',
      pin,
      paymentSource,
      bankAccountId
    );
  } else {
    // Use existing wallet payment logic
    return await payDueWithWallet(dueId, userId, amount, pin!, description);
  }
}
```

### 4. Transaction Model Integration

Extend existing transaction models to support dues:

```typescript
interface EnhancedDuesTransaction {
  id: number;
  user_id: number;
  due_id: string;
  type: 'dues_payment';
  amount: number;
  original_due_amount: number; // Track original vs paid amount
  payment_source: 'wallet' | 'bank';
  payment_method_id?: string;
  bank_account_id?: string;
  reference_id: string;
  payment_provider: string;
  description: string;
  status_id: number;
  created_at: Date;
  last_updated: Date;
  meta_data: {
    dueId: string;
    originalAmount: number;
    paidAmount: number;
    remainingAmount: number;
    paymentBreakdown: Array<{
      label: string;
      value: number;
    }>;
    bankAccount?: string;
    bankName?: string;
    paymentMethodType?: string;
    plaid_status?: string;
    plaid_transfer_id?: string;
    failure_reason?: string;
  };
}
```

## Data Models

### 1. Enhanced Due Model

```typescript
interface EnhancedDue extends Due {
  // Flexible payment tracking
  original_amount: number;
  paid_amount: number;
  remaining_amount: number;
  allows_partial_payment: boolean;
  allows_overpayment: boolean;
  
  // Payment history
  payment_history: Array<{
    id: string;
    amount: number;
    payment_date: string;
    payment_method: string;
    status: string;
    transaction_id: string;
  }>;
  
  // Bank account integration
  user_bank_accounts?: BankAccount[];
  preferred_payment_method?: string;
}
```

### 2. Payment Method Integration

Reuse existing payment method models:

```typescript
interface PaymentMethod {
  id: number;
  method_id: string;
  name: string;
  description: string;
  processing_time: string;
  fee_type: 'fixed' | 'percentage' | 'hybrid';
  min_fee?: number;
  max_fee?: number;
  percentage_fee?: number;
  type: 'withdrawal' | 'deposit' | 'transfer';
  is_active: boolean;
  applicable_to_dues: boolean; // New field for dues compatibility
}
```

### 3. Bank Account Model

Extend existing bank account model for dues:

```typescript
interface UserBankAccount extends StaffBankAccount {
  user_id: number; // Instead of staff_id
  is_verified_for_dues: boolean;
  dues_payment_enabled: boolean;
  last_used_for_dues?: Date;
}
```

## Error Handling

The system will use the same error handling patterns as staff payments:

1. **Amount Validation Errors**:
   - Invalid amount (negative, zero, or too large)
   - Insufficient wallet balance
   - Amount exceeds payment limits

2. **Payment Source Errors**:
   - Bank account not found or inactive
   - Payment method not supported for dues
   - Bank account verification failed

3. **Processing Errors**:
   - PIN verification failed
   - Plaid transfer creation failed
   - Network or API timeouts

4. **Status Update Errors**:
   - Failed to update due status
   - Transaction record creation failed
   - Notification delivery failed

## Testing Strategy

1. **Unit Tests**:
   - Test flexible amount validation and calculation
   - Test payment method selection and fee calculation
   - Test bank account integration functions
   - Test transaction model extensions

2. **Integration Tests**:
   - Test complete payment flow with custom amounts
   - Test bank account management integration
   - Test real-time status updates
   - Test payment method switching

3. **UI Tests**:
   - Test amount input validation and formatting
   - Test payment source selection
   - Test bank account selection and management
   - Test status tracking display

4. **Edge Cases**:
   - Test partial payments and remaining balance calculation
   - Test overpayments and credit handling
   - Test payment method fee calculations
   - Test concurrent payment attempts

## Security Considerations

1. **Amount Validation**:
   - Server-side validation of all payment amounts
   - Prevention of negative or excessive amounts
   - Audit logging of amount changes

2. **Bank Account Security**:
   - Reuse existing bank account encryption
   - Secure storage of account information
   - Access control for bank account management

3. **Transaction Security**:
   - PIN verification for all payment sources
   - Secure transmission of payment data
   - Transaction audit trails

## Implementation Plan

### Phase 1: Core Infrastructure
1. Extend dues models to support flexible amounts
2. Integrate with existing payment method APIs
3. Adapt bank account management for dues context
4. Update database schema for enhanced tracking

### Phase 2: Frontend Enhancement
1. Enhance PaymentOffcanvas with amount editing
2. Add payment source selection interface
3. Integrate bank account management UI
4. Implement real-time status tracking

### Phase 3: Payment Processing
1. Integrate with plaidTransferService for bank payments
2. Enhance wallet payment processing
3. Implement flexible amount handling
4. Add comprehensive transaction logging

### Phase 4: Testing and Deployment
1. Comprehensive testing of all payment flows
2. Performance testing with various amounts
3. Security testing of enhanced features
4. Gradual rollout with monitoring

## Future Enhancements

1. **Recurring Payments**: Support for scheduled dues payments
2. **Payment Plans**: Split large dues into installments
3. **Bulk Payments**: Pay multiple dues in single transaction
4. **Advanced Notifications**: SMS and email payment confirmations
5. **Payment Analytics**: Detailed reporting on payment patterns
6. **Mobile Optimization**: Enhanced mobile payment experience