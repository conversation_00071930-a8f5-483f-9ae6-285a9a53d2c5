import { Request, Response } from 'express';
import {
  getPaymentMethods,
  getPaymentMethodById,
  getWithdrawalSteps,
  getWithdrawalProgress,
  getWithdrawalProgressWithTransaction,
  processSuccessfulWithdrawal,
  processFailedWithdrawal,
  processWithdrawalReversal,
  processWithdrawalCancellation,
  processWithdrawalReview,
  addWithdrawalStep
} from '../services/paymentMethodService';
import { sendSuccess, sendError, sendUnauthorized } from '../utils/response';
import logger from '../utils/logger';

/**
 * Get all payment methods
 */
export const getPaymentMethodsController = async (req: Request, res: Response) => {
  try {
    const { type } = req.query;
    const methodType = type as 'withdrawal' | 'deposit' | 'both' | undefined;

    const methods = await getPaymentMethods(methodType);

    sendSuccess(res, {
      payment_methods: methods,
      total: methods.length
    }, 'Payment methods retrieved successfully');

  } catch (error) {
    logger.error('Error getting payment methods', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    sendError(res, 'Failed to get payment methods', 500);
  }
};

/**
 * Get payment method by ID
 */
export const getPaymentMethodByIdController = async (req: Request, res: Response) => {
  try {
    const { methodId } = req.params;

    if (!methodId) {
      return sendError(res, 'Method ID is required', 400);
    }

    const method = await getPaymentMethodById(methodId);

    if (!method) {
      return sendError(res, 'Payment method not found', 404);
    }

    sendSuccess(res, {
      payment_method: method
    }, 'Payment method retrieved successfully');

  } catch (error) {
    logger.error('Error getting payment method by ID', {
      error: error instanceof Error ? error.message : 'Unknown error',
      methodId: req.params.methodId
    });
    sendError(res, 'Failed to get payment method', 500);
  }
};

/**
 * Get withdrawal steps
 */
export const getWithdrawalStepsController = async (req: Request, res: Response) => {
  try {
    const steps = await getWithdrawalSteps();

    sendSuccess(res, {
      withdrawal_steps: steps,
      total: steps.length
    }, 'Withdrawal steps retrieved successfully');

  } catch (error) {
    logger.error('Error getting withdrawal steps', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    sendError(res, 'Failed to get withdrawal steps', 500);
  }
};

/**
 * Get withdrawal progress for a transaction
 */
export const getWithdrawalProgressController = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const { transactionId } = req.params;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    if (!transactionId) {
      return sendError(res, 'Transaction ID is required', 400);
    }

    const progressData = await getWithdrawalProgressWithTransaction(parseInt(transactionId));

    if (!progressData) {
      return sendError(res, 'Transaction not found', 404);
    }

    // Verify transaction belongs to user
    if (progressData.transaction.user_id !== parseInt(userId)) {
      return sendUnauthorized(res, 'Access denied');
    }

    sendSuccess(res, {
      transaction: progressData.transaction,
      steps: progressData.steps
    }, 'Withdrawal progress retrieved successfully');

  } catch (error) {
    logger.error('Error getting withdrawal progress', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId,
      transactionId: req.params.transactionId
    });
    sendError(res, 'Failed to get withdrawal progress', 500);
  }
};

/**
 * Get withdrawal progress for multiple transactions (for user's withdrawal history)
 */
export const getUserWithdrawalProgressController = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const { limit = 10, offset = 0 } = req.query;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    // Get user's withdrawal transactions with progress
    const withdrawals = await getWithdrawalProgress(parseInt(userId));

    sendSuccess(res, {
      withdrawals,
      total: withdrawals.length
    }, 'User withdrawal progress retrieved successfully');

  } catch (error) {
    logger.error('Error getting user withdrawal progress', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to get withdrawal progress', 500);
  }
};

/**
 * Update withdrawal status (for admin/webhook use)
 */
export const updateWithdrawalStatusController = async (req: Request, res: Response) => {
  try {
    const { transactionId, action, reason, paymentMethod } = req.body;

    if (!transactionId || !action) {
      return sendError(res, 'Transaction ID and action are required', 400);
    }

    const validActions = ['success', 'fail', 'reverse', 'refund', 'cancel', 'review_approve', 'review_reject'];
    if (!validActions.includes(action)) {
      return sendError(res, 'Invalid action', 400);
    }

    let result = false;

    switch (action) {
      case 'success':
        result = await processSuccessfulWithdrawal(transactionId, paymentMethod || 'ach_standard');
        break;
      case 'fail':
        const failurePoint = req.body.failurePoint || 'processing';
        result = await processFailedWithdrawal(transactionId, failurePoint, reason || 'Payment failed');
        break;
      case 'reverse':
        result = await processWithdrawalReversal(transactionId, 'reversed', reason || 'Payment reversed');
        break;
      case 'refund':
        result = await processWithdrawalReversal(transactionId, 'refunded', reason || 'Payment refunded');
        break;
      case 'cancel':
        result = await processWithdrawalCancellation(transactionId, reason || 'Withdrawal cancelled');
        break;
      case 'review_approve':
        result = await processWithdrawalReview(transactionId, 'approved', reason || 'Review approved');
        break;
      case 'review_reject':
        result = await processWithdrawalReview(transactionId, 'rejected', reason || 'Review rejected');
        break;
    }

    if (result) {
      sendSuccess(res, {
        transactionId,
        action,
        message: `Withdrawal ${action} processed successfully`
      }, 'Withdrawal status updated successfully');
    } else {
      sendError(res, 'Failed to update withdrawal status', 500);
    }

  } catch (error) {
    logger.error('Error updating withdrawal status', {
      error: error instanceof Error ? error.message : 'Unknown error',
      transactionId: req.body.transactionId,
      action: req.body.action
    });
    sendError(res, 'Failed to update withdrawal status', 500);
  }
};

/**
 * Add custom withdrawal step (for admin use)
 */
export const addWithdrawalStepController = async (req: Request, res: Response) => {
  try {
    const { transactionId, stepKey, status, notes } = req.body;

    if (!transactionId || !stepKey || !status) {
      return sendError(res, 'Transaction ID, step key, and status are required', 400);
    }

    const validStatuses = ['pending', 'completed', 'failed', 'skipped'];
    if (!validStatuses.includes(status)) {
      return sendError(res, 'Invalid status', 400);
    }

    const result = await addWithdrawalStep(transactionId, stepKey, status, notes);

    if (result) {
      sendSuccess(res, {
        transactionId,
        stepKey,
        status,
        message: 'Withdrawal step added successfully'
      }, 'Withdrawal step added successfully');
    } else {
      sendError(res, 'Failed to add withdrawal step', 500);
    }

  } catch (error) {
    logger.error('Error adding withdrawal step', {
      error: error instanceof Error ? error.message : 'Unknown error',
      transactionId: req.body.transactionId,
      stepKey: req.body.stepKey
    });
    sendError(res, 'Failed to add withdrawal step', 500);
  }
};

/**
 * Progress withdrawal to next step (for testing/admin use)
 */
export const progressWithdrawalStepController = async (req: Request, res: Response) => {
  try {
    const { transactionId, nextStepKey, status = 'completed', notes } = req.body;

    if (!transactionId || !nextStepKey) {
      return sendError(res, 'Transaction ID and next step key are required', 400);
    }

    const validStatuses = ['pending', 'completed', 'failed'];
    if (!validStatuses.includes(status)) {
      return sendError(res, 'Invalid status', 400);
    }

    const { progressWithdrawalToNextStep } = await import('../services/paymentMethodService');
    const result = await progressWithdrawalToNextStep(transactionId, nextStepKey, status, notes);

    if (result) {
      sendSuccess(res, {
        transactionId,
        nextStepKey,
        status,
        notes,
        message: 'Withdrawal progressed to next step successfully'
      }, 'Withdrawal step progressed successfully');
    } else {
      sendError(res, 'Failed to progress withdrawal step', 500);
    }

  } catch (error) {
    logger.error('Error progressing withdrawal step', {
      error: error instanceof Error ? error.message : 'Unknown error',
      transactionId: req.body.transactionId,
      nextStepKey: req.body.nextStepKey
    });
    sendError(res, 'Failed to progress withdrawal step', 500);
  }
};
