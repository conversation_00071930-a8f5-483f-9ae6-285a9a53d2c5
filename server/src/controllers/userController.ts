import { Request, Response, RequestHandler } from 'express';
import jwt from 'jsonwebtoken';
import { decryptMessage, handleApiError, validateAndDecryptRequest } from '../utils/helpers';
import { executeUpdate, executeQuerySingle } from '../utils/database';
import { sendSuccess, sendError, sendUnauthorized, sendUserSuccess, sendUserError } from '../utils/response';
import logger from '../utils/logger';
import { checkPinSetupRequired } from '../services/walletService';
import {
  generateAndSaveOTP,
  generateAndSendOTP,
  verifyOTPCode,
  markOTPAsUsed,
  invalidateUserOTPs,
  cleanupExpiredOTPs as cleanupOTPs,
  OTP_CONFIG
} from '../services/otpService';
import {
  createUserSession,
  invalidateSessionByToken,
  hasUserEverHadSession,
  getActiveSessionCount,
  enforceSingleSessionPolicy
} from '../services/sessionService';
import { sendWelcomeEmail } from '../services/emailService';

// In-memory blacklist for revoked tokens (replace with a database or cache in production)
const tokenBlacklist: Set<string> = new Set();



export const login = async (req: Request, res: Response) => {
  let email: string | undefined;

  try {
    const { auth } = req.body;

    if (!auth) {
      return sendError(res, 'Authentication data is required', 400);
    }

    let decodedData;
    try {
      decodedData = await decryptMessage(auth);
    } catch (decryptError) {
      logger.warn('Failed to decrypt authentication data', {
        error: decryptError instanceof Error ? decryptError.message : 'Unknown error'
      });
      return sendUnauthorized(res, 'Invalid authentication data');
    }

    // Validate that decodedData contains required fields
    if (!decodedData || typeof decodedData !== 'object') {
      logger.warn('Decrypted data is not a valid object', { decodedData });
      return sendUnauthorized(res, 'Invalid authentication data format');
    }

    const { firstname, lastname, email: userEmail, id } = decodedData;
    email = userEmail;

    // Validate required fields
    if (!email || typeof email !== 'string') {
      logger.warn('Email is missing or invalid in decrypted data', { decodedData });
      return sendUnauthorized(res, 'Invalid authentication data - email required');
    }

    if (!id) {
      logger.warn('ID is missing in decrypted data', { decodedData });
      return sendUnauthorized(res, 'Invalid authentication data - id required');
    }

    // Optional: Validate firstname and lastname if they exist
    if (firstname && typeof firstname !== 'string') {
      logger.warn('Firstname is invalid in decrypted data', { decodedData });
      return sendUnauthorized(res, 'Invalid authentication data - invalid firstname');
    }

    // Check if user exists using parameterized query to prevent SQL injection
    let user = await executeQuerySingle(
      'SELECT * FROM tbl_users WHERE email = ?',
      [email]
    );

    if (!user) {
      // User doesn't exist, create new user
      logger.info('User not found, creating new user', { email });

      const fullName = [firstname, lastname].filter(Boolean).join(' ');
      const insertResult = await executeUpdate(
        'INSERT INTO tbl_users (email, full_name, team_connect_user_id, is_organizer,master_parent_user_id, role_id) VALUES (?, ?, ?, ?,?,?)',
        [email, fullName || null, id, 1, 0, 3]
      );

      // Get the newly created user
      user = await executeQuerySingle(
        'SELECT * FROM tbl_users WHERE id = ?',
        [insertResult.insertId]
      );

      if (!user) {
        logger.error('Failed to retrieve newly created user', { insertId: insertResult.insertId });
        return sendError(res, 'Failed to create user', 500);
      }

      logger.info('New user created successfully', { userId: user.id, email: user.email });
    } else {
      // User exists, use existing user data
      logger.info('Existing user found', { userId: user.id, email: user.email });
    }
    // Generate, save and send OTP for login verification
    const { otpCode, otpId, emailSent } = await generateAndSendOTP(user.id, user.email, 'login');

    // Generate JWT token
    const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';

    const token = jwt.sign(
      { id: user.id, team_connect_user_id: user.team_connect_user_id, master_parent_user_id: user.master_parent_user_id, email: user.email },
      jwtSecret,
      { expiresIn: '24h' }
    );

    sendUserSuccess(res, 'AUTH', 'OTP_SENT', {
      user: {
        id: user.id,
        email: user.email,
        firstname: user.full_name ? user.full_name.split(' ')[0] : firstname,
        lastname: user.full_name ? user.full_name.split(' ')[1] : lastname
      },
      token,
      emailSent,
      // Include OTP in development mode for testing
      ...(process.env.NODE_ENV === 'development' && {
        otp: otpCode,
        otpId,
        message: 'Development mode: OTP included for testing'
      })
    });

  } catch (error) {
    // Check if it's a decryption error (should be handled above, but just in case)
    if (error instanceof Error && error.message.includes('decrypt')) {
      logger.warn('Decryption error in login', {
        error: error.message,
        email: email || 'unknown'
      });
      return sendUnauthorized(res, 'Invalid authentication data');
    }

    // General error handling
    logger.error('Login error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      email: email || 'unknown'
    });
    sendError(res, 'Login failed', 500, error instanceof Error ? error.message : 'Unknown error');
  }
};

export const verifyOTP = async (req: Request & { userId?: string; parent_user_id?: string }, res: Response) => {
  try {
    const { otpCode } = req.body;
    const userId = req.userId; // Get userId from authenticated token
    const parentUserId = req.parent_user_id; // Get parent_user_id from authenticated token


    // Validate input
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    if (!otpCode) {
      return sendError(res, 'OTP code is required', 400);
    }

    if (typeof otpCode !== 'string' || otpCode.length !== OTP_CONFIG.LENGTH) {
      return sendError(res, `OTP must be ${OTP_CONFIG.LENGTH} digits`, 400);
    }

    // Get user by ID from token
    const user = await executeQuerySingle(
      'SELECT * FROM tbl_users WHERE id = ?',
      [userId]
    );

    if (!user) {
      logger.warn('User not found during OTP verification', { userId });
      return sendError(res, 'User not found', 404);
    }

    // Verify OTP using service
    const otpVerification = await verifyOTPCode(parseInt(userId), otpCode, 'login');

    if (!otpVerification.isValid) {
      logger.warn('OTP verification failed', {
        userId: user.id,
        email: user.email,
        otpCode,
        error: otpVerification.error
      });
      return sendError(res, otpVerification.error || 'Invalid OTP code', 400);
    }

    // Mark OTP as used
    if (otpVerification.otpRecord) {
      await markOTPAsUsed(otpVerification.otpRecord.id);
    }

    // Check if user has wallet PIN setup
    const requiresPinSetup = await checkPinSetupRequired(user.id);

    // Check current active sessions before creating new one
    const currentActiveSessions = await getActiveSessionCount(user.id);
    logger.info('User active sessions before login', {
      userId: user.id,
      email: user.email,
      currentActiveSessions
    });

    // Check if this is the user's first session (before creating new session)
    const isFirstTimeUser = !(await hasUserEverHadSession(user.id));

    // Generate JWT token first
    const token = jwt.sign(
      { id: user.id, team_connect_user_id: user.team_connect_user_id, master_parent_user_id: user.master_parent_user_id, email: user.email },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '24h' }
    );

    // Create user session using the JWT token as session key (this will invalidate any existing sessions)
    const userAgent = req.headers['user-agent'] || 'Unknown';
    const ipAddress = req.ip || req.socket.remoteAddress || 'Unknown';

    const { sessionId, expiresAt } = await createUserSession(
      user.id,
      token, // Use JWT token as session key
      userAgent,
      ipAddress
    );

    // Enforce single session policy as an additional safety measure
    await enforceSingleSessionPolicy(user.id);

    // Verify final session count
    const finalActiveSessions = await getActiveSessionCount(user.id);
    logger.info('User active sessions after login', {
      userId: user.id,
      email: user.email,
      finalActiveSessions,
      sessionId
    });

    // Warn if somehow multiple sessions still exist
    if (finalActiveSessions > 1) {
      logger.error('CRITICAL: Multiple active sessions detected after enforcement', {
        userId: user.id,
        email: user.email,
        finalActiveSessions,
        sessionId
      });
    }

    // Send welcome email for first-time users
    if (isFirstTimeUser) {
      try {
        const userName = user.full_name || user.email.split('@')[0];
        const welcomeEmailSent = await sendWelcomeEmail(user.email, userName);

        logger.info('Welcome email sent to first-time user', {
          userId: user.id,
          email: user.email,
          userName,
          emailSent: welcomeEmailSent
        });
      } catch (error) {
        // Don't fail the login if welcome email fails
        logger.error('Failed to send welcome email to first-time user', {
          userId: user.id,
          email: user.email,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    logger.info('OTP verified successfully', {
      userId: user.id,
      email: user.email,
      parentUserId,
      sessionId,
      requiresPinSetup,
      isFirstTimeUser
    });

    const responseData = {
      requiresPinSetup,
      isFirstTimeUser,
      user: {
        id: user.id,
        email: user.email,
        fullName: user.full_name,
        organizerId: user.organizer_id
      },
      token,
      session: {
        sessionId,
        expiresAt
      }
    };

    if (isFirstTimeUser) {
      sendUserSuccess(res, 'AUTH', 'FIRST_TIME_LOGIN', responseData);
    } else {
      sendUserSuccess(res, 'AUTH', 'OTP_VERIFIED', responseData);
    }

  } catch (error) {
    logger.error('OTP verification error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'OTP verification failed', 500, error instanceof Error ? error.message : 'Unknown error');
  }
};

export const resendOTP = async (req: Request & { userId?: string; parent_user_id?: string }, res: Response) => {
  try {
    const userId = req.userId; // Get userId from authenticated token

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    // Get user details
    const user = await executeQuerySingle(
      'SELECT * FROM tbl_users WHERE id = ?',
      [userId]
    );

    if (!user) {
      return sendError(res, 'User not found', 404);
    }

    // Invalidate all previous unused login OTPs
    await invalidateUserOTPs(user.id, 'login');

    // Generate, save and send new OTP
    const { otpCode, otpId, emailSent } = await generateAndSendOTP(user.id, user.email, 'login');

    logger.info('OTP resent', {
      userId: user.id,
      email: user.email,
      otpId,
      emailSent,
      expiresInMinutes: OTP_CONFIG.EXPIRY_MINUTES
    });

    sendUserSuccess(res, 'AUTH', 'OTP_RESEND', {
      message: 'New security code sent successfully',
      userId: user.id,
      email: user.email,
      emailSent,
      // Remove this in production - OTP should be sent via SMS/Email
      otp: process.env.NODE_ENV === 'development' ? otpCode : undefined,
      expiresInMinutes: OTP_CONFIG.EXPIRY_MINUTES
    });

  } catch (error) {
    logger.error('Resend OTP error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to resend OTP', 500, error instanceof Error ? error.message : 'Unknown error');
  }
};

/**
 * Clean up expired OTPs (utility function - can be called by a cron job)
 */
export const cleanupExpiredOTPs = async (_req: Request, res: Response) => {
  try {
    const deletedCount = await cleanupOTPs();

    sendSuccess(res, {
      deletedCount
    }, 'Expired OTPs cleaned up successfully');

  } catch (error) {
    logger.error('Cleanup expired OTPs error', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    sendError(res, 'Failed to cleanup expired OTPs', 500, error instanceof Error ? error.message : 'Unknown error');
  }
};

/**
 * Clean up expired sessions (utility function - can be called by a cron job)
 */
export const cleanupExpiredSessions = async (_req: Request, res: Response) => {
  try {
    const { cleanupExpiredSessions: cleanupSessions } = await import('../services/sessionService');
    const deletedCount = await cleanupSessions();

    sendSuccess(res, {
      deletedCount
    }, 'Expired sessions cleaned up successfully');

  } catch (error) {
    logger.error('Cleanup expired sessions error', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    sendError(res, 'Failed to cleanup expired sessions', 500, error instanceof Error ? error.message : 'Unknown error');
  }
};

export const logout: RequestHandler = async (
  req: Request & { userId?: string; parent_user_id?: string; sessionToken?: string },
  res: Response
): Promise<void> => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      sendError(res, 'Token not provided', 400);
      return;
    }

    // Add token to blacklist
    tokenBlacklist.add(token);

    // Invalidate session using the JWT token itself as session key
    await invalidateSessionByToken(token);
    logger.info('Session invalidated during logout', {
      userId: req.userId
    });

    logger.info('User logged out successfully', { userId: req.userId });
    sendSuccess(res, undefined, 'Logged out successfully');
  } catch (error) {
    logger.error('Logout error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Logout failed', 500);
  }
};





// Helper function for common error handling

// Add Account API - Focuses on parent-child relationship
export const addUserWithAddAccount = async (req: Request, res: Response) => {
  try {
    const { body } = req.body;
    const decodedData = await validateAndDecryptRequest(body);

    // Specific validation for this endpoint
    if (!decodedData.user_id) {
      logger.warn('Missing user_id in decrypted data', { decodedData });
      return sendUnauthorized(res, 'User ID is required');
    }

    // Find existing user
    const user = await executeQuerySingle(
      'SELECT * FROM tbl_users WHERE team_connect_user_id = ?',
      [decodedData.user_id]
    );

    if (!user) {
      logger.warn('User not found', { user_id: decodedData.user_id });
      return sendError(res, 'User not found', 404);
    }

    // Create parent relationship if parent_user_id exists
    if (decodedData.parent_user_id) {
      await executeUpdate(
        'INSERT INTO tbl_user_parents (user_id, parent_user_id,role_id) VALUES (?, ?,?)',
        [user.id, decodedData.parent_user_id , decodedData.role_id]
      );
      logger.info('Parent relationship created', {
        user_id: user.id,
        parent_id: decodedData.parent_user_id
      });
    }

    return sendSuccess(res, {
      user: {
        id: user.id,
        email: user.email
      }
    }, 'User relationship added successfully');

  } catch (error) {
    handleApiError(res, error, 'Add account');
  }
};

// Add Member API - Focuses on user creation
export const addUsersWithAddMember = async (req: Request, res: Response) => {
  let email: string | undefined;

  try {
    const { body } = req.body;
    const decodedData = await validateAndDecryptRequest(body);
    email = decodedData.email;

    // Specific validation for this endpoint
    if (!decodedData.email || !decodedData.id) {
      logger.warn('Missing required fields', { decodedData });
      return sendUnauthorized(res, 'Email and ID are required');
    }

    // Check if user exists
    let user = await executeQuerySingle(
      'SELECT * FROM tbl_users WHERE email = ?',
      [decodedData.email]
    );

    if (!user) {
      // Create new user
      const fullName = [decodedData.firstname, decodedData.lastname].filter(Boolean).join(' ');
      
      const insertResult = await executeUpdate(
        'INSERT INTO tbl_users (email, full_name, team_connect_user_id, is_organizer, master_parent_user_id, role_id,phone, profile_pic) VALUES (?, ?, ?, ?, ?, ?,?,?)',
        [
          decodedData.email,
          fullName || null,
          decodedData.id,
          decodedData.is_organizer,
          decodedData.parent_user_id,
          decodedData.role_id,
          decodedData.contact,
          decodedData.avatar

        ]
      );

      user = await executeQuerySingle(
        'SELECT * FROM tbl_users WHERE id = ?',
        [insertResult.insertId]
      );

      if (!user) {
        throw new Error('Failed to retrieve created user');
      }

      // Create wallet for new user
      const walletUniqueId = `wallet_${decodedData.id}_${Date.now()}`;
      const walletName = fullName || `Wallet ${decodedData.id}`;
      const walletUsername = decodedData.email.split('@')[0];

      await executeUpdate(
        `INSERT INTO tbl_masterwallet
         (wallet_unique_id, user_id, name, username, balance, status_id, created_at, last_updated)
         VALUES (?, ?, ?, ?, 0.00, 1, NOW(), NOW())`,
        [walletUniqueId, user.id, walletName, walletUsername]
      );

      // Create parent relationship if specified
      if (decodedData.parent_user_id) {
        await executeUpdate(
          'INSERT INTO tbl_user_parents (user_id, parent_user_id,role_id) VALUES (?, ?,?)',
          [user.id, decodedData.parent_user_id , decodedData.role_id ]
        );
      }

      logger.info('New user created', { userId: user.id });
    }

    return sendSuccess(res, {
      user: {
        id: user.id,
        email: user.email,
        firstname: user.full_name?.split(' ')[0] || decodedData.firstname,
        lastname: user.full_name?.split(' ')[1] || decodedData.lastname
      }
    }, 'User processed successfully');

  } catch (error) {
    handleApiError(res, error, 'Add member', email);
  }
};


export const generateToken = async (req:Request , res:Response) => {
  try {
    const { body } = req.body;
    const decodedData = await validateAndDecryptRequest(body);

    // Specific validation for this endpoint
    if (!decodedData.user_id) {
      logger.warn('Missing user_id in decrypted data', { decodedData });
      return sendUnauthorized(res, 'User ID is required');
    }

    // Find existing user
    const user = await executeQuerySingle(
      'SELECT * FROM tbl_users WHERE team_connect_user_id = ?',
      [decodedData.user_id]
    );

    if (!user) {
      logger.warn('User not found', { user_id: decodedData.user_id });
      return sendError(res, 'User not found', 404);
    }

    // Generate JWT token
    const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';

    const token = jwt.sign(
      { id: user.id, team_connect_user_id: user.team_connect_user_id, master_parent_user_id: user.master_parent_user_id, email: user.email },
      jwtSecret,
      { expiresIn: '24h' }
    );

    return sendSuccess(res, {
      token
    }, 'Token generated successfully');

  } catch (error) {
    handleApiError(res, error, 'Generate token');
  }
};





// export const addUser = async (req: Request, res: Response) => {
//   let email: string | undefined;

//   try {
//     const { body } = req.body;
//     console.log(body, 'body', req.body)
//     if (!body) {
//       return sendError(res, 'Authentication data is required', 400);
//     }

//     let decodedData;
//     try {
//       decodedData = await decryptMessage(body);
//       console.log(decodedData, 'decorrrr')
//     } catch (decryptError) {
//       logger.warn('Failed to decrypt authentication data', {
//         error: decryptError instanceof Error ? decryptError.message : 'Unknown error'
//       });
//       return sendUnauthorized(res, 'Invalid authentication data');
//     }

//     // Validate that decodedData contains required fields
//     if (!decodedData || typeof decodedData !== 'object') {
//       logger.warn('Decrypted data is not a valid object', { decodedData });
//       return sendUnauthorized(res, 'Invalid authentication data format');
//     }

//     const { firstname, lastname, email: userEmail, id, role_id, is_organizer } = decodedData;
//     email = userEmail;

//     // Validate required fields
//     if (!email || typeof email !== 'string') {
//       logger.warn('Email is missing or invalid in decrypted data', { decodedData });
//       return sendUnauthorized(res, 'Invalid authentication data - email required');
//     }

//     if (!id) {
//       logger.warn('ID is missing in decrypted data', { decodedData });
//       return sendUnauthorized(res, 'Invalid authentication data - id required');
//     }

//     // Optional: Validate firstname and lastname if they exist
//     if (firstname && typeof firstname !== 'string') {
//       logger.warn('Firstname is invalid in decrypted data', { decodedData });
//       return sendUnauthorized(res, 'Invalid authentication data - invalid firstname');
//     }

//     // Check if user exists using parameterized query to prevent SQL injection
//     let user = await executeQuerySingle(
//       'SELECT * FROM tbl_users WHERE email = ?',
//       [email]
//     );


//     if (!user) {
//       // User doesn't exist, create new user
//       logger.info('User not found, creating new user', { email });

//       const fullName = [firstname, lastname].filter(Boolean).join(' ');
//       const insertResult = await executeUpdate(
//         'INSERT INTO tbl_users (email, full_name, team_connect_user_id, is_organizer,parent_user_id, role_id) VALUES (?, ?, ?, ?,?,?)',
//         [email, fullName || null, id, is_organizer, 0, role_id]
//       );

//       // Get the newly created user
//       user = await executeQuerySingle(
//         'SELECT * FROM tbl_users WHERE id = ?',
//         [insertResult.insertId]
//       );
//       // Generate unique wallet ID
//       const walletUniqueId = `wallet_${id}_${Date.now()}`;

//       // Use full_name as wallet name and email prefix as username
//       const walletName = fullName || `Wallet ${id}`;
//       const walletUsername = email ? email.split('@')[0] : `user_${insertResult.insertId}`;

//       await executeUpdate(
//         `INSERT INTO tbl_masterwallet
//        (wallet_unique_id, user_id, name, username,  balance, status_id, created_at, last_updated)
//        VALUES (?, ?, ?, ?, 0.00, 1, NOW(), NOW())`,
//         [walletUniqueId, insertResult.insertId, walletName, walletUsername]
//       );
//       if (!user) {
//         logger.error('Failed to retrieve newly created user', { insertId: insertResult.insertId });
//         return sendError(res, 'Failed to create user', 500);
//       }

//       logger.info('New user created successfully', { userId: user.id, email: user.email });
//     } else {
//       // User exists, use existing user data
//       logger.info('Existing user found', { userId: user.id, email: user.email });
//     }
//     // Generate and save OTP for login verification


//     // Generate JWT token


//     sendSuccess(res, {
//       user: {
//         id: user.id,
//         email: user.email,
//         firstname: user.full_name ? user.full_name.split(' ')[0] : firstname,
//         lastname: user.full_name ? user.full_name.split(' ')[1] : lastname
//       },
//       // Include OTP in development mode for testing



//     }, 'User Added successful');

//   } catch (error) {
//     // Check if it's a decryption error (should be handled above, but just in case)
//     if (error instanceof Error && error.message.includes('decrypt')) {
//       logger.warn('Decryption error in login', {
//         error: error.message,
//         email: email || 'unknown'
//       });
//       return sendUnauthorized(res, 'Invalid authentication data');
//     }

//     // General error handling
//     logger.error('Login error', {
//       error: error instanceof Error ? error.message : 'Unknown error',
//       email: email || 'unknown'
//     });
//     sendError(res, 'Login failed', 500, error instanceof Error ? error.message : 'Unknown error');
//   }
// };