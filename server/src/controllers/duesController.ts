import { Request, Response } from 'express';
import logger from '../utils/logger';
import { sendSuccess, sendError, sendUserError } from '../utils/response';
import { 
  getMyDues, 
  getAwaitingPayments, 
  getTeamDetail, 
  getGameSeasons, 
  getDuesSummary,
  payDueWithWallet,
  PaymentRequest
} from '../services/duesService';
import { verifyPin } from '../services/walletService';

/**
 * Get dues for the authenticated user
 * GET /api/dues/get-Dues
 */
export const getMyDuesController = async (
  req: Request & { team_connect_user_id?: string },
  res: Response
) => {
  try {
    const userId = req.team_connect_user_id;
    if (!userId) {
      return sendUserError(res, 'AUTH', 'MISSING_USER_ID', 400);
    }

    logger.info('Getting my dues', { userId });

    const { status, search, limit, offset } = req.query;
    
    const filters = {
      status: status as string,
      search: search as string,
      limit: limit ? parseInt(limit as string) : undefined,
      offset: offset ? parseInt(offset as string) : undefined
    };

    const { data, totalRecords } = await getMyDues(userId, filters);

    sendSuccess(res, {
      data,
      totalRecords,
      page: offset ? Math.floor(parseInt(offset as string) / (limit ? parseInt(limit as string) : 10)) + 1 : 1,
      pageSize: limit ? parseInt(limit as string) : 10
    }, 'Dues retrieved successfully');
  } catch (error) {
    logger.error('Error getting my dues', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    sendError(res, 'Failed to retrieve dues', 500);
  }
};

/**
 * Get dues summary for dashboard
 * GET /api/dues/summary
 */
export const getDuesSummaryController = async (
  req: Request & { team_connect_user_id?: string },
  res: Response
) => {
  try {
    const userId = req.team_connect_user_id;
    if (!userId) {
      return sendUserError(res, 'AUTH', 'MISSING_USER_ID', 400);
    }

    logger.info('Getting dues summary', { userId });

    const summary = await getDuesSummary(userId);

    sendSuccess(res, summary, 'Dues summary retrieved successfully');
  } catch (error) {
    logger.error('Error getting dues summary', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    sendError(res, 'Failed to retrieve dues summary', 500);
  }
};

/**
 * Get awaiting payments for organization admin
 * POST /api/dues/awaiting-payments
 */
export const getAwaitingPaymentsController = async (
  req: Request & { team_connect_user_id?: string },
  res: Response
) => {
  try {
    const userId = req.team_connect_user_id;
    if (!userId) {
      return sendUserError(res, 'AUTH', 'MISSING_USER_ID', 400);
    }

    logger.info('Getting awaiting payments', { userId, filters: req.body });

    const { game_id, season_id, group_id, search, page = 1, pageSize = 10 } = req.body;
    
    const filters = {
      game_id,
      season_id,
      group_id,
      search,
      limit: pageSize,
      offset: (page - 1) * pageSize
    };

    const { data, totalRecords } = await getAwaitingPayments(userId, filters);

    sendSuccess(res, {
      data,
      totalRecords,
      page,
      pageSize
    }, 'Awaiting payments retrieved successfully');
  } catch (error) {
    logger.error('Error getting awaiting payments', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    sendError(res, 'Failed to retrieve awaiting payments', 500);
  }
};

/**
 * Get team details with payment information
 * GET /api/dues/team/:id
 */
export const getTeamDetailController = async (
  req: Request & { team_connect_user_id?: string },
  res: Response
) => {
  try {
    const teamId = req.params.id;
    if (!teamId) {
      return sendUserError(res, 'SYSTEM', 'MISSING_TEAM_ID', 400);
    }

    logger.info('Getting team details', { teamId });

    const teamDetails = await getTeamDetail(teamId);

    sendSuccess(res, teamDetails, 'Team details retrieved successfully');
  } catch (error) {
    logger.error('Error getting team details', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    sendError(res, 'Failed to retrieve team details', 500);
  }
};

/**
 * Get game seasons for filters
 * GET /api/dues/game-seasons
 */
export const getGameSeasonsController = async (
  req: Request & { team_connect_user_id?: string },
  res: Response
) => {
  try {
    const userId = req.team_connect_user_id;
    if (!userId) {
      return sendUserError(res, 'AUTH', 'MISSING_USER_ID', 400);
    }

    logger.info('Getting game seasons', { userId });

    const gameSeasons = await getGameSeasons(userId);

    sendSuccess(res, gameSeasons, 'Game seasons retrieved successfully');
  } catch (error) {
    logger.error('Error getting game seasons', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    sendError(res, 'Failed to retrieve game seasons', 500);
  }
};

/**
 * Pay due with wallet
 * POST /api/dues/pay
 */
export const payDueController = async (
  req: Request & { team_connect_user_id?: string },
  res: Response
) => {
  try {
    const userId = req.team_connect_user_id;
    if (!userId) {
      return sendUserError(res, 'AUTH', 'MISSING_USER_ID', 400);
    }

    const { dueId, amount, recipientUserId, pin, description } = req.body;

    if (!dueId || !amount || !recipientUserId || !pin) {
      return sendUserError(res, 'SYSTEM', 'MISSING_REQUIRED_FIELDS', 400);
    }

    logger.info('Processing due payment', { userId, dueId, amount });

    // Verify PIN first
    const pinVerification = await verifyPin(userId, pin);
    if (!pinVerification.success) {
      return sendUserError(res, 'PIN', 'INVALID_PIN', 400);
    }

    const paymentRequest: PaymentRequest = {
      dueId,
      amount,
      payerUserId: userId,
      recipientUserId,
      pin,
      description
    };

    const result = await payDueWithWallet(paymentRequest);

    if (result.success) {
      sendSuccess(res, result, 'Payment processed successfully');
    } else {
      sendUserError(res, 'MONEY', 'PAYMENT_FAILED', 400, result.message);
    }
  } catch (error) {
    logger.error('Error processing due payment', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    sendError(res, 'Failed to process payment', 500);
  }
};

/**
 * Pay multiple dues with wallet (bulk payment)
 * POST /api/dues/pay-bulk
 */
export const payBulkDuesController = async (
  req: Request & { team_connect_user_id?: string },
  res: Response
) => {
  try {
    const userId = req.team_connect_user_id;
    if (!userId) {
      return sendUserError(res, 'AUTH', 'MISSING_USER_ID', 400);
    }

    const { payments, pin } = req.body;

    if (!payments || !Array.isArray(payments) || payments.length === 0 || !pin) {
      return sendUserError(res, 'SYSTEM', 'MISSING_REQUIRED_FIELDS', 400);
    }

    logger.info('Processing bulk dues payment', { userId, paymentCount: payments.length });

    // Verify PIN first
    const pinVerification = await verifyPin(userId, pin);
    if (!pinVerification.success) {
      return sendUserError(res, 'PIN', 'INVALID_PIN', 400);
    }

    // Process each payment
    const results = [];
    let successCount = 0;
    let failCount = 0;
    let totalAmount = 0;

    for (const payment of payments) {
      const { dueId, amount, recipientUserId, description } = payment;
      
      if (!dueId || !amount || !recipientUserId) {
        results.push({
          dueId: payment.dueId || 'unknown',
          success: false,
          message: 'Missing required fields'
        });
        failCount++;
        continue;
      }

      totalAmount += amount;

      const paymentRequest: PaymentRequest = {
        dueId,
        amount,
        payerUserId: userId,
        recipientUserId,
        pin,
        description
      };

      try {
        const result = await payDueWithWallet(paymentRequest);
        
        results.push({
          dueId,
          success: result.success,
          message: result.message,
          transactionId: result.transactionId
        });

        if (result.success) {
          successCount++;
        } else {
          failCount++;
        }
      } catch (error) {
        results.push({
          dueId,
          success: false,
          message: 'Payment processing failed'
        });
        failCount++;
      }
    }

    sendSuccess(res, {
      success: successCount > 0,
      results,
      totalAmount,
      successfulPayments: successCount,
      failedPayments: failCount
    }, 'Bulk payment processing completed');
  } catch (error) {
    logger.error('Error processing bulk dues payment', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    sendError(res, 'Failed to process bulk payment', 500);
  }
};
