import { Request, Response } from 'express';
import { getAuditLogs } from '../services/auditService';
import logger from '../utils/logger';

/**
 * Get audit logs for a specific entity
 * @param req - Request object
 * @param res - Response object
 */
export async function getEntityAuditLogs(req: Request, res: Response): Promise<void> {
  try {
    const { entityType, entityId } = req.params;
    const { limit = 50, offset = 0 } = req.query;
    
    const logs = await getAuditLogs(
      entityType,
      entityId,
      parseInt(limit as string),
      parseInt(offset as string)
    );
    
    res.status(200).json({
      success: true,
      logs
    });
  } catch (error) {
    logger.error('Error getting audit logs', { error });
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve audit logs'
    });
  }
}

/**
 * Get bank account audit logs for a staff member
 * @param req - Request object
 * @param res - Response object
 */
export async function getStaffBankAccountAuditLogs(req: Request, res: Response): Promise<void> {
  try {
    const { staffId } = req.params;
    const { limit = 50, offset = 0 } = req.query;
    
    const logs = await getAuditLogs(
      'staff_bank_account',
      staffId,
      parseInt(limit as string),
      parseInt(offset as string)
    );
    
    res.status(200).json({
      success: true,
      logs
    });
  } catch (error) {
    logger.error('Error getting staff bank account audit logs', { error });
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve staff bank account audit logs'
    });
  }
}