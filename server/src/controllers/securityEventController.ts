import { Request, Response } from 'express';
import { sendError } from '../utils/response';

export const logEvent = async (req: Request, res: Response) => {
  // TODO: Implement security event logging
  sendError(res, 'Not implemented', 501);
};

export const getEvents = async (req: Request, res: Response) => {
  // TODO: Implement fetching security events
  sendError(res, 'Not implemented', 501);
};