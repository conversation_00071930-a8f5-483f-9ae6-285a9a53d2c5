import fs from 'fs';
import path from 'path';
import logger from '../utils/logger';

/**
 * Email template service for loading and processing HTML email templates
 */
export class EmailTemplateService {
  private static templateCache: Map<string, string> = new Map();
  private static readonly TEMPLATES_DIR = path.join(__dirname, '../templates/email');

  /**
   * Load an email template from file
   * @param templateName - Name of the template file (without extension)
   * @param extension - File extension (html or txt)
   * @returns Promise<string> - Template content
   */
  static async loadTemplate(templateName: string, extension: 'html' | 'txt' = 'html'): Promise<string> {
    try {
      const cacheKey = `${templateName}.${extension}`;

      // Check cache first
      if (this.templateCache.has(cacheKey)) {
        return this.templateCache.get(cacheKey)!;
      }

      const templatePath = path.join(this.TEMPLATES_DIR, `${templateName}.${extension}`);

      // Check if template file exists
      if (!fs.existsSync(templatePath)) {
        throw new Error(`Email template not found: ${templateName}.${extension}`);
      }

      // Read template file
      const templateContent = fs.readFileSync(templatePath, 'utf-8');

      // Cache the template
      this.templateCache.set(cacheKey, templateContent);

      logger.info(`Email template loaded: ${templateName}.${extension}`);
      return templateContent;
    } catch (error) {
      logger.error(`Error loading email template: ${templateName}`, error);
      throw error;
    }
  }

  /**
   * Process template with variables
   * @param templateContent - HTML template content
   * @param variables - Object containing template variables
   * @returns string - Processed HTML content
   */
  static processTemplate(templateContent: string, variables: Record<string, string>): string {
    let processedContent = templateContent;

    // Replace all template variables
    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{{${key}}}`;
      processedContent = processedContent.replace(new RegExp(placeholder, 'g'), value);
    });

    return processedContent;
  }

  /**
   * Load and process template in one step
   * @param templateName - Name of the template file
   * @param variables - Object containing template variables
   * @param extension - File extension (html or txt)
   * @returns Promise<string> - Processed template content
   */
  static async loadAndProcessTemplate(
    templateName: string,
    variables: Record<string, string>,
    extension: 'html' | 'txt' = 'html'
  ): Promise<string> {
    const templateContent = await this.loadTemplate(templateName, extension);
    return this.processTemplate(templateContent, variables);
  }

  /**
   * Load and process both HTML and text templates
   * @param templateName - Name of the template file
   * @param variables - Object containing template variables
   * @returns Promise<{html: string, text: string}> - Processed HTML and text content
   */
  static async loadAndProcessBothTemplates(
    templateName: string,
    variables: Record<string, string>
  ): Promise<{html: string, text: string}> {
    try {
      // Try to load both HTML and text templates
      const [htmlContent, textContent] = await Promise.allSettled([
        this.loadAndProcessTemplate(templateName, variables, 'html'),
        this.loadAndProcessTemplate(templateName, variables, 'txt')
      ]);

      const html = htmlContent.status === 'fulfilled' ? htmlContent.value : '';
      let text = textContent.status === 'fulfilled' ? textContent.value : '';

      // If no text template, generate from HTML
      if (!text && html) {
        text = this.htmlToText(html);
      }

      return { html, text };
    } catch (error) {
      logger.error(`Error loading both templates for: ${templateName}`, error);
      throw error;
    }
  }

  /**
   * Clear template cache
   */
  static clearCache(): void {
    this.templateCache.clear();
    logger.info('Email template cache cleared');
  }

  /**
   * Get available templates
   * @returns Promise<string[]> - Array of available template names
   */
  static async getAvailableTemplates(): Promise<string[]> {
    try {
      const files = fs.readdirSync(this.TEMPLATES_DIR);
      return files
        .filter(file => file.endsWith('.html'))
        .map(file => file.replace('.html', ''));
    } catch (error) {
      logger.error('Error reading templates directory', error);
      return [];
    }
  }

  /**
   * Generate plain text version from HTML
   * @param htmlContent - HTML content
   * @returns string - Plain text version
   */
  static htmlToText(htmlContent: string): string {
    return htmlContent
      // Remove HTML tags
      .replace(/<[^>]*>/g, '')
      // Replace multiple spaces with single space
      .replace(/\s+/g, ' ')
      // Replace HTML entities
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      // Trim whitespace
      .trim();
  }
}

export default EmailTemplateService;
