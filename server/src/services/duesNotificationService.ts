import { executeQuery, executeUpdate } from '../utils/database';
import logger from '../utils/logger';
import { sendEmail } from './emailService';

// Notification types
export enum NotificationType {
  PAYMENT_SUCCESS = 'payment_success',
  PAYMENT_FAILED = 'payment_failed',
  PAYMENT_REMINDER = 'payment_reminder',
  PAYMENT_OVERDUE = 'payment_overdue'
}

// Notification interface
export interface DueNotification {
  id?: number;
  user_id: string;
  type: NotificationType;
  title: string;
  message: string;
  due_id?: string;
  transaction_id?: string;
  read: boolean;
  created_at?: string;
}

/**
 * Create a notification for a user
 */
export const createNotification = async (notification: DueNotification): Promise<number> => {
  try {
    logger.info('Creating notification', { userId: notification.user_id, type: notification.type });

    const result = await executeUpdate(
      `INSERT INTO tbl_notifications 
       (user_id, type, title, message, reference_id, transaction_id, read, created_at) 
       VALUES (?, ?, ?, ?, ?, ?, ?, NOW())`,
      [
        notification.user_id,
        notification.type,
        notification.title,
        notification.message,
        notification.due_id,
        notification.transaction_id,
        notification.read ? 1 : 0
      ]
    );

    logger.info('Notification created successfully', { 
      userId: notification.user_id, 
      notificationId: result.insertId 
    });

    return result.insertId;
  } catch (error) {
    logger.error('Error creating notification', { 
      userId: notification.user_id, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
    throw new Error(`Failed to create notification: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Send payment success notification
 */
export const sendPaymentSuccessNotification = async (
  userId: string,
  dueId: string,
  transactionId: string,
  amount: number,
  description: string
): Promise<void> => {
  try {
    logger.info('Sending payment success notification', { userId, dueId, transactionId });

    // Get user details
    const users = await executeQuery(
      'SELECT email, firstname FROM users WHERE id = ?',
      [userId]
    );

    if (!users.length) {
      logger.warn('User not found for notification', { userId });
      return;
    }

    const user = users[0];
    
    // Create notification
    const notification: DueNotification = {
      user_id: userId,
      type: NotificationType.PAYMENT_SUCCESS,
      title: 'Payment Successful',
      message: `Your payment of $${amount.toFixed(2)} for ${description} has been processed successfully.`,
      due_id: dueId,
      transaction_id: transactionId,
      read: false
    };

    await createNotification(notification);

    // Send email notification
    await sendEmail({
      to: user.email,
      subject: 'Payment Successful',
      text: `Hello ${user.firstname},\n\nYour payment of $${amount.toFixed(2)} for ${description} has been processed successfully.\n\nTransaction ID: ${transactionId}\n\nThank you for your payment.`,
      html: `
        <h2>Payment Successful</h2>
        <p>Hello ${user.firstname},</p>
        <p>Your payment of <strong>$${amount.toFixed(2)}</strong> for ${description} has been processed successfully.</p>
        <p>Transaction ID: ${transactionId}</p>
        <p>Thank you for your payment.</p>
      `
    });

    logger.info('Payment success notification sent', { userId, dueId, transactionId });
  } catch (error) {
    logger.error('Error sending payment success notification', { 
      userId, 
      dueId, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
  }
};

/**
 * Send payment failed notification
 */
export const sendPaymentFailedNotification = async (
  userId: string,
  dueId: string,
  amount: number,
  description: string,
  errorMessage: string
): Promise<void> => {
  try {
    logger.info('Sending payment failed notification', { userId, dueId });

    // Get user details
    const users = await executeQuery(
      'SELECT email, firstname FROM users WHERE id = ?',
      [userId]
    );

    if (!users.length) {
      logger.warn('User not found for notification', { userId });
      return;
    }

    const user = users[0];
    
    // Create notification
    const notification: DueNotification = {
      user_id: userId,
      type: NotificationType.PAYMENT_FAILED,
      title: 'Payment Failed',
      message: `Your payment of $${amount.toFixed(2)} for ${description} has failed. Reason: ${errorMessage}`,
      due_id: dueId,
      read: false
    };

    await createNotification(notification);

    // Send email notification
    await sendEmail({
      to: user.email,
      subject: 'Payment Failed',
      text: `Hello ${user.firstname},\n\nYour payment of $${amount.toFixed(2)} for ${description} has failed.\n\nReason: ${errorMessage}\n\nPlease try again or contact support if the issue persists.`,
      html: `
        <h2>Payment Failed</h2>
        <p>Hello ${user.firstname},</p>
        <p>Your payment of <strong>$${amount.toFixed(2)}</strong> for ${description} has failed.</p>
        <p>Reason: ${errorMessage}</p>
        <p>Please try again or contact support if the issue persists.</p>
      `
    });

    logger.info('Payment failed notification sent', { userId, dueId });
  } catch (error) {
    logger.error('Error sending payment failed notification', { 
      userId, 
      dueId, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
  }
};

/**
 * Send payment reminder notification
 */
export const sendPaymentReminderNotification = async (
  userId: string,
  dueId: string,
  amount: number,
  description: string,
  dueDate: string
): Promise<void> => {
  try {
    logger.info('Sending payment reminder notification', { userId, dueId });

    // Get user details
    const users = await executeQuery(
      'SELECT email, firstname FROM users WHERE id = ?',
      [userId]
    );

    if (!users.length) {
      logger.warn('User not found for notification', { userId });
      return;
    }

    const user = users[0];
    
    // Create notification
    const notification: DueNotification = {
      user_id: userId,
      type: NotificationType.PAYMENT_REMINDER,
      title: 'Payment Reminder',
      message: `Your payment of $${amount.toFixed(2)} for ${description} is due on ${new Date(dueDate).toLocaleDateString()}.`,
      due_id: dueId,
      read: false
    };

    await createNotification(notification);

    // Send email notification
    await sendEmail({
      to: user.email,
      subject: 'Payment Reminder',
      text: `Hello ${user.firstname},\n\nThis is a reminder that your payment of $${amount.toFixed(2)} for ${description} is due on ${new Date(dueDate).toLocaleDateString()}.\n\nPlease make your payment before the due date to avoid any late fees.`,
      html: `
        <h2>Payment Reminder</h2>
        <p>Hello ${user.firstname},</p>
        <p>This is a reminder that your payment of <strong>$${amount.toFixed(2)}</strong> for ${description} is due on ${new Date(dueDate).toLocaleDateString()}.</p>
        <p>Please make your payment before the due date to avoid any late fees.</p>
      `
    });

    logger.info('Payment reminder notification sent', { userId, dueId });
  } catch (error) {
    logger.error('Error sending payment reminder notification', { 
      userId, 
      dueId, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
  }
};

/**
 * Send payment overdue notification
 */
export const sendPaymentOverdueNotification = async (
  userId: string,
  dueId: string,
  amount: number,
  description: string,
  dueDate: string
): Promise<void> => {
  try {
    logger.info('Sending payment overdue notification', { userId, dueId });

    // Get user details
    const users = await executeQuery(
      'SELECT email, firstname FROM users WHERE id = ?',
      [userId]
    );

    if (!users.length) {
      logger.warn('User not found for notification', { userId });
      return;
    }

    const user = users[0];
    
    // Create notification
    const notification: DueNotification = {
      user_id: userId,
      type: NotificationType.PAYMENT_OVERDUE,
      title: 'Payment Overdue',
      message: `Your payment of $${amount.toFixed(2)} for ${description} was due on ${new Date(dueDate).toLocaleDateString()} and is now overdue.`,
      due_id: dueId,
      read: false
    };

    await createNotification(notification);

    // Send email notification
    await sendEmail({
      to: user.email,
      subject: 'Payment Overdue',
      text: `Hello ${user.firstname},\n\nYour payment of $${amount.toFixed(2)} for ${description} was due on ${new Date(dueDate).toLocaleDateString()} and is now overdue.\n\nPlease make your payment as soon as possible to avoid any further late fees.`,
      html: `
        <h2>Payment Overdue</h2>
        <p>Hello ${user.firstname},</p>
        <p>Your payment of <strong>$${amount.toFixed(2)}</strong> for ${description} was due on ${new Date(dueDate).toLocaleDateString()} and is now overdue.</p>
        <p>Please make your payment as soon as possible to avoid any further late fees.</p>
      `
    });

    logger.info('Payment overdue notification sent', { userId, dueId });
  } catch (error) {
    logger.error('Error sending payment overdue notification', { 
      userId, 
      dueId, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
  }
};
