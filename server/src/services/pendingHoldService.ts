import { executeUpdate, executeQuerySingle, executeQuery } from '../utils/database';
import logger from '../utils/logger';

export interface PendingHold {
  id: number;
  user_id: number;
  amount: number;
  type: 'withdrawal' | 'deposit';
  reference_id: string;
  plaid_transfer_id?: string;
  status: 'pending' | 'confirmed' | 'failed' | 'released';
  created_at: Date;
  expires_at: Date;
  meta_data: any;
}

/**
 * Create a pending hold for a withdrawal (staff payment)
 * This reduces available balance without actually debiting the wallet
 */
export async function createWithdrawalHold(
  userId: number,
  amount: number,
  plaidTransferId: string,
  description: string,
  metadata: any = {}
): Promise<{ success: boolean; holdId?: number; message?: string }> {
  try {
    // Create the pending hold record
    const holdResult = await executeUpdate(
      `INSERT INTO tbl_pending_holds 
       (user_id, amount, type, reference_id, plaid_transfer_id, status, description, meta_data, created_at, expires_at)
       VALUES (?, ?, 'withdrawal', ?, ?, 'pending', ?, ?, NOW(), DATE_ADD(NOW(), INTERVAL 7 DAY))`,
      [
        userId,
        amount,
        plaidTransferId,
        plaidTransferId,
        description,
        JSON.stringify(metadata)
      ]
    );

    logger.info('Withdrawal hold created', {
      userId,
      amount,
      plaidTransferId,
      holdId: holdResult.insertId
    });

    return {
      success: true,
      holdId: holdResult.insertId,
      message: 'Withdrawal hold created successfully'
    };

  } catch (error) {
    logger.error('Error creating withdrawal hold', { userId, amount, plaidTransferId, error });
    return {
      success: false,
      message: 'Failed to create withdrawal hold'
    };
  }
}

/**
 * Create a pending deposit transaction
 * This doesn't add to balance until confirmed
 */
export async function createDepositPending(
  userId: number,
  amount: number,
  plaidTransferId: string,
  description: string,
  metadata: any = {}
): Promise<{ success: boolean; holdId?: number; message?: string }> {
  try {
    // Create the pending deposit record
    const holdResult = await executeUpdate(
      `INSERT INTO tbl_pending_holds 
       (user_id, amount, type, reference_id, plaid_transfer_id, status, description, meta_data, created_at, expires_at)
       VALUES (?, ?, 'deposit', ?, ?, 'pending', ?, ?, NOW(), DATE_ADD(NOW(), INTERVAL 7 DAY))`,
      [
        userId,
        amount,
        plaidTransferId,
        plaidTransferId,
        description,
        JSON.stringify(metadata)
      ]
    );

    logger.info('Deposit pending created', {
      userId,
      amount,
      plaidTransferId,
      holdId: holdResult.insertId
    });

    return {
      success: true,
      holdId: holdResult.insertId,
      message: 'Deposit pending created successfully'
    };

  } catch (error) {
    logger.error('Error creating deposit pending', { userId, amount, plaidTransferId, error });
    return {
      success: false,
      message: 'Failed to create deposit pending'
    };
  }
}

/**
 * Confirm a withdrawal hold - actually debit the wallet
 */
export async function confirmWithdrawalHold(
  plaidTransferId: string,
  plaidStatus: string
): Promise<{ success: boolean; message?: string }> {
  try {
    // Get the pending hold
    const hold = await executeQuerySingle(
      `SELECT * FROM tbl_pending_holds 
       WHERE plaid_transfer_id = ? AND type = 'withdrawal' AND status = 'pending'`,
      [plaidTransferId]
    );

    if (!hold) {
      return {
        success: false,
        message: 'Pending withdrawal hold not found'
      };
    }

    // Import wallet service
    const { getUserMasterWallet, updateWalletBalance } = await import('./walletService');

    // Get current wallet balance
    const wallet = await getUserMasterWallet(hold.user_id);
    if (!wallet) {
      return {
        success: false,
        message: 'Wallet not found'
      };
    }

    const currentBalance = parseFloat(wallet.balance.toString());
    const holdAmount = parseFloat(hold.amount);

    // Check if wallet still has sufficient balance
    if (currentBalance < holdAmount) {
      // Mark hold as failed due to insufficient balance
      await executeUpdate(
        `UPDATE tbl_pending_holds 
         SET status = 'failed', 
             meta_data = JSON_SET(meta_data, '$.failure_reason', 'Insufficient balance at confirmation'),
             updated_at = NOW()
         WHERE id = ?`,
        [hold.id]
      );

      return {
        success: false,
        message: 'Insufficient wallet balance at confirmation time'
      };
    }

    // Debit the wallet
    const newBalance = currentBalance - holdAmount;
    await updateWalletBalance(hold.user_id, newBalance);

    // Mark hold as confirmed
    await executeUpdate(
      `UPDATE tbl_pending_holds 
       SET status = 'confirmed', 
           meta_data = JSON_SET(meta_data, '$.plaid_status', ?, '$.confirmed_at', NOW()),
           updated_at = NOW()
       WHERE id = ?`,
      [plaidStatus, hold.id]
    );

    // Update the wallet transaction status
    await executeUpdate(
      `UPDATE tbl_wallet_transactions 
       SET status_id = '1',
           meta_data = JSON_SET(meta_data, '$.wallet_debited', NOW(), '$.plaid_status', ?),
           updated_at = NOW()
       WHERE reference_id = ?`,
      [plaidStatus, plaidTransferId]
    );

    logger.info('Withdrawal hold confirmed and wallet debited', {
      userId: hold.user_id,
      amount: holdAmount,
      newBalance,
      plaidTransferId
    });

    return {
      success: true,
      message: 'Withdrawal confirmed and wallet debited'
    };

  } catch (error) {
    logger.error('Error confirming withdrawal hold', { plaidTransferId, error });
    return {
      success: false,
      message: 'Failed to confirm withdrawal hold'
    };
  }
}

/**
 * Confirm a deposit - actually add money to wallet
 */
export async function confirmDeposit(
  plaidTransferId: string,
  plaidStatus: string
): Promise<{ success: boolean; message?: string }> {
  try {
    // Get the pending deposit
    const hold = await executeQuerySingle(
      `SELECT * FROM tbl_pending_holds 
       WHERE plaid_transfer_id = ? AND type = 'deposit' AND status = 'pending'`,
      [plaidTransferId]
    );

    if (!hold) {
      return {
        success: false,
        message: 'Pending deposit not found'
      };
    }

    // Import wallet service
    const { getUserMasterWallet, updateWalletBalance } = await import('./walletService');

    // Get current wallet balance
    const wallet = await getUserMasterWallet(hold.user_id);
    if (!wallet) {
      return {
        success: false,
        message: 'Wallet not found'
      };
    }

    const currentBalance = parseFloat(wallet.balance.toString());
    const depositAmount = parseFloat(hold.amount);

    // Add money to wallet
    const newBalance = currentBalance + depositAmount;
    await updateWalletBalance(hold.user_id, newBalance);

    // Mark deposit as confirmed
    await executeUpdate(
      `UPDATE tbl_pending_holds 
       SET status = 'confirmed', 
           meta_data = JSON_SET(meta_data, '$.plaid_status', ?, '$.confirmed_at', NOW()),
           updated_at = NOW()
       WHERE id = ?`,
      [plaidStatus, hold.id]
    );

    // Update the wallet transaction status
    await executeUpdate(
      `UPDATE tbl_wallet_transactions 
       SET status_id = '1',
           meta_data = JSON_SET(meta_data, '$.wallet_credited', NOW(), '$.plaid_status', ?),
           updated_at = NOW()
       WHERE reference_id = ?`,
      [plaidStatus, plaidTransferId]
    );

    logger.info('Deposit confirmed and wallet credited', {
      userId: hold.user_id,
      amount: depositAmount,
      newBalance,
      plaidTransferId
    });

    return {
      success: true,
      message: 'Deposit confirmed and wallet credited'
    };

  } catch (error) {
    logger.error('Error confirming deposit', { plaidTransferId, error });
    return {
      success: false,
      message: 'Failed to confirm deposit'
    };
  }
}

/**
 * Release a failed hold - for withdrawals that failed
 */
export async function releaseFailedHold(
  plaidTransferId: string,
  failureReason: string
): Promise<{ success: boolean; message?: string }> {
  try {
    // Mark hold as failed/released
    await executeUpdate(
      `UPDATE tbl_pending_holds 
       SET status = 'failed', 
           meta_data = JSON_SET(meta_data, '$.failure_reason', ?, '$.failed_at', NOW()),
           updated_at = NOW()
       WHERE plaid_transfer_id = ? AND status = 'pending'`,
      [failureReason, plaidTransferId]
    );

    // Update the wallet transaction status
    await executeUpdate(
      `UPDATE tbl_wallet_transactions 
       SET status_id = '3',
           meta_data = JSON_SET(meta_data, '$.failure_reason', ?),
           updated_at = NOW()
       WHERE reference_id = ?`,
      [failureReason, plaidTransferId]
    );

    logger.info('Failed hold released', { plaidTransferId, failureReason });

    return {
      success: true,
      message: 'Failed hold released'
    };

  } catch (error) {
    logger.error('Error releasing failed hold', { plaidTransferId, error });
    return {
      success: false,
      message: 'Failed to release hold'
    };
  }
}

/**
 * Get available wallet balance (actual balance minus pending withdrawal holds)
 */
export async function getAvailableWalletBalance(userId: number): Promise<number> {
  try {
    // Import wallet service
    const { getUserMasterWallet } = await import('./walletService');

    // Get actual wallet balance
    const wallet = await getUserMasterWallet(userId);
    if (!wallet) {
      return 0;
    }

    const actualBalance = parseFloat(wallet.balance.toString());

    // Get total pending withdrawal holds
    const pendingHolds = await executeQuerySingle(
      `SELECT COALESCE(SUM(amount), 0) as total_holds
       FROM tbl_pending_holds 
       WHERE user_id = ? AND type = 'withdrawal' AND status = 'pending'`,
      [userId]
    );

    const totalHolds = parseFloat(pendingHolds?.total_holds || 0);
    const availableBalance = actualBalance - totalHolds;

    logger.info('Available wallet balance calculated', {
      userId,
      actualBalance,
      totalHolds,
      availableBalance
    });

    return Math.max(0, availableBalance); // Never return negative

  } catch (error) {
    logger.error('Error calculating available wallet balance', { userId, error });
    return 0;
  }
}

/**
 * Get pending holds for a user
 */
export async function getUserPendingHolds(userId: number): Promise<PendingHold[]> {
  try {
    const holds = await executeQuery(
      `SELECT * FROM tbl_pending_holds 
       WHERE user_id = ? AND status = 'pending'
       ORDER BY created_at DESC`,
      [userId]
    );

    return Array.isArray(holds) ? holds : (holds ? [holds] : []);

  } catch (error) {
    logger.error('Error getting user pending holds', { userId, error });
    return [];
  }
}