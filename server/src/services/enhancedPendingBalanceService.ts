import { executeUpdate, executeQuery<PERSON>ingle, executeQuery } from '../utils/database';
import logger from '../utils/logger';

export interface WalletBalance {
  balance: number;
  pending_balance: number;
  blocked_balance: number;
  available_balance: number;
}

export interface PendingHold {
  id: number;
  user_id: number;
  amount: number;
  type: 'deposit' | 'withdrawal';
  reference_id: string;
  plaid_transfer_id?: string;
  status: 'pending' | 'confirmed' | 'failed' | 'released' | 'expired';
  description: string;
  meta_data: any;
  created_at: Date;
  updated_at: Date;
  expires_at?: Date;
  confirmed_at?: Date;
}

/**
 * Get comprehensive wallet balance including pending and blocked amounts
 */
export async function getEnhancedWalletBalance(userId: number): Promise<WalletBalance | null> {
  try {
    const wallet = await executeQuerySingle(
      `SELECT 
        balance,
        pending_balance,
        blocked_balance,
        available_balance
       FROM tbl_masterwallet 
       WHERE user_id = ?`,
      [userId]
    );

    if (!wallet) {
      return null;
    }

    return {
      balance: parseFloat(wallet.balance.toString()),
      pending_balance: parseFloat(wallet.pending_balance?.toString() || '0'),
      blocked_balance: parseFloat(wallet.blocked_balance?.toString() || '0'),
      available_balance: parseFloat(wallet.available_balance?.toString() || '0')
    };
  } catch (error) {
    logger.error('Error getting enhanced wallet balance', { userId, error });
    return null;
  }
}

/**
 * Create a pending deposit hold (for add money operations)
 * This creates a pending transaction without adding to the main balance
 */
export async function createPendingDeposit(
  userId: number,
  amount: number,
  plaidTransferId: string,
  description: string,
  metadata: any = {}
): Promise<{ success: boolean; holdId?: number; message?: string }> {
  try {
    // Create the pending hold record
    const holdResult = await executeUpdate(
      `INSERT INTO tbl_pending_holds 
       (user_id, amount, type, reference_id, plaid_transfer_id, status, description, meta_data, created_at, expires_at)
       VALUES (?, ?, 'deposit', ?, ?, 'pending', ?, ?, NOW(), DATE_ADD(NOW(), INTERVAL 7 DAY))`,
      [
        userId,
        amount,
        plaidTransferId,
        plaidTransferId,
        description,
        JSON.stringify(metadata)
      ]
    );

    // Update pending balance in wallet
    await executeUpdate(
      `UPDATE tbl_masterwallet 
       SET pending_balance = pending_balance + ? 
       WHERE user_id = ?`,
      [amount, userId]
    );

    logger.info('Pending deposit created', {
      userId,
      amount,
      plaidTransferId,
      holdId: holdResult.insertId
    });

    return {
      success: true,
      holdId: holdResult.insertId,
      message: 'Pending deposit created successfully'
    };

  } catch (error) {
    logger.error('Error creating pending deposit', { userId, amount, plaidTransferId, error });
    return {
      success: false,
      message: 'Failed to create pending deposit'
    };
  }
}

/**
 * Create a withdrawal hold (for withdraw and staff payment operations)
 * This blocks the amount from available balance without removing it from main balance
 */
export async function createWithdrawalHold(
  userId: number,
  amount: number,
  plaidTransferId: string,
  description: string,
  metadata: any = {}
): Promise<{ success: boolean; holdId?: number; message?: string }> {
  try {
    // Check if user has sufficient available balance
    const walletBalance = await getEnhancedWalletBalance(userId);
    if (!walletBalance || walletBalance.available_balance < amount) {
      return {
        success: false,
        message: 'Insufficient available balance'
      };
    }

    // Create the pending hold record
    const holdResult = await executeUpdate(
      `INSERT INTO tbl_pending_holds 
       (user_id, amount, type, reference_id, plaid_transfer_id, status, description, meta_data, created_at, expires_at)
       VALUES (?, ?, 'withdrawal', ?, ?, 'pending', ?, ?, NOW(), DATE_ADD(NOW(), INTERVAL 7 DAY))`,
      [
        userId,
        amount,
        plaidTransferId,
        plaidTransferId,
        description,
        JSON.stringify(metadata)
      ]
    );

    // Block the amount in wallet (increase blocked_balance)
    await executeUpdate(
      `UPDATE tbl_masterwallet 
       SET blocked_balance = blocked_balance + ? 
       WHERE user_id = ?`,
      [amount, userId]
    );

    logger.info('Withdrawal hold created', {
      userId,
      amount,
      plaidTransferId,
      holdId: holdResult.insertId
    });

    return {
      success: true,
      holdId: holdResult.insertId,
      message: 'Withdrawal hold created successfully'
    };

  } catch (error) {
    logger.error('Error creating withdrawal hold', { userId, amount, plaidTransferId, error });
    return {
      success: false,
      message: 'Failed to create withdrawal hold'
    };
  }
}

/**
 * Confirm a pending deposit (when Plaid confirms the transfer)
 * This moves money from pending to main balance
 */
export async function confirmPendingDeposit(
  plaidTransferId: string,
  plaidStatus: string = 'posted'
): Promise<{ success: boolean; message?: string; userId?: number; amount?: number }> {
  try {
    // Find the pending deposit
    const hold = await executeQuerySingle(
      `SELECT * FROM tbl_pending_holds 
       WHERE plaid_transfer_id = ? AND type = 'deposit' AND status = 'pending'`,
      [plaidTransferId]
    );

    if (!hold) {
      return {
        success: false,
        message: 'Pending deposit not found'
      };
    }

    const amount = parseFloat(hold.amount.toString());
    const userId = hold.user_id;

    // Update the hold status
    await executeUpdate(
      `UPDATE tbl_pending_holds 
       SET status = 'confirmed', confirmed_at = NOW(), updated_at = NOW()
       WHERE id = ?`,
      [hold.id]
    );

    // Move from pending to main balance
    await executeUpdate(
      `UPDATE tbl_masterwallet 
       SET balance = balance + ?, pending_balance = pending_balance - ?
       WHERE user_id = ?`,
      [amount, amount, userId]
    );

    // Update the wallet transaction status
    await executeUpdate(
      `UPDATE tbl_wallet_transactions 
       SET status_id = 'completed', plaid_status = ?, plaid_status_updated_at = NOW()
       WHERE reference_id = ? OR authorization_id = ?`,
      [plaidStatus, plaidTransferId, plaidTransferId]
    );

    logger.info('Pending deposit confirmed', {
      userId,
      amount,
      plaidTransferId,
      holdId: hold.id
    });

    return {
      success: true,
      message: 'Deposit confirmed successfully',
      userId,
      amount
    };

  } catch (error) {
    logger.error('Error confirming pending deposit', { plaidTransferId, error });
    return {
      success: false,
      message: 'Failed to confirm deposit'
    };
  }
}

/**
 * Confirm a withdrawal hold (when Plaid confirms the transfer)
 * This removes the blocked amount from both blocked and main balance
 */
export async function confirmWithdrawalHold(
  plaidTransferId: string,
  plaidStatus: string = 'posted'
): Promise<{ success: boolean; message?: string; userId?: number; amount?: number }> {
  try {
    // Find the pending withdrawal
    const hold = await executeQuerySingle(
      `SELECT * FROM tbl_pending_holds 
       WHERE plaid_transfer_id = ? AND type = 'withdrawal' AND status = 'pending'`,
      [plaidTransferId]
    );

    if (!hold) {
      return {
        success: false,
        message: 'Pending withdrawal not found'
      };
    }

    const amount = parseFloat(hold.amount.toString());
    const userId = hold.user_id;

    // Update the hold status
    await executeUpdate(
      `UPDATE tbl_pending_holds 
       SET status = 'confirmed', confirmed_at = NOW(), updated_at = NOW()
       WHERE id = ?`,
      [hold.id]
    );

    // Remove from both main balance and blocked balance
    await executeUpdate(
      `UPDATE tbl_masterwallet 
       SET balance = balance - ?, blocked_balance = blocked_balance - ?
       WHERE user_id = ?`,
      [amount, amount, userId]
    );

    // Update the wallet transaction status
    await executeUpdate(
      `UPDATE tbl_wallet_transactions 
       SET status_id = 'completed', plaid_status = ?, plaid_status_updated_at = NOW()
       WHERE reference_id = ? OR authorization_id = ?`,
      [plaidStatus, plaidTransferId, plaidTransferId]
    );

    logger.info('Withdrawal hold confirmed', {
      userId,
      amount,
      plaidTransferId,
      holdId: hold.id
    });

    return {
      success: true,
      message: 'Withdrawal confirmed successfully',
      userId,
      amount
    };

  } catch (error) {
    logger.error('Error confirming withdrawal hold', { plaidTransferId, error });
    return {
      success: false,
      message: 'Failed to confirm withdrawal'
    };
  }
}

/**
 * Release a failed hold (when Plaid transfer fails)
 * This reverses the hold and restores available balance
 */
export async function releaseFailedHold(
  plaidTransferId: string,
  failureReason: string = 'Transfer failed'
): Promise<{ success: boolean; message?: string; userId?: number; amount?: number }> {
  try {
    // Find the pending hold
    const hold = await executeQuerySingle(
      `SELECT * FROM tbl_pending_holds
       WHERE plaid_transfer_id = ? AND status = 'pending'`,
      [plaidTransferId]
    );

    if (!hold) {
      return {
        success: false,
        message: 'Pending hold not found'
      };
    }

    const amount = parseFloat(hold.amount.toString());
    const userId = hold.user_id;
    const holdType = hold.type;

    // Update the hold status
    await executeUpdate(
      `UPDATE tbl_pending_holds
       SET status = 'failed', updated_at = NOW()
       WHERE id = ?`,
      [hold.id]
    );

    // Reverse the hold based on type
    if (holdType === 'deposit') {
      // Remove from pending balance
      await executeUpdate(
        `UPDATE tbl_masterwallet
         SET pending_balance = pending_balance - ?
         WHERE user_id = ?`,
        [amount, userId]
      );
    } else if (holdType === 'withdrawal') {
      // Remove from blocked balance (restore available balance)
      await executeUpdate(
        `UPDATE tbl_masterwallet
         SET blocked_balance = blocked_balance - ?
         WHERE user_id = ?`,
        [amount, userId]
      );
    }

    // Update the wallet transaction status
    await executeUpdate(
      `UPDATE tbl_wallet_transactions
       SET status_id = 'failed', plaid_status = 'failed', failure_reason = ?, plaid_status_updated_at = NOW()
       WHERE reference_id = ? OR authorization_id = ?`,
      [failureReason, plaidTransferId, plaidTransferId]
    );

    logger.info('Failed hold released', {
      userId,
      amount,
      holdType,
      plaidTransferId,
      holdId: hold.id,
      failureReason
    });

    return {
      success: true,
      message: 'Failed hold released successfully',
      userId,
      amount
    };

  } catch (error) {
    logger.error('Error releasing failed hold', { plaidTransferId, failureReason, error });
    return {
      success: false,
      message: 'Failed to release hold'
    };
  }
}

/**
 * Get all pending holds for a user
 */
export async function getUserPendingHolds(userId: number): Promise<PendingHold[]> {
  try {
    const holds = await executeQuery(
      `SELECT * FROM tbl_pending_holds
       WHERE user_id = ? AND status = 'pending'
       ORDER BY created_at DESC`,
      [userId]
    );

    return holds.map(hold => ({
      ...hold,
      amount: parseFloat(hold.amount.toString()),
      meta_data: typeof hold.meta_data === 'string' ? JSON.parse(hold.meta_data) : hold.meta_data
    }));
  } catch (error) {
    logger.error('Error getting user pending holds', { userId, error });
    return [];
  }
}

/**
 * Clean up expired holds
 */
export async function cleanupExpiredHolds(): Promise<{ cleaned: number }> {
  try {
    // Find expired holds
    const expiredHolds = await executeQuery(
      `SELECT * FROM tbl_pending_holds
       WHERE status = 'pending' AND expires_at < NOW()`
    );

    let cleanedCount = 0;

    for (const hold of expiredHolds) {
      const amount = parseFloat(hold.amount.toString());
      const userId = hold.user_id;
      const holdType = hold.type;

      // Mark as expired
      await executeUpdate(
        `UPDATE tbl_pending_holds
         SET status = 'expired', updated_at = NOW()
         WHERE id = ?`,
        [hold.id]
      );

      // Reverse the hold
      if (holdType === 'deposit') {
        await executeUpdate(
          `UPDATE tbl_masterwallet
           SET pending_balance = pending_balance - ?
           WHERE user_id = ?`,
          [amount, userId]
        );
      } else if (holdType === 'withdrawal') {
        await executeUpdate(
          `UPDATE tbl_masterwallet
           SET blocked_balance = blocked_balance - ?
           WHERE user_id = ?`,
          [amount, userId]
        );
      }

      // Update transaction status
      await executeUpdate(
        `UPDATE tbl_wallet_transactions
         SET status_id = 'failed', failure_reason = 'Hold expired'
         WHERE reference_id = ? OR authorization_id = ?`,
        [hold.plaid_transfer_id, hold.plaid_transfer_id]
      );

      cleanedCount++;
    }

    logger.info('Expired holds cleaned up', { cleanedCount });

    return { cleaned: cleanedCount };
  } catch (error) {
    logger.error('Error cleaning up expired holds', { error });
    return { cleaned: 0 };
  }
}
