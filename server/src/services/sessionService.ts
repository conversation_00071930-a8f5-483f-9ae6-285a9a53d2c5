import { executeUpdate, executeQuerySingle, executeQuery } from '../utils/database';
import logger from '../utils/logger';

export interface UserSession {
  id: number;
  user_id: number;
  session_token: string; // This will be the JWT token itself
  user_agent: string;
  ip_address: string;
  is_valid: boolean;
  created_at: Date;
  expires_at: Date;
}

// Session configuration
export const SESSION_CONFIG = {
  EXPIRY_HOURS: 24
};

/**
 * Create a new user session using JW<PERSON> token as session key (invalidates existing sessions)
 * @param userId - User ID
 * @param jwtToken - JWT token to use as session key
 * @param userAgent - User agent string from request
 * @param ipAddress - IP address from request
 * @returns Promise<{sessionId: number, expiresAt: Date}>
 */
export async function createUserSession(
  userId: number,
  jwtToken: string,
  userAgent: string,
  ipAddress: string
): Promise<{sessionId: number, expiresAt: Date}> {
  try {
    // First, check existing active sessions for this user
    const existingSessions = await executeQuery<UserSession>(
      'SELECT id, session_token FROM tbl_user_sessions WHERE user_id = ? AND is_valid = 1',
      [userId]
    );

    logger.info('Checking existing sessions before creation', {
      userId,
      existingSessionCount: existingSessions.length
    });

    // Invalidate all existing sessions for this user (single session policy)
    const invalidatedCount = await invalidateUserSessions(userId);

    logger.info('Invalidated existing sessions', {
      userId,
      invalidatedCount,
      previousSessionCount: existingSessions.length
    });

    // Calculate expiry time
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + SESSION_CONFIG.EXPIRY_HOURS);

    // Insert new session using JWT token as session_token
    const result = await executeUpdate(
      `INSERT INTO tbl_user_sessions (user_id, session_token, user_agent, ip_address, is_valid, created_at, expires_at)
       VALUES (?, ?, ?, ?, 1, NOW(), ?)`,
      [userId, jwtToken, userAgent, ipAddress, expiresAt]
    );

    // Verify that only one active session exists for this user
    const sessionCountResult = await executeQuerySingle<{ count: number }>(
      'SELECT COUNT(*) as count FROM tbl_user_sessions WHERE user_id = ? AND is_valid = 1',
      [userId]
    );

    const activeSessionCount = sessionCountResult?.count || 0;

    logger.info('User session created with single session enforcement', {
      userId,
      sessionId: result.insertId,
      ipAddress,
      expiresAt,
      activeSessionsAfterCreation: activeSessionCount
    });

    // Ensure only one active session exists
    if (activeSessionCount > 1) {
      logger.warn('Multiple active sessions detected after creation - this should not happen', {
        userId,
        activeSessionCount
      });

      // Force cleanup of extra sessions if somehow multiple exist
      await executeUpdate(
        'UPDATE tbl_user_sessions SET is_valid = 0 WHERE user_id = ? AND id != ? AND is_valid = 1',
        [userId, result.insertId]
      );

      logger.info('Forced cleanup of extra sessions completed', { userId });
    }

    return {
      sessionId: result.insertId,
      expiresAt
    };
  } catch (error) {
    logger.error('Error creating user session', { userId, error });
    throw new Error('Failed to create user session');
  }
}

/**
 * Validate a JWT token as session key
 * @param jwtToken - JWT token to validate as session
 * @returns Promise<{isValid: boolean, session?: UserSession, error?: string}>
 */
export async function validateSession(
  jwtToken: string
): Promise<{isValid: boolean, session?: UserSession, error?: string}> {
  try {
    // Get session by JWT token
    const session = await executeQuerySingle<UserSession>(
      `SELECT * FROM tbl_user_sessions
       WHERE session_token = ? AND is_valid = 1
       ORDER BY created_at DESC LIMIT 1`,
      [jwtToken]
    );

    if (!session) {
      return { isValid: false, error: 'No active session found' };
    }

    // Check if session has expired
    const now = new Date();
    const expiresAt = new Date(session.expires_at);

    if (now > expiresAt) {
      // Mark session as invalid
      await invalidateSession(session.id);
      return { isValid: false, error: 'Session has expired' };
    }

    return { isValid: true, session };
  } catch (error) {
    logger.error('Error validating session', { jwtToken, error });
    return { isValid: false, error: 'Session validation failed' };
  }
}

/**
 * Invalidate a specific session
 * @param sessionId - Session ID to invalidate
 * @returns Promise<boolean>
 */
export async function invalidateSession(sessionId: number): Promise<boolean> {
  try {
    const result = await executeUpdate(
      'UPDATE tbl_user_sessions SET is_valid = 0 WHERE id = ?',
      [sessionId]
    );

    logger.info('Session invalidated', { sessionId });
    return result.affectedRows > 0;
  } catch (error) {
    logger.error('Error invalidating session', { sessionId, error });
    return false;
  }
}

/**
 * Invalidate all sessions for a user
 * @param userId - User ID
 * @returns Promise<number> - Number of sessions invalidated
 */
export async function invalidateUserSessions(userId: number): Promise<number> {
  try {
    const result = await executeUpdate(
      'UPDATE tbl_user_sessions SET is_valid = 0 WHERE user_id = ? AND is_valid = 1',
      [userId]
    );

    logger.info('User sessions invalidated', { userId, count: result.affectedRows });
    return result.affectedRows;
  } catch (error) {
    logger.error('Error invalidating user sessions', { userId, error });
    return 0;
  }
}

/**
 * Invalidate session by JWT token
 * @param jwtToken - JWT token used as session key to invalidate
 * @returns Promise<boolean>
 */
export async function invalidateSessionByToken(jwtToken: string): Promise<boolean> {
  try {
    const result = await executeUpdate(
      'UPDATE tbl_user_sessions SET is_valid = 0 WHERE session_token = ?',
      [jwtToken]
    );

    logger.info('Session invalidated by JWT token', { jwtToken });
    return result.affectedRows > 0;
  } catch (error) {
    logger.error('Error invalidating session by JWT token', { jwtToken, error });
    return false;
  }
}

/**
 * Get user's active sessions
 * @param userId - User ID
 * @returns Promise<UserSession[]>
 */
export async function getUserActiveSessions(userId: number): Promise<UserSession[]> {
  try {
    const sessions = await executeQuery<UserSession>(
      `SELECT * FROM tbl_user_sessions 
       WHERE user_id = ? AND is_valid = 1 AND expires_at > NOW() 
       ORDER BY created_at DESC`,
      [userId]
    );

    return sessions;
  } catch (error) {
    logger.error('Error getting user active sessions', { userId, error });
    return [];
  }
}

/**
 * Clean up expired sessions (utility function - can be called by a cron job)
 * @returns Promise<number> - Number of sessions cleaned up
 */
export async function cleanupExpiredSessions(): Promise<number> {
  try {
    const result = await executeUpdate(
      'UPDATE tbl_user_sessions SET is_valid = 0 WHERE expires_at <= NOW() AND is_valid = 1'
    );

    logger.info('Expired sessions cleaned up', { count: result.affectedRows });
    return result.affectedRows;
  } catch (error) {
    logger.error('Error cleaning up expired sessions', { error });
    return 0;
  }
}

/**
 * Update session activity (extend expiry) using JWT token
 * @param jwtToken - JWT token used as session key
 * @returns Promise<boolean>
 */
export async function updateSessionActivity(jwtToken: string): Promise<boolean> {
  try {
    const newExpiresAt = new Date();
    newExpiresAt.setHours(newExpiresAt.getHours() + SESSION_CONFIG.EXPIRY_HOURS);

    const result = await executeUpdate(
      'UPDATE tbl_user_sessions SET expires_at = ? WHERE session_token = ? AND is_valid = 1',
      [newExpiresAt, jwtToken]
    );

    return result.affectedRows > 0;
  } catch (error) {
    logger.error('Error updating session activity', { jwtToken, error });
    return false;
  }
}

/**
 * Get current active session count for a user
 * @param userId - User ID
 * @returns Promise<number> - Number of active sessions
 */
export async function getActiveSessionCount(userId: number): Promise<number> {
  try {
    const result = await executeQuerySingle<{ count: number }>(
      'SELECT COUNT(*) as count FROM tbl_user_sessions WHERE user_id = ? AND is_valid = 1 AND expires_at > NOW()',
      [userId]
    );

    const count = result?.count || 0;

    logger.info('Active session count check', { userId, activeSessionCount: count });

    return count;
  } catch (error) {
    logger.error('Error getting active session count', { userId, error });
    return 0;
  }
}

/**
 * Check if user has ever had any session (to determine if this is their first login)
 * @param userId - User ID
 * @returns Promise<boolean> - true if user has had sessions before, false if this is their first time
 */
export async function hasUserEverHadSession(userId: number): Promise<boolean> {
  try {
    const sessionCount = await executeQuerySingle<{ count: number }>(
      'SELECT COUNT(*) as count FROM tbl_user_sessions WHERE user_id = ?',
      [userId]
    );

    const hasHadSessions = sessionCount ? sessionCount.count > 0 : false;

    logger.info('User session history check', {
      userId,
      hasHadSessions,
      totalSessions: sessionCount?.count || 0
    });

    return hasHadSessions;
  } catch (error) {
    logger.error('Error checking user session history', { userId, error });
    // Return true as fallback to avoid sending welcome emails on error
    return true;
  }
}

/**
 * Enforce single session policy - ensure user has only one active session
 * @param userId - User ID
 * @returns Promise<boolean> - true if enforcement was successful
 */
export async function enforceSingleSessionPolicy(userId: number): Promise<boolean> {
  try {
    const activeCount = await getActiveSessionCount(userId);

    if (activeCount <= 1) {
      logger.info('Single session policy already enforced', { userId, activeCount });
      return true;
    }

    logger.warn('Multiple active sessions detected, enforcing single session policy', {
      userId,
      activeSessionCount: activeCount
    });

    // Get the most recent session
    const mostRecentSession = await executeQuerySingle<UserSession>(
      `SELECT * FROM tbl_user_sessions
       WHERE user_id = ? AND is_valid = 1 AND expires_at > NOW()
       ORDER BY created_at DESC LIMIT 1`,
      [userId]
    );

    if (!mostRecentSession) {
      logger.warn('No recent session found during enforcement', { userId });
      return false;
    }

    // Invalidate all other sessions except the most recent one
    const result = await executeUpdate(
      'UPDATE tbl_user_sessions SET is_valid = 0 WHERE user_id = ? AND id != ? AND is_valid = 1',
      [userId, mostRecentSession.id]
    );

    logger.info('Single session policy enforced', {
      userId,
      keptSessionId: mostRecentSession.id,
      invalidatedSessions: result.affectedRows
    });

    return true;
  } catch (error) {
    logger.error('Error enforcing single session policy', { userId, error });
    return false;
  }
}
