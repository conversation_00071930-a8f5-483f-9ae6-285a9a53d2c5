import crypto from 'crypto';
import { executeUpdate, executeQuery<PERSON>ingle } from '../utils/database';
import logger from '../utils/logger';
import { sendOTPEmail } from './emailService';

// OTP configuration
export const OTP_CONFIG = {
  LENGTH: 6,
  EXPIRY_MINUTES: 5,
  MAX_ATTEMPTS: 5,
  RESEND_COOLDOWN_MINUTES: 1
};

export type OTPType = 'login' | 'transaction' | 'password_reset' | 'email_verify' | 'phone_verify';

export interface OTPRecord {
  id: number;
  user_id: number;
  otp_code: string;
  otp_type: OTPType;
  is_used: boolean;
  created_at: Date;
  expires_at: Date;
}

/**
 * Generate a random OTP code
 * @param length - Length of the OTP (default: 6)
 * @returns string - Generated OTP
 */
export function generateOTPCode(length: number = OTP_CONFIG.LENGTH): string {
  const digits = '0123456789';
  let otp = '';
  for (let i = 0; i < length; i++) {
    otp += digits[crypto.randomInt(0, digits.length)];
  }
  return otp;
}

/**
 * Save OTP to database
 * @param userId - User ID
 * @param otpCode - Generated OTP code
 * @param otpType - Type of OTP
 * @param expiryMinutes - Expiry time in minutes
 * @returns Promise<number> - OTP record ID
 */
export async function saveOTP(
  userId: number, 
  otpCode: string, 
  otpType: OTPType,
  expiryMinutes: number = OTP_CONFIG.EXPIRY_MINUTES
): Promise<number> {
  const expiresAt = new Date();
  expiresAt.setMinutes(expiresAt.getMinutes() + expiryMinutes);

  const result = await executeUpdate(
    `INSERT INTO tbl_user_otps (user_id, otp_code, otp_type, is_used, created_at, expires_at) 
     VALUES (?, ?, ?, 0, NOW(), ?)`,
    [userId, otpCode, otpType, expiresAt]
  );

  logger.info('OTP saved to database', {
    userId,
    otpType,
    otpId: result.insertId,
    expiresAt
  });

  return result.insertId;
}

/**
 * Generate and save a new OTP
 * @param userId - User ID
 * @param otpType - Type of OTP
 * @returns Promise<{otpCode: string, otpId: number, expiresAt: Date}>
 */
export async function generateAndSaveOTP(
  userId: number,
  otpType: OTPType
): Promise<{otpCode: string, otpId: number, expiresAt: Date}> {
  const otpCode = generateOTPCode();
  const otpId = await saveOTP(userId, otpCode, otpType);

  const expiresAt = new Date();
  expiresAt.setMinutes(expiresAt.getMinutes() + OTP_CONFIG.EXPIRY_MINUTES);

  return { otpCode, otpId, expiresAt };
}

/**
 * Generate, save and send OTP via email
 * @param userId - User ID
 * @param email - User email address
 * @param otpType - Type of OTP
 * @returns Promise<{otpCode: string, otpId: number, expiresAt: Date, emailSent: boolean}>
 */
export async function generateAndSendOTP(
  userId: number,
  email: string,
  otpType: OTPType
): Promise<{otpCode: string, otpId: number, expiresAt: Date, emailSent: boolean}> {
  // Generate and save OTP
  const { otpCode, otpId, expiresAt } = await generateAndSaveOTP(userId, otpType);

  // Send OTP via email
  const emailSent = await sendOTP(userId, otpCode, 'email', email, otpType);

  return { otpCode, otpId, expiresAt, emailSent };
}

/**
 * Verify OTP code
 * @param userId - User ID
 * @param otpCode - OTP code to verify
 * @param otpType - Type of OTP
 * @returns Promise<{isValid: boolean, otpRecord?: OTPRecord, error?: string}>
 */
export async function verifyOTPCode(
  userId: number, 
  otpCode: string, 
  otpType: OTPType
): Promise<{isValid: boolean, otpRecord?: OTPRecord, error?: string}> {
  try {
    // Get the latest unused OTP for the user
    const otpRecord = await executeQuerySingle<OTPRecord>(
      `SELECT * FROM tbl_user_otps 
       WHERE user_id = ? AND otp_code = ? AND otp_type = ? AND is_used = 0 
       ORDER BY created_at DESC LIMIT 1`,
      [userId, otpCode, otpType]
    );

    if (!otpRecord) {
      return { isValid: false, error: 'Invalid OTP code' };
    }

    // Check if OTP has expired
    const now = new Date();
    const expiresAt = new Date(otpRecord.expires_at);
    
    if (now > expiresAt) {
      return { isValid: false, error: 'OTP has expired' };
    }

    return { isValid: true, otpRecord };
  } catch (error) {
    logger.error('Error verifying OTP', { userId, otpType, error });
    return { isValid: false, error: 'OTP verification failed' };
  }
}

/**
 * Mark OTP as used
 * @param otpId - OTP record ID
 * @returns Promise<boolean>
 */
export async function markOTPAsUsed(otpId: number): Promise<boolean> {
  try {
    const result = await executeUpdate(
      'UPDATE tbl_user_otps SET is_used = 1 WHERE id = ?',
      [otpId]
    );

    const success = result.affectedRows > 0;
    
    if (success) {
      logger.info('OTP marked as used', { otpId });
    } else {
      logger.warn('Failed to mark OTP as used - record not found', { otpId });
    }

    return success;
  } catch (error) {
    logger.error('Error marking OTP as used', { otpId, error });
    return false;
  }
}

/**
 * Invalidate all unused OTPs for a user and type
 * @param userId - User ID
 * @param otpType - Type of OTP
 * @returns Promise<number> - Number of invalidated OTPs
 */
export async function invalidateUserOTPs(userId: number, otpType: OTPType): Promise<number> {
  try {
    const result = await executeUpdate(
      `UPDATE tbl_user_otps 
       SET is_used = 1 
       WHERE user_id = ? AND otp_type = ? AND is_used = 0`,
      [userId, otpType]
    );

    logger.info('User OTPs invalidated', { 
      userId, 
      otpType, 
      invalidatedCount: result.affectedRows 
    });

    return result.affectedRows;
  } catch (error) {
    logger.error('Error invalidating user OTPs', { userId, otpType, error });
    return 0;
  }
}

/**
 * Clean up expired OTPs
 * @returns Promise<number> - Number of deleted OTPs
 */
export async function cleanupExpiredOTPs(): Promise<number> {
  try {
    const result = await executeUpdate(
      'DELETE FROM tbl_user_otps WHERE expires_at < NOW()',
      []
    );

    logger.info('Expired OTPs cleaned up', { deletedCount: result.affectedRows });
    return result.affectedRows;
  } catch (error) {
    logger.error('Error cleaning up expired OTPs', { error });
    return 0;
  }
}

/**
 * Get OTP statistics for a user
 * @param userId - User ID
 * @param otpType - Type of OTP
 * @returns Promise<{total: number, used: number, expired: number, active: number}>
 */
export async function getOTPStats(
  userId: number, 
  otpType: OTPType
): Promise<{total: number, used: number, expired: number, active: number}> {
  try {
    const stats = await executeQuerySingle<{
      total: number;
      used: number;
      expired: number;
      active: number;
    }>(
      `SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN is_used = 1 THEN 1 ELSE 0 END) as used,
        SUM(CASE WHEN expires_at < NOW() AND is_used = 0 THEN 1 ELSE 0 END) as expired,
        SUM(CASE WHEN expires_at >= NOW() AND is_used = 0 THEN 1 ELSE 0 END) as active
       FROM tbl_user_otps 
       WHERE user_id = ? AND otp_type = ?`,
      [userId, otpType]
    );

    return stats || { total: 0, used: 0, expired: 0, active: 0 };
  } catch (error) {
    logger.error('Error getting OTP stats', { userId, otpType, error });
    return { total: 0, used: 0, expired: 0, active: 0 };
  }
}

/**
 * Check if user can request a new OTP (rate limiting)
 * @param userId - User ID
 * @param otpType - Type of OTP
 * @returns Promise<{canRequest: boolean, waitMinutes?: number}>
 */
export async function canRequestOTP(
  userId: number, 
  otpType: OTPType
): Promise<{canRequest: boolean, waitMinutes?: number}> {
  try {
    const lastOTP = await executeQuerySingle<{created_at: Date}>(
      `SELECT created_at FROM tbl_user_otps 
       WHERE user_id = ? AND otp_type = ? 
       ORDER BY created_at DESC LIMIT 1`,
      [userId, otpType]
    );

    if (!lastOTP) {
      return { canRequest: true };
    }

    const now = new Date();
    const lastCreated = new Date(lastOTP.created_at);
    const minutesSinceLastOTP = (now.getTime() - lastCreated.getTime()) / (1000 * 60);

    if (minutesSinceLastOTP < OTP_CONFIG.RESEND_COOLDOWN_MINUTES) {
      const waitMinutes = Math.ceil(OTP_CONFIG.RESEND_COOLDOWN_MINUTES - minutesSinceLastOTP);
      return { canRequest: false, waitMinutes };
    }

    return { canRequest: true };
  } catch (error) {
    logger.error('Error checking OTP request eligibility', { userId, otpType, error });
    return { canRequest: true }; // Allow request on error to avoid blocking users
  }
}

/**
 * Send OTP via SMS/Email
 * @param userId - User ID
 * @param otpCode - OTP code
 * @param deliveryMethod - 'sms' or 'email'
 * @param recipient - Phone number or email address
 * @param otpType - Type of OTP (optional, defaults to 'login')
 * @returns Promise<boolean>
 */
export async function sendOTP(
  userId: number,
  otpCode: string,
  deliveryMethod: 'sms' | 'email',
  recipient: string,
  otpType: OTPType = 'login'
): Promise<boolean> {
  try {
    let deliverySuccess = false;

    if (deliveryMethod === 'email') {
      // Send OTP via email
      deliverySuccess = await sendOTPEmail(recipient, otpCode, otpType);
    } else if (deliveryMethod === 'sms') {
      // TODO: Implement SMS sending (e.g., using Twilio)
      logger.warn('SMS delivery not implemented yet', { userId, recipient });
      deliverySuccess = false;
    }

    if (deliverySuccess) {
      logger.info('OTP sent successfully', {
        userId,
        deliveryMethod,
        recipient,
        otpType,
        otpCode: process.env.NODE_ENV === 'development' ? otpCode : '[HIDDEN]'
      });
    } else {
      logger.error('Failed to send OTP', {
        userId,
        deliveryMethod,
        recipient,
        otpType
      });
    }

    return deliverySuccess;
  } catch (error) {
    logger.error('Error sending OTP', {
      userId,
      deliveryMethod,
      recipient,
      otpType,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return false;
  }
}
