import { executeQuery, executeUpdate } from '../utils/database';
import { 
  confirmPendingDeposit, 
  confirmWithdrawalHold, 
  releaseFailedHold,
  cleanupExpiredHolds 
} from './enhancedPendingBalanceService';
import { getTransferStatus } from './plaidTransferService';
import logger from '../utils/logger';

/**
 * Sync all pending transfers with Plaid to catch any missed webhook events
 */
export async function syncAllPendingTransfers(): Promise<{ 
  synced: number; 
  confirmed: number; 
  failed: number; 
  errors: number; 
}> {
  try {
    logger.info('Starting sync of all pending transfers');

    // Get all pending holds that haven't been confirmed or failed
    const pendingHolds = await executeQuery(
      `SELECT * FROM tbl_pending_holds 
       WHERE status = 'pending' 
       AND plaid_transfer_id IS NOT NULL 
       AND created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)
       ORDER BY created_at ASC`
    );

    let synced = 0;
    let confirmed = 0;
    let failed = 0;
    let errors = 0;

    for (const hold of pendingHolds) {
      try {
        const transferId = hold.plaid_transfer_id;
        
        // Get current status from Plaid
        const statusResult = await getTransferStatus(transferId);
        
        if (!statusResult.success) {
          logger.warn('Failed to get transfer status from Plaid', {
            transferId,
            holdId: hold.id,
            error: statusResult.message
          });
          errors++;
          continue;
        }

        synced++;

        // Process based on status
        const plaidStatus = statusResult.status;
        
        if (plaidStatus === 'posted' || plaidStatus === 'settled') {
          // Transfer completed successfully
          if (hold.type === 'deposit') {
            const result = await confirmPendingDeposit(transferId, plaidStatus);
            if (result.success) {
              confirmed++;
              logger.info('Deposit confirmed via sync', {
                transferId,
                holdId: hold.id,
                userId: result.userId,
                amount: result.amount
              });
            }
          } else if (hold.type === 'withdrawal') {
            const result = await confirmWithdrawalHold(transferId, plaidStatus);
            if (result.success) {
              confirmed++;
              logger.info('Withdrawal confirmed via sync', {
                transferId,
                holdId: hold.id,
                userId: result.userId,
                amount: result.amount
              });
            }
          }
        } else if (plaidStatus === 'failed' || plaidStatus === 'cancelled' || plaidStatus === 'returned') {
          // Transfer failed
          const result = await releaseFailedHold(transferId, statusResult.failureReason || 'Transfer failed');
          if (result.success) {
            failed++;
            logger.info('Failed hold released via sync', {
              transferId,
              holdId: hold.id,
              userId: result.userId,
              amount: result.amount,
              failureReason: statusResult.failureReason
            });
          }
        }

        // Record status history
        await recordTransferStatusHistory(transferId, plaidStatus, statusResult.failureReason);

      } catch (error) {
        logger.error('Error syncing individual transfer', {
          transferId: hold.plaid_transfer_id,
          holdId: hold.id,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        errors++;
      }
    }

    // Also cleanup expired holds
    const cleanupResult = await cleanupExpiredHolds();

    logger.info('Pending transfers sync completed', {
      totalPending: pendingHolds.length,
      synced,
      confirmed,
      failed,
      errors,
      expiredCleaned: cleanupResult.cleaned
    });

    return { synced, confirmed, failed, errors };

  } catch (error) {
    logger.error('Error syncing pending transfers', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return { synced: 0, confirmed: 0, failed: 0, errors: 1 };
  }
}

/**
 * Sync a specific transfer by ID
 */
export async function syncTransferById(transferId: string): Promise<{
  success: boolean;
  status?: string;
  message?: string;
}> {
  try {
    logger.info('Syncing specific transfer', { transferId });

    // Get current status from Plaid
    const statusResult = await getTransferStatus(transferId);
    
    if (!statusResult.success) {
      return {
        success: false,
        message: statusResult.message || 'Failed to get transfer status from Plaid'
      };
    }

    const plaidStatus = statusResult.status;

    // Find the pending hold
    const hold = await executeQuery(
      `SELECT * FROM tbl_pending_holds 
       WHERE plaid_transfer_id = ? AND status = 'pending'`,
      [transferId]
    );

    if (hold.length === 0) {
      return {
        success: false,
        message: 'No pending hold found for this transfer'
      };
    }

    const holdRecord = hold[0];

    // Process based on status
    if (plaidStatus === 'posted' || plaidStatus === 'settled') {
      // Transfer completed successfully
      if (holdRecord.type === 'deposit') {
        const result = await confirmPendingDeposit(transferId, plaidStatus);
        if (result.success) {
          logger.info('Deposit confirmed via manual sync', {
            transferId,
            userId: result.userId,
            amount: result.amount
          });
        }
      } else if (holdRecord.type === 'withdrawal') {
        const result = await confirmWithdrawalHold(transferId, plaidStatus);
        if (result.success) {
          logger.info('Withdrawal confirmed via manual sync', {
            transferId,
            userId: result.userId,
            amount: result.amount
          });
        }
      }
    } else if (plaidStatus === 'failed' || plaidStatus === 'cancelled' || plaidStatus === 'returned') {
      // Transfer failed
      const result = await releaseFailedHold(transferId, statusResult.failureReason || 'Transfer failed');
      if (result.success) {
        logger.info('Failed hold released via manual sync', {
          transferId,
          userId: result.userId,
          amount: result.amount,
          failureReason: statusResult.failureReason
        });
      }
    }

    // Record status history
    await recordTransferStatusHistory(transferId, plaidStatus, statusResult.failureReason);

    return {
      success: true,
      status: plaidStatus,
      message: `Transfer status synced: ${plaidStatus}`
    };

  } catch (error) {
    logger.error('Error syncing specific transfer', {
      transferId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return {
      success: false,
      message: 'Failed to sync transfer status'
    };
  }
}

/**
 * Record transfer status history for audit purposes
 */
async function recordTransferStatusHistory(
  transferId: string, 
  status: string, 
  failureReason?: string
): Promise<void> {
  try {
    await executeUpdate(
      `INSERT INTO tbl_plaid_transfer_status_history 
       (plaid_transfer_id, status, failure_reason, status_timestamp, webhook_received_at, metadata)
       VALUES (?, ?, ?, NOW(), NOW(), ?)`,
      [
        transferId,
        status,
        failureReason || null,
        JSON.stringify({ source: 'sync_service', timestamp: new Date().toISOString() })
      ]
    );
  } catch (error) {
    logger.warn('Failed to record transfer status history', {
      transferId,
      status,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

/**
 * Get sync statistics
 */
export async function getSyncStatistics(): Promise<{
  pendingHolds: number;
  recentSyncs: number;
  failedTransfers: number;
  expiredHolds: number;
}> {
  try {
    const [pendingHolds, recentSyncs, failedTransfers, expiredHolds] = await Promise.all([
      executeQuery(`SELECT COUNT(*) as count FROM tbl_pending_holds WHERE status = 'pending'`),
      executeQuery(`SELECT COUNT(*) as count FROM tbl_plaid_transfer_status_history WHERE webhook_received_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)`),
      executeQuery(`SELECT COUNT(*) as count FROM tbl_pending_holds WHERE status = 'failed' AND updated_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)`),
      executeQuery(`SELECT COUNT(*) as count FROM tbl_pending_holds WHERE status = 'expired'`)
    ]);

    return {
      pendingHolds: pendingHolds[0]?.count || 0,
      recentSyncs: recentSyncs[0]?.count || 0,
      failedTransfers: failedTransfers[0]?.count || 0,
      expiredHolds: expiredHolds[0]?.count || 0
    };
  } catch (error) {
    logger.error('Error getting sync statistics', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return {
      pendingHolds: 0,
      recentSyncs: 0,
      failedTransfers: 0,
      expiredHolds: 0
    };
  }
}
