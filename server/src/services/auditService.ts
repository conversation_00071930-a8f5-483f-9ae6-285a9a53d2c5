import { executeUpdate, executeQuery } from '../utils/database';
import logger from '../utils/logger';
import { maskSensitiveData } from '../models/auditLog';

/**
 * Log bank account operations for audit purposes
 * @param action - Action performed (e.g., 'add', 'replace', 'delete')
 * @param staffId - Staff ID
 * @param organizerId - Organizer ID
 * @param details - Additional details about the action
 */
export async function logBankAccountOperation(
  action: string,
  staffId: string,
  organizerId: number,
  details: Record<string, any>
): Promise<void> {
  try {
    // Mask sensitive information
    const sanitizedDetails = { ...details };
    
    // Mask account number and routing number if present
    if (sanitizedDetails.accountNumber) {
      sanitizedDetails.accountNumber = `****${sanitizedDetails.accountNumber.slice(-4)}`;
    }
    if (sanitizedDetails.routingNumber) {
      sanitizedDetails.routingNumber = `****${sanitizedDetails.routingNumber.slice(-4)}`;
    }
    
    // Add timestamp
    sanitizedDetails.timestamp = new Date().toISOString();
    
    // Log to console
    logger.info(`Bank account ${action} operation`, {
      action,
      staffId,
      organizerId,
      ...sanitizedDetails
    });
    
    // Store in database
    await executeUpdate(
      `INSERT INTO tbl_audit_logs
       (user_id, action_type, reference_table, reference_id, details, created_at)
       VALUES (?, ?, ?, ?, ?, NOW())`,
      [
        organizerId,
        `bank_account_${action}`,
        'staff_bank_account',
        parseInt(staffId),
        JSON.stringify(sanitizedDetails)
      ]
    );
  } catch (error) {
    // Don't fail the operation if logging fails
    logger.error('Failed to log bank account operation', { action, staffId, organizerId, error });
  }
}

/**
 * Get audit logs for a specific entity
 * @param entityType - Type of entity
 * @param entityId - ID of the entity
 * @param limit - Maximum number of logs to return
 * @param offset - Offset for pagination
 * @returns Promise<any[]> - Array of audit logs
 */
export async function getAuditLogs(
  entityType: string,
  entityId: string,
  limit: number = 50,
  offset: number = 0
): Promise<any[]> {
  try {
    const logs = await executeQuery(
      `SELECT * FROM tbl_audit_logs
       WHERE reference_table = ? AND reference_id = ?
       ORDER BY created_at DESC
       LIMIT ? OFFSET ?`,
      [entityType, parseInt(entityId), limit, offset]
    );

    return Array.isArray(logs) ? logs : (logs ? [logs] : []);
  } catch (error) {
    logger.error('Error getting audit logs', { entityType, entityId, error });
    return [];
  }
}

/**
 * Log transaction events for audit purposes
 * @param eventType - Type of event (e.g., 'transfer_authorization_created', 'transfer_created', 'transfer_status_updated')
 * @param resourceId - ID of the resource (e.g., transfer ID, authorization ID)
 * @param userId - User ID associated with the transaction (0 if not applicable)
 * @param data - Additional data about the event
 * @param oldStatus - Previous status of the transaction (optional)
 * @param newStatus - New status of the transaction (optional)
 * @returns Promise<number | null> - ID of the created audit log or null if failed
 */
export async function logTransactionEvent(
  eventType: string,
  resourceId: string,
  userId: number,
  data: Record<string, any>,
  oldStatus?: string,
  newStatus?: string
): Promise<number | null> {
  try {
    // Mask sensitive information using the helper function from models/auditLog.ts
    const sanitizedData = maskSensitiveData({ ...data });
    
    // Add status change information if provided
    if (oldStatus && newStatus) {
      sanitizedData.statusChange = {
        from: oldStatus,
        to: newStatus,
        changedAt: new Date().toISOString()
      };
    }
    
    // Add transaction metadata
    sanitizedData.metadata = {
      ...sanitizedData.metadata,
      eventType,
      timestamp: new Date().toISOString(),
      transactionId: resourceId,
      userId
    };
    
    // Add source and destination information if available
    if (sanitizedData.sourceType) {
      sanitizedData.metadata.source = {
        type: sanitizedData.sourceType,
        id: sanitizedData.sourceId
      };
    }
    
    if (sanitizedData.destinationType) {
      sanitizedData.metadata.destination = {
        type: sanitizedData.destinationType,
        id: sanitizedData.destinationId
      };
    }
    
    // Log to console with appropriate level based on event type
    if (eventType.includes('error') || eventType.includes('failed')) {
      logger.error(`Transaction event: ${eventType}`, {
        eventType,
        resourceId,
        userId,
        status: newStatus || sanitizedData.status,
        error: sanitizedData.error || 'Unknown error'
      });
    } else if (eventType.includes('warning')) {
      logger.warn(`Transaction event: ${eventType}`, {
        eventType,
        resourceId,
        userId,
        status: newStatus || sanitizedData.status
      });
    } else {
      logger.info(`Transaction event: ${eventType}`, {
        eventType,
        resourceId,
        userId,
        status: newStatus || sanitizedData.status
      });
    }
    
    // Store in database (handle userId = 0 case to avoid foreign key constraint)
    const effectiveUserId = userId === 0 ? 1 : userId; // Use system user ID 1 instead of 0

    const result = await executeUpdate(
      `INSERT INTO tbl_audit_logs
       (user_id, action_type, reference_table, reference_id, details, created_at)
       VALUES (?, ?, ?, ?, ?, NOW())`,
      [
        effectiveUserId,
        eventType,
        'transaction',
        0, // reference_id should be an integer, using 0 as default
        JSON.stringify(sanitizedData)
      ]
    );
    
    return result && result.insertId ? result.insertId : null;
  } catch (error) {
    // Don't fail the operation if logging fails
    logger.error('Failed to log transaction event', { eventType, resourceId, userId, error });
    return null;
  }
}

/**
 * Log balance changes for audit purposes
 * @param userId - User ID
 * @param oldBalance - Previous balance
 * @param newBalance - New balance
 * @param reason - Reason for the balance change
 * @param transferId - Associated transfer ID (optional)
 * @param transactionType - Type of transaction causing the balance change (optional)
 * @param metadata - Additional metadata about the balance change (optional)
 * @returns Promise<number | null> - ID of the created audit log or null if failed
 */
export async function logBalanceChange(
  userId: number,
  oldBalance: number,
  newBalance: number,
  reason: string,
  transferId?: string,
  transactionType?: string,
  metadata?: Record<string, any>
): Promise<number | null> {
  try {
    const timestamp = new Date().toISOString();
    const difference = newBalance - oldBalance;
    const changeDirection = difference > 0 ? 'increase' : difference < 0 ? 'decrease' : 'no_change';
    
    const details = {
      oldBalance,
      newBalance,
      difference,
      changeDirection,
      reason,
      transferId,
      transactionType,
      timestamp,
      metadata: {
        ...metadata,
        walletId: userId.toString(),
        changeAmount: Math.abs(difference),
        currency: 'USD', // Assuming USD is the default currency
      }
    };
    
    // Log to console with appropriate level based on context
    if (reason.includes('error') || reason.includes('failed')) {
      logger.warn(`Balance change for user ${userId}`, {
        userId,
        oldBalance,
        newBalance,
        difference,
        reason,
        transferId
      });
    } else {
      logger.info(`Balance change for user ${userId}`, {
        userId,
        oldBalance,
        newBalance,
        difference,
        reason,
        transferId
      });
    }
    
    // Store in database
    const result = await executeUpdate(
      `INSERT INTO tbl_audit_logs
       (user_id, action_type, reference_table, reference_id, details, created_at)
       VALUES (?, ?, ?, ?, ?, NOW())`,
      [
        userId,
        'balance_change',
        'wallet',
        userId,
        JSON.stringify(details)
      ]
    );
    
    return result && result.insertId ? result.insertId : null;
  } catch (error) {
    // Don't fail the operation if logging fails
    logger.error('Failed to log balance change', { userId, oldBalance, newBalance, reason, error });
    return null;
  }
}

/**
 * Log webhook events for audit purposes
 * @param webhookType - Type of webhook
 * @param webhookCode - Webhook code (e.g., 'TRANSFER_CREATED', 'TRANSFER_PENDING')
 * @param rawData - Raw webhook data
 * @param processedResult - Result of processing the webhook
 * @param verified - Whether the webhook signature was verified
 * @returns Promise<number | null> - ID of the created audit log or null if failed
 */
export async function logWebhookEvent(
  webhookType: string,
  webhookCode: string,
  rawData: any,
  processedResult: any = null,
  verified: boolean = false
): Promise<number | null> {
  try {
    // Generate a unique ID for the webhook event
    const eventId = rawData.webhook_id || 
                   rawData.event_id || 
                   `webhook_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    
    // Extract relevant IDs from the webhook data
    const transferId = rawData?.transfer_id || 
                      rawData?.data?.transfer_id || 
                      processedResult?.transferId || 
                      'unknown';
    
    const itemId = rawData?.item_id || 
                  rawData?.data?.item_id || 
                  null;
    
    const accountId = rawData?.account_id || 
                     rawData?.data?.account_id || 
                     null;
    
    const status = rawData?.new_transfer_status || 
                  rawData?.new_status || 
                  rawData?.status || 
                  null;
    
    // Create a sanitized copy of the raw data for logging
    const sanitizedRawData = maskSensitiveData({ ...rawData });
    
    // Prepare detailed metadata
    const metadata = {
      webhookType,
      webhookCode,
      eventId,
      transferId,
      itemId,
      accountId,
      status,
      verified,
      timestamp: new Date().toISOString(),
      processingStatus: processedResult ? 'processed' : 'pending'
    };
    
    // Log to console with appropriate level based on verification status
    if (!verified) {
      logger.warn(`Unverified webhook received: ${webhookType} - ${webhookCode}`, {
        eventId,
        transferId,
        status
      });
    } else {
      logger.info(`Webhook received: ${webhookType} - ${webhookCode}`, {
        eventId,
        transferId,
        status,
        processedResult: processedResult ? 'success' : 'pending'
      });
    }
    
    // Store in database
    const result = await executeUpdate(
      `INSERT INTO tbl_audit_logs
       (user_id, action_type, reference_table, reference_id, details, created_at)
       VALUES (?, ?, ?, ?, ?, NOW())`,
      [
        0, // No specific user ID for webhooks
        `webhook_${webhookType}_${webhookCode}`,
        'webhook',
        0, // reference_id should be an integer, using 0 as default
        JSON.stringify({
          metadata,
          rawData: sanitizedRawData,
          processedResult,
          timestamp: new Date().toISOString()
        })
      ]
    );
    
    // Also store the webhook event in the webhook_events table using the webhookEventService
    // This is done in addition to the audit log to maintain a separate record specifically for webhooks
    try {
      const webhookEventService = require('../services/webhookEventService');
      await webhookEventService.storeWebhookEvent(
        webhookType,
        webhookCode,
        rawData,
        processedResult,
        verified
      );
    } catch (webhookStoreError) {
      logger.error('Failed to store webhook event in webhook_events table', { 
        webhookType, 
        webhookCode,
        eventId,
        error: webhookStoreError 
      });
    }
    
    return result && result.insertId ? result.insertId : null;
  } catch (error) {
    // Don't fail the operation if logging fails
    logger.error('Failed to log webhook event', { webhookType, error });
    return null;
  }
}