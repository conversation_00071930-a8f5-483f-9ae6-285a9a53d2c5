import express from 'express';
import { getEntityAuditLogs, getStaffBankAccountAuditLogs } from '../controllers/auditController';
import { authenticateToken } from '../middlewares/auth';

const router = express.Router();

// Protect all audit routes with authentication
router.use(authenticateToken);

// Get audit logs for a specific entity
router.get('/entity/:entityType/:entityId', getEntityAuditLogs);

// Get bank account audit logs for a staff member
router.get('/staff/:staffId/bank-accounts', getStaffBankAccountAuditLogs);

export default router;