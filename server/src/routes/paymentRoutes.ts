import { Router, Request, Response, NextFunction, RequestHandler } from 'express';
import { getAllPayments, getAwaitingFitersbyUserId, getAwaitingPayments, getAwaitingPaymentsbyTeamId,  getMyDues, getDuesSummary, sendReminderForPaymentPending, getPlatformDues, getPlatformDuesSummary, getPlatformDuesDetail, getAllPaymentTotals } from '../controllers/paymentController';
import { authenticateTokenAndSession } from '../middlewares/sessionAuth';

const router = Router();


router.use(authenticateTokenAndSession);
router.get('/', getAllPayments as RequestHandler);
router.post('/awaiting-payment',getAwaitingPayments);
router.get('/awaiting-payment-summary',getAllPaymentTotals);

router.post('/awaiting-team-payments/:id',getAwaitingPaymentsbyTeamId);
router.get('/get-filters',getAwaitingFitersbyUserId);
router.post('/send-reminder-email',sendReminderForPaymentPending);
router.get('/staff-dues', getMyDues);
router.get('/staff-dues-summary', getDuesSummary);
router.get('/platform-dues', getPlatformDues);
router.get('/platform-due/teams/:groupId', getPlatformDuesDetail);

router.get('/platform-dues-summary', getPlatformDuesSummary);





export default router;
