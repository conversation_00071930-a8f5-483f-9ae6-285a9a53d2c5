import { Router } from 'express';
import {
  getPaymentMethodsController,
  getPaymentMethodByIdController,
  getWithdrawalStepsController,
  getWithdrawalProgressController,
  getUserWithdrawalProgressController,
  updateWithdrawalStatusController,
  addWithdrawalStepController,
  progressWithdrawalStepController
} from '../controllers/paymentMethodController';
import { authenticateToken } from '../middlewares/auth';

const router = Router();

// Public routes (no authentication required)
router.get('/payment-methods', getPaymentMethodsController); // Get all payment methods
router.get('/payment-methods/:methodId', getPaymentMethodByIdController); // Get specific payment method
router.get('/withdrawal-steps', getWithdrawalStepsController); // Get withdrawal steps template

// Protected routes (authentication required)
router.get('/withdrawal-progress/:transactionId', authenticateToken, getWithdrawalProgressController); // Get progress for specific transaction
router.get('/user-withdrawal-progress', authenticateToken, getUserWithdrawalProgressController); // Get user's withdrawal history with progress

// Admin routes (for webhook/admin use - consider adding admin authentication)
router.post('/withdrawal-status/update', updateWithdrawalStatusController); // Update withdrawal status (success/fail/reverse/etc)
router.post('/withdrawal-step/add', addWithdrawalStepController); // Add custom withdrawal step
router.post('/withdrawal-step/progress', progressWithdrawalStepController); // Progress withdrawal to next step

export default router;
