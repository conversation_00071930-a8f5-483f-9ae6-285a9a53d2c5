import { Router, Request, Response, NextFunction } from 'express';
import {
  createTransfer,
  getTransferStatusController,
  syncTransfers,
  createStaffTransfer,
  createWithdrawal,
  createTransferAuthorizationController
} from '../controllers/plaidTransferController';
import { authenticateTokenAndSession } from '../middlewares/sessionAuth';

const router = Router();

// Create transfer from bank to wallet (add money)
router.post('/transfers', authenticateTokenAndSession, (req: Request, res: Response, next: NextFunction) => {
  createTransfer(req, res).catch(next);
});

// Create withdrawal from wallet to bank
router.post('/withdrawals', authenticateTokenAndSession, (req: Request, res: Response, next: NextFunction) => {
  createWithdrawal(req, res).catch(next);
});

// Get transfer status
router.get('/transfers/:transferId', authenticateTokenAndSession, (req: Request, res: Response, next: NextFunction) => {
  getTransferStatusController(req, res).catch(next);
});

// Sync pending transfers
router.post('/transfers/sync', authenticateTokenAndSession, (req: Request, res: Response, next: NextFunction) => {
  syncTransfers(req, res).catch(next);
});

// Create transfer authorization
router.post('/transfer-authorizations', authenticateTokenAndSession, (req: Request, res: Response, next: NextFunction) => {
  createTransferAuthorizationController(req, res).catch(next);
});

// Create staff transfer - DISABLED to prevent duplicate transactions
// Use /api/staff/transfer-to-bank instead
// router.post('/staff-transfers', authenticateTokenAndSession, (req: Request, res: Response, next: NextFunction) => {
//   createStaffTransfer(req, res).catch(next);
// });

export default router;