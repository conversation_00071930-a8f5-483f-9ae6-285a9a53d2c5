import express from 'express';
import { 
  syncPendingTransfers,
  syncSpecificTransfer,
  getWalletStatus,
  getTransferStatusById,
  getSystemSyncStats,
  forceCleanupExpiredHolds
} from '../controllers/plaidStatusController';
import { authenticateToken } from '../middlewares/auth';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

/**
 * @route POST /api/plaid-status/sync
 * @desc Manually sync all pending transfers
 * @access Private
 */
router.post('/sync', syncPendingTransfers);

/**
 * @route POST /api/plaid-status/sync/:transferId
 * @desc Sync a specific transfer by ID
 * @access Private
 */
router.post('/sync/:transferId', syncSpecificTransfer);

/**
 * @route GET /api/plaid-status/wallet
 * @desc Get enhanced wallet status including pending balances
 * @access Private
 */
router.get('/wallet', getWalletStatus);

/**
 * @route GET /api/plaid-status/transfer/:transferId
 * @desc Get transfer status by ID
 * @access Private
 */
router.get('/transfer/:transferId', getTransferStatusById);

/**
 * @route GET /api/plaid-status/system-stats
 * @desc Get system sync statistics (admin only)
 * @access Private (Admin)
 */
router.get('/system-stats', getSystemSyncStats);

/**
 * @route POST /api/plaid-status/cleanup-expired
 * @desc Force cleanup of expired holds (admin only)
 * @access Private (Admin)
 */
router.post('/cleanup-expired', forceCleanupExpiredHolds);

export default router;
