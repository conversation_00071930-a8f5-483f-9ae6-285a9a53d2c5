import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { validateSession, updateSessionActivity } from '../services/sessionService';
import { sendUnauthorized } from '../utils/response';
import logger from '../utils/logger';
import dotenv from 'dotenv';

dotenv.config();

// Extend the Request interface to include session information
declare global {
  namespace Express {
    interface Request {
      userId?: string;
      team_connect_user_id?: string;
      sessionToken?: string;
      sessionId?: number;
    }
  }
}

/**
 * Middleware to authenticate JWT token and validate session using the JWT token itself as session key
 * This replaces the basic authenticateToken middleware for routes that need session validation
 */
export const authenticateTokenAndSession = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return sendUnauthorized(res, 'Authorization token required');
    }

    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      logger.error('JWT secret not configured');
      res.status(500).json({ message: 'Server configuration error' });
      return;
    }

    // Verify JWT token
    jwt.verify(token, jwtSecret, async (err: any, user: any) => {
      if (err) {
        logger.warn('Invalid JWT token', { error: err.message });
        return sendUnauthorized(res, 'Invalid token');
      }

      if (typeof user !== 'object' || user === null || !('id' in user)) {
        logger.warn('Invalid token payload', { user });
        return sendUnauthorized(res, 'Invalid token payload');
      }

      // Extract user information from JWT
      const userId = (user as { id: string }).id;
      const parentUserId = (user as { team_connect_user_id?: string }).team_connect_user_id;

      // Use the JWT token itself as the session key for validation
      const sessionValidation = await validateSession(token);

      if (!sessionValidation.isValid || !sessionValidation.session) {
        logger.warn('Invalid session', {
          userId,
          error: sessionValidation.error
        });
        return sendUnauthorized(res, sessionValidation.error || 'Invalid session');
      }

      // Verify that the session belongs to the user from the JWT
      if (sessionValidation.session.user_id.toString() !== userId.toString()) {
        logger.warn('Session user mismatch', {
          jwtUserId: userId,
          sessionUserId: sessionValidation.session.user_id
        });
        return sendUnauthorized(res, 'Session user mismatch');
      }

      // Update session activity (extend expiry) using JWT token
      await updateSessionActivity(token);

      // Set request properties
      req.userId = userId;
      req.team_connect_user_id = parentUserId;
      req.sessionToken = token; // The JWT token itself
      req.sessionId = sessionValidation.session.id;

      logger.info('Authentication successful', {
        userId,
        sessionId: sessionValidation.session.id,
        parentUserId
      });

      next();
    });
  } catch (error) {
    logger.error('Authentication error', { error });
    res.status(500).json({ message: 'Authentication failed' });
  }
};

/**
 * Middleware for routes that only need JWT validation (like verify-otp, resend-otp)
 * This is used for routes that don't require session validation
 */
export const authenticateTokenOnly = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return sendUnauthorized(res, 'Authorization token required');
  }

  const jwtSecret = process.env.JWT_SECRET;
  if (!jwtSecret) {
    logger.error('JWT secret not configured');
    res.status(500).json({ message: 'Server configuration error' });
    return;
  }

  jwt.verify(token, jwtSecret, (err: any, user: any) => {
    if (err) {
      logger.warn('Invalid JWT token', { error: err.message });
      return sendUnauthorized(res, 'Invalid token');
    }

    if (typeof user !== 'object' || user === null || !('id' in user)) {
      logger.warn('Invalid token payload', { user });
      return sendUnauthorized(res, 'Invalid token payload');
    }

    // Set request properties
    req.userId = (user as { id: string }).id;
    req.team_connect_user_id = (user as { team_connect_user_id?: string }).team_connect_user_id;
    req.sessionToken = token; // The JWT token itself

    next();
  });
};

/**
 * Middleware to check if user has an active session (optional check)
 * This can be used for routes that want to know about session status but don't require it
 */
export const checkSessionStatus = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const jwtToken = req.sessionToken; // This is the JWT token itself

    if (jwtToken) {
      const sessionValidation = await validateSession(jwtToken);
      if (sessionValidation.isValid && sessionValidation.session) {
        req.sessionId = sessionValidation.session.id;
        logger.info('Active session found', {
          userId: req.userId,
          sessionId: sessionValidation.session.id
        });
      }
    }

    next();
  } catch (error) {
    logger.error('Session status check error', { error });
    // Don't fail the request, just continue without session info
    next();
  }
};
