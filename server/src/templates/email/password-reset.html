<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Password Reset - Pay Connect</title>
  <style>
    body { 
      font-family: Arial, sans-serif; 
      line-height: 1.6; 
      color: #333; 
      margin: 0; 
      padding: 0; 
      background-color: #f4f4f4; 
    }
    .container { 
      max-width: 600px; 
      margin: 0 auto; 
      padding: 20px; 
      background-color: #ffffff; 
    }
    .header { 
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
      color: white; 
      padding: 30px; 
      text-align: center; 
      border-radius: 10px 10px 0 0; 
    }
    .header h1 {
      margin: 0;
      font-size: 28px;
      font-weight: bold;
    }
    .header p {
      margin: 10px 0 0 0;
      font-size: 16px;
      opacity: 0.9;
    }
    .content { 
      background: #f9f9f9; 
      padding: 30px; 
      border-radius: 0 0 10px 10px; 
    }
    .content h2 {
      color: #333;
      margin-top: 0;
    }
    .reset-info {
      background: #fff;
      padding: 20px;
      border-radius: 8px;
      margin: 20px 0;
      border-left: 4px solid #ff6b6b;
    }
    .otp-code { 
      background: #fff; 
      border: 2px dashed #667eea; 
      padding: 20px; 
      text-align: center; 
      margin: 20px 0; 
      border-radius: 8px; 
    }
    .otp-number { 
      font-size: 32px; 
      font-weight: bold; 
      color: #667eea; 
      letter-spacing: 8px; 
      margin: 0;
    }
    .warning { 
      background: #fff3cd; 
      border: 1px solid #ffeaa7; 
      padding: 15px; 
      border-radius: 5px; 
      margin: 20px 0; 
    }
    .warning strong {
      color: #856404;
    }
    .security-notice {
      background: #f8d7da;
      border: 1px solid #f5c6cb;
      padding: 15px;
      border-radius: 5px;
      margin: 20px 0;
      color: #721c24;
    }
    .footer { 
      text-align: center; 
      margin-top: 30px; 
      color: #666; 
      font-size: 14px; 
    }
    .footer p {
      margin: 5px 0;
    }
    .footer small {
      color: #999;
    }
    @media only screen and (max-width: 600px) {
      .container {
        padding: 10px;
      }
      .header, .content {
        padding: 20px;
      }
      .otp-number {
        font-size: 24px;
        letter-spacing: 4px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🔐 Pay Connect</h1>
      <p>Password Reset Request</p>
    </div>
    <div class="content">
      <h2>Password Reset Verification</h2>
      
      <div class="reset-info">
        <p><strong>🔒 Security Alert:</strong> A password reset was requested for your Pay Connect account.</p>
        <p>If you requested this reset, use the verification code below to proceed. If you didn't request this, please ignore this email.</p>
      </div>
      
      <p>Your password reset verification code is:</p>
      
      <div class="otp-code">
        <div class="otp-number">{{OTP_CODE}}</div>
      </div>
      
      <div class="warning">
        <strong>⏰ Important:</strong> This code will expire in <strong>5 minutes</strong> for security reasons.
      </div>
      
      <div class="security-notice">
        <strong>🛡️ Security Reminder:</strong>
        <ul style="margin: 10px 0; padding-left: 20px;">
          <li>Never share this code with anyone</li>
          <li>Pay Connect will never ask for this code via phone or email</li>
          <li>If you didn't request this reset, please secure your account immediately</li>
        </ul>
      </div>
      
      <p>After entering this code, you'll be able to create a new secure password for your account.</p>
      
      <div class="footer">
        <p>Best regards,<br><strong>Pay Connect Security Team</strong></p>
        <p><small>This is an automated security message. Please do not reply to this email.</small></p>
      </div>
    </div>
  </div>
</body>
</html>
