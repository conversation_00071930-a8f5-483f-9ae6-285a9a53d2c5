<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Welcome to Pay Connect</title>
  <style>
    body { 
      font-family: Arial, sans-serif; 
      line-height: 1.6; 
      color: #333; 
      margin: 0; 
      padding: 0; 
      background-color: #f4f4f4; 
    }
    .container { 
      max-width: 600px; 
      margin: 0 auto; 
      padding: 20px; 
      background-color: #ffffff; 
    }
    .header { 
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
      color: white; 
      padding: 40px 30px; 
      text-align: center; 
      border-radius: 10px 10px 0 0; 
    }
    .header h1 {
      margin: 0;
      font-size: 32px;
      font-weight: bold;
    }
    .header p {
      margin: 15px 0 0 0;
      font-size: 18px;
      opacity: 0.9;
    }
    .content { 
      background: #f9f9f9; 
      padding: 40px 30px; 
      border-radius: 0 0 10px 10px; 
    }
    .content h2 {
      color: #333;
      margin-top: 0;
      font-size: 24px;
    }
    .welcome-message {
      background: #fff;
      padding: 25px;
      border-radius: 8px;
      margin: 20px 0;
      border-left: 4px solid #667eea;
    }
    .features {
      background: #fff;
      padding: 25px;
      border-radius: 8px;
      margin: 20px 0;
    }
    .features h3 {
      color: #667eea;
      margin-top: 0;
    }
    .features ul {
      padding-left: 20px;
    }
    .features li {
      margin: 8px 0;
    }
    .cta-button {
      display: inline-block;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 15px 30px;
      text-decoration: none;
      border-radius: 25px;
      font-weight: bold;
      margin: 20px 0;
      text-align: center;
    }
    .footer { 
      text-align: center; 
      margin-top: 30px; 
      color: #666; 
      font-size: 14px; 
    }
    .footer p {
      margin: 5px 0;
    }
    .footer small {
      color: #999;
    }
    @media only screen and (max-width: 600px) {
      .container {
        padding: 10px;
      }
      .header, .content {
        padding: 20px;
      }
      .header h1 {
        font-size: 24px;
      }
      .header p {
        font-size: 16px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🎉 Welcome to Pay Connect!</h1>
      <p>Your digital wallet journey starts here</p>
    </div>
    <div class="content">
      <h2>Hello {{USER_NAME}}!</h2>
      
      <div class="welcome-message">
        <p>Welcome to Pay Connect! We're excited to have you join our community of users who trust us with their digital payments and wallet management.</p>
        <p>Your account has been successfully created and you're ready to start using all our features.</p>
      </div>
      
      <div class="features">
        <h3>🚀 What you can do with Pay Connect:</h3>
        <ul>
          <li><strong>💰 Wallet Management:</strong> Add money, track transactions, and manage your digital wallet</li>
          <li><strong>🔒 Secure Transactions:</strong> Send and receive money with bank-level security</li>
          <li><strong>📊 Transaction History:</strong> View detailed reports of all your activities</li>
          <li><strong>🏦 Bank Integration:</strong> Connect your bank accounts securely</li>
          <li><strong>📱 Mobile Friendly:</strong> Access your wallet anywhere, anytime</li>
        </ul>
      </div>
      
      <div style="text-align: center;">
        <a href="{{DASHBOARD_URL}}" class="cta-button">Get Started Now</a>
      </div>
      
      <p>If you have any questions or need assistance, our support team is here to help. Simply reply to this email or contact us through the app.</p>
      
      <div class="footer">
        <p>Best regards,<br><strong>Pay Connect Team</strong></p>
        <p><small>This is an automated message. You can reply to this email for support.</small></p>
      </div>
    </div>
  </div>
</body>
</html>
