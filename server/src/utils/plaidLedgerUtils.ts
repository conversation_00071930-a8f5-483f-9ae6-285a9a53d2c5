import { plaidClient } from '../config/plaidConfig';
import logger from './logger';

export interface PlaidLedgerBalance {
  available: string;
  pending: string;
}

export interface PlaidLedgerStatus {
  success: boolean;
  balance?: PlaidLedgerBalance;
  message?: string;
}

/**
 * Get Plaid Ledger balance
 */
export async function getPlaidLedgerBalance(): Promise<PlaidLedgerStatus> {
  try {
    const response = await plaidClient.transferLedgerGet({});
    
    return {
      success: true,
      balance: {
        available: response.data.balance.available,
        pending: response.data.balance.pending
      }
    };
  } catch (error: any) {
    logger.error('Error getting Plaid Ledger balance', { error: error.message });
    return {
      success: false,
      message: 'Failed to check Plaid Ledger balance'
    };
  }
}

/**
 * Check if Plaid Ledger has sufficient funds for a transfer
 */
export async function checkPlaidLedgerFunds(requiredAmount: number): Promise<{
  sufficient: boolean;
  availableBalance: number;
  message?: string;
}> {
  try {
    const ledgerStatus = await getPlaidLedgerBalance();
    
    if (!ledgerStatus.success || !ledgerStatus.balance) {
      return {
        sufficient: false,
        availableBalance: 0,
        message: 'Unable to check Plaid Ledger balance'
      };
    }

    const availableBalance = parseFloat(ledgerStatus.balance.available);
    const sufficient = availableBalance >= requiredAmount;

    logger.info('Plaid Ledger funds check', {
      requiredAmount,
      availableBalance,
      sufficient
    });

    return {
      sufficient,
      availableBalance,
      message: sufficient 
        ? 'Sufficient funds available'
        : `Insufficient funds. Available: $${availableBalance.toFixed(2)}, Required: $${requiredAmount.toFixed(2)}`
    };

  } catch (error: any) {
    logger.error('Error checking Plaid Ledger funds', { error: error.message, requiredAmount });
    return {
      sufficient: false,
      availableBalance: 0,
      message: 'Error checking Plaid Ledger funds'
    };
  }
}

/**
 * Fund Plaid Ledger (for sandbox testing)
 */
export async function fundPlaidLedger(amount: number): Promise<{
  success: boolean;
  message: string;
  depositId?: string;
}> {
  try {
    const response = await plaidClient.transferLedgerDeposit({
      idempotency_key: `fund_ledger_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      amount: amount.toFixed(2),
      network: 'ach' as any
    });

    logger.info('Plaid Ledger funded', {
      amount,
      depositId: response.data.sweep?.id,
      status: response.data.sweep?.status
    });

    return {
      success: true,
      message: `Plaid Ledger funded with $${amount.toFixed(2)}`,
      depositId: response.data.sweep?.id
    };

  } catch (error: any) {
    logger.error('Error funding Plaid Ledger', { error: error.message, amount });
    return {
      success: false,
      message: `Failed to fund Plaid Ledger: ${error.message}`
    };
  }
}