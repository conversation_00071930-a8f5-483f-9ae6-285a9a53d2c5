/**
 * User-friendly messages for Pay Connect API responses
 * These messages are designed to be engaging, payment-focused, and user-centric
 */

export interface UserMessage {
  success: string;
  error: string;
  loading?: string;
  tip?: string;
}

export const USER_MESSAGES = {
  // Authentication & Login
  AUTH: {
    LOGIN_SUCCESS: {
      success: "Welcome back! You're now securely logged into your Pay Connect account",
      error: "We couldn't log you in right now. Please check your credentials and try again",
      tip: "Keep your login credentials safe and never share them with anyone"
    },
    FIRST_TIME_LOGIN: {
      success: "🎉 Welcome to Pay Connect! Your digital wallet journey starts here",
      error: "We're having trouble setting up your account. Please try again in a moment",
      tip: "Complete your wallet setup to start managing your money securely"
    },
    OTP_SENT: {
      success: "Security code sent! Check your email and enter the 6-digit code to continue",
      error: "We couldn't send your security code right now. Please try again",
      loading: "Sending your security code...",
      tip: "The code expires in 10 minutes for your security"
    },
    OTP_VERIFIED: {
      success: "Security code verified! You're all set to access your wallet",
      error: "That security code isn't quite right. Please check and try again",
      tip: "Request a new code if yours has expired"
    },
    OTP_RESEND: {
      success: "New security code sent! Check your email for the latest code",
      error: "We couldn't send a new code right now. Please wait a moment and try again",
      tip: "Only the latest code will work - previous codes are automatically cancelled"
    },
    SESSION_EXPIRED: {
      success: "You've been securely logged out",
      error: "Your session has expired for security. Please log in again to continue",
      tip: "We automatically log you out after 24 hours to keep your money safe"
    },
    LOGOUT: {
      success: "You've been safely logged out. Your wallet is secure!",
      error: "There was an issue logging you out. Please close your browser for security",
      tip: "Always log out when using shared devices"
    }
  },

  // Wallet Management
  WALLET: {
    CREATED: {
      success: "🎉 Your digital wallet is ready! You can now add money and make payments",
      error: "We couldn't create your wallet right now. Please try again in a moment",
      loading: "Setting up your secure digital wallet...",
      tip: "Your wallet is protected with bank-level security"
    },
    ALREADY_EXISTS: {
      success: "Your wallet is already set up and ready to use!",
      error: "You already have a wallet. Contact support if you need help accessing it",
      tip: "One wallet per account keeps your money organized and secure"
    },
    INFO_RETRIEVED: {
      success: "Here's your wallet overview - everything looks good!",
      error: "We couldn't load your wallet details right now. Please refresh and try again",
      loading: "Loading your wallet information...",
      tip: "Check your wallet regularly to track your spending and savings"
    },
    BALANCE_RETRIEVED: {
      success: "Your current balance is up to date",
      error: "We couldn't fetch your latest balance. Please try refreshing",
      loading: "Getting your latest balance...",
      tip: "Your balance updates instantly with every transaction"
    },
    NO_WALLET: {
      success: "Ready to create your wallet? Let's get started!",
      error: "You don't have a wallet yet. Create one to start managing your money",
      tip: "Creating a wallet takes less than a minute"
    }
  },

  // PIN Management
  PIN: {
    SETUP_SUCCESS: {
      success: "🔐 Your wallet PIN is set! Your money is now extra secure",
      error: "We couldn't set up your PIN right now. Please try again",
      loading: "Securing your wallet with your PIN...",
      tip: "Choose a PIN you'll remember but others can't guess"
    },
    SETUP_REQUIRED: {
      success: "Almost done! Set up your 4-digit PIN to secure your wallet",
      error: "You need to set up a PIN before you can use your wallet",
      tip: "Your PIN protects your money from unauthorized access"
    },
    VERIFIED: {
      success: "PIN verified! You can now proceed with your transaction",
      error: "That PIN isn't correct. Please try again carefully",
      tip: "Take your time entering your PIN to avoid mistakes"
    },
    CHANGE_OTP_SENT: {
      success: "Security code sent! Enter it along with your new PIN to complete the change",
      error: "We couldn't send your security code for PIN change. Please try again",
      loading: "Sending security code for PIN change...",
      tip: "Changing your PIN regularly helps keep your wallet secure"
    },
    CHANGED: {
      success: "🔐 Your PIN has been updated! Your wallet is now secured with your new PIN",
      error: "We couldn't change your PIN right now. Please try again",
      loading: "Updating your wallet security...",
      tip: "Remember your new PIN - you'll need it for all transactions"
    },
    ALREADY_SET: {
      success: "Your wallet PIN is already configured and working",
      error: "Your PIN is already set. Use 'Change PIN' if you want to update it",
      tip: "Contact support if you've forgotten your PIN"
    }
  },

  // Money Management
  MONEY: {
    ADDED: {
      success: "💰 Money added successfully! Your wallet balance has been updated",
      error: "We couldn't add money to your wallet right now. Please try again",
      loading: "Adding money to your wallet...",
      tip: "Added money is available immediately for payments and transfers"
    },
    WITHDRAWN: {
      success: "💸 Money withdrawn successfully! Funds are being transferred to your bank",
      error: "We couldn't process your withdrawal right now. Please try again",
      loading: "Processing your withdrawal...",
      tip: "Bank transfers usually take 1-3 business days to complete"
    },
    INSUFFICIENT_BALANCE: {
      success: "Add more money to your wallet to complete this transaction",
      error: "You don't have enough money in your wallet for this transaction",
      tip: "Add money from your linked bank account instantly"
    },
    INVALID_AMOUNT: {
      success: "Please enter a valid amount to continue",
      error: "The amount you entered isn't valid. Please check and try again",
      tip: "Minimum transaction amount is $1.00"
    }
  },

  // Transactions
  TRANSACTIONS: {
    RETRIEVED: {
      success: "Here's your transaction history - all your money movements in one place",
      error: "We couldn't load your transactions right now. Please refresh and try again",
      loading: "Loading your transaction history...",
      tip: "Keep track of your spending to better manage your budget"
    },
    NO_TRANSACTIONS: {
      success: "No transactions yet - your wallet is ready for your first payment!",
      error: "You haven't made any transactions yet",
      tip: "Start by adding money to your wallet or making your first payment"
    },
    PAYMENT_SUCCESS: {
      success: "💳 Payment completed! Your transaction was processed successfully",
      error: "We couldn't process your payment right now. Please try again",
      loading: "Processing your payment...",
      tip: "You'll receive a confirmation email for this transaction"
    },
    PAYMENT_FAILED: {
      success: "Please try your payment again",
      error: "Your payment couldn't be processed. Please check your details and try again",
      tip: "Make sure you have sufficient balance and your PIN is correct"
    }
  },

  // Bank Account
  BANK: {
    LINKED: {
      success: "🏦 Bank account linked! You can now add money directly to your wallet",
      error: "We couldn't link your bank account right now. Please try again",
      loading: "Securely connecting your bank account...",
      tip: "Your bank details are encrypted and never stored in plain text"
    },
    NOT_LINKED: {
      success: "Link a bank account to easily add money to your wallet",
      error: "You need to link a bank account before you can add money",
      tip: "Linking your bank account is secure and takes just a few minutes"
    },
    PRIMARY_SET: {
      success: "Primary bank account updated! This account will be used for transactions",
      error: "We couldn't update your primary bank account. Please try again",
      tip: "You can change your primary account anytime in settings"
    }
  },

  // Dashboard
  DASHBOARD: {
    LOADED: {
      success: "Welcome to your Pay Connect dashboard! Everything is up to date",
      error: "We couldn't load your dashboard right now. Please refresh the page",
      loading: "Loading your financial overview...",
      tip: "Your dashboard shows real-time information about your wallet and transactions"
    },
    STATS_UPDATED: {
      success: "Your financial stats are current and looking good!",
      error: "We couldn't update your stats right now. Please try refreshing",
      loading: "Updating your financial statistics...",
      tip: "Track your spending patterns to make better financial decisions"
    }
  },

  // Security
  SECURITY: {
    SECURE_CONNECTION: {
      success: "🔒 Your connection is secure and your data is protected",
      error: "Security check failed. Please ensure you're on a secure connection",
      tip: "Always look for the lock icon in your browser when using Pay Connect"
    },
    SUSPICIOUS_ACTIVITY: {
      success: "We're monitoring your account for any unusual activity",
      error: "We detected unusual activity. Please verify your identity",
      tip: "Contact support immediately if you notice any unauthorized transactions"
    },
    ACCOUNT_LOCKED: {
      success: "Your account is temporarily secured",
      error: "Your account has been locked for security. Please contact support",
      tip: "Account locks help protect your money from unauthorized access"
    }
  },

  // General System
  SYSTEM: {
    MAINTENANCE: {
      success: "We're improving Pay Connect! Service will resume shortly",
      error: "Pay Connect is temporarily unavailable for maintenance",
      loading: "System maintenance in progress...",
      tip: "Maintenance helps us keep your money safe and the service running smoothly"
    },
    RATE_LIMITED: {
      success: "Please wait a moment before trying again",
      error: "You're making requests too quickly. Please slow down for security",
      tip: "Rate limits help protect against fraud and keep your account secure"
    },
    NETWORK_ERROR: {
      success: "Please check your internet connection and try again",
      error: "Network connection issue. Please check your internet and retry",
      tip: "A stable internet connection ensures your transactions are processed safely"
    },
    UNKNOWN_ERROR: {
      success: "Something unexpected happened, but your money is safe",
      error: "We encountered an unexpected issue. Please try again or contact support",
      tip: "If problems persist, our support team is here to help 24/7"
    }
  }
};

/**
 * Get user-friendly message based on context and type
 */
export function getUserMessage(
  category: keyof typeof USER_MESSAGES,
  action: string,
  type: 'success' | 'error' | 'loading' | 'tip' = 'success'
): string {
  const categoryMessages = USER_MESSAGES[category];
  const actionMessages = categoryMessages?.[action as keyof typeof categoryMessages] as UserMessage;
  
  if (!actionMessages) {
    return type === 'success' 
      ? "Operation completed successfully"
      : "Something went wrong. Please try again";
  }

  return actionMessages[type] || actionMessages.success || "Operation completed";
}

/**
 * Get contextual tip for user guidance
 */
export function getUserTip(category: keyof typeof USER_MESSAGES, action: string): string | undefined {
  return getUserMessage(category, action, 'tip');
}

/**
 * Format amount for user display
 */
export function formatAmount(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2
  }).format(amount);
}

/**
 * Get encouraging message based on wallet balance
 */
export function getBalanceMessage(balance: number): string {
  if (balance === 0) {
    return "Ready to add your first funds? Your wallet is waiting!";
  } else if (balance < 10) {
    return "Consider adding more funds to avoid running low during transactions";
  } else if (balance < 100) {
    return "You're doing great! Keep building your wallet balance";
  } else {
    return "Excellent! You have a healthy wallet balance";
  }
}

export default {
  USER_MESSAGES,
  getUserMessage,
  getUserTip,
  formatAmount,
  getBalanceMessage
};
