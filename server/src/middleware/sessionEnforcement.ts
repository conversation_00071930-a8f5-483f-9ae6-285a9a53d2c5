import { Request, Response, NextFunction } from 'express';
import { getActiveSessionCount, enforceSingleSessionPolicy } from '../services/sessionService';
import logger from '../utils/logger';

/**
 * Middleware to monitor and enforce single session policy
 * This middleware checks for multiple active sessions and logs warnings
 */
export async function sessionEnforcementMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    // Only check for authenticated requests that have user info
    const userId = (req as any).user?.id;
    
    if (!userId) {
      return next();
    }

    // Check active session count
    const activeSessionCount = await getActiveSessionCount(userId);
    
    // Log if multiple sessions detected
    if (activeSessionCount > 1) {
      logger.warn('Multiple active sessions detected in middleware', {
        userId,
        activeSessionCount,
        endpoint: req.path,
        method: req.method,
        userAgent: req.headers['user-agent'],
        ipAddress: req.ip || req.socket.remoteAddress
      });

      // Optionally enforce single session policy
      if (process.env.ENFORCE_SINGLE_SESSION === 'true') {
        logger.info('Enforcing single session policy via middleware', { userId });
        await enforceSingleSessionPolicy(userId);
      }
    }

    next();
  } catch (error) {
    logger.error('Error in session enforcement middleware', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: (req as any).user?.id
    });
    
    // Don't block the request if middleware fails
    next();
  }
}

/**
 * Middleware specifically for login endpoints to ensure single session
 */
export async function loginSessionEnforcementMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    // This middleware runs after successful authentication
    const userId = (req as any).user?.id;
    
    if (!userId) {
      return next();
    }

    logger.info('Login session enforcement check', {
      userId,
      endpoint: req.path,
      userAgent: req.headers['user-agent'],
      ipAddress: req.ip || req.socket.remoteAddress
    });

    // Always enforce single session on login
    const enforcementResult = await enforceSingleSessionPolicy(userId);
    
    if (!enforcementResult) {
      logger.error('Failed to enforce single session policy during login', { userId });
    }

    // Get final session count
    const finalSessionCount = await getActiveSessionCount(userId);
    
    if (finalSessionCount > 1) {
      logger.error('CRITICAL: Multiple sessions still exist after login enforcement', {
        userId,
        finalSessionCount
      });
    } else {
      logger.info('Single session policy successfully enforced during login', {
        userId,
        finalSessionCount
      });
    }

    next();
  } catch (error) {
    logger.error('Error in login session enforcement middleware', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: (req as any).user?.id
    });
    
    // Don't block the request if middleware fails
    next();
  }
}

/**
 * Express middleware to add session monitoring headers to responses
 */
export function sessionMonitoringHeaders(
  req: Request,
  res: Response,
  next: NextFunction
): void {
  const userId = (req as any).user?.id;
  
  if (userId) {
    // Add custom header to indicate session monitoring is active
    res.setHeader('X-Session-Policy', 'single-session-enforced');
    
    // Add session info to response headers for debugging (in development)
    if (process.env.NODE_ENV === 'development') {
      getActiveSessionCount(userId).then(count => {
        res.setHeader('X-Active-Sessions', count.toString());
      }).catch(() => {
        // Ignore errors in header setting
      });
    }
  }
  
  next();
}

/**
 * Utility function to check and log session status for a user
 */
export async function logSessionStatus(userId: number, context: string): Promise<void> {
  try {
    const activeCount = await getActiveSessionCount(userId);
    
    logger.info('Session status check', {
      userId,
      context,
      activeSessionCount: activeCount,
      timestamp: new Date().toISOString()
    });

    if (activeCount > 1) {
      logger.warn('Multiple active sessions detected', {
        userId,
        context,
        activeSessionCount: activeCount
      });
    }
  } catch (error) {
    logger.error('Error checking session status', {
      userId,
      context,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

export default {
  sessionEnforcementMiddleware,
  loginSessionEnforcementMiddleware,
  sessionMonitoringHeaders,
  logSessionStatus
};
