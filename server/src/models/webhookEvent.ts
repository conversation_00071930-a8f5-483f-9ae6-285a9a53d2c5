/**
 * WebhookEvent model
 * Represents a webhook event received from Plaid
 */
export interface WebhookEvent {
  id: number;
  webhookType: string;
  webhookCode: string;
  eventId: string;
  transferId?: string;
  itemId?: string;
  accountId?: string;
  status?: string;
  rawData: Record<string, any>;
  processedResult: Record<string, any>;
  verified: boolean;
  createdAt: Date;
}

/**
 * Create webhook event table SQL
 */
export const createWebhookEventTableSQL = `
CREATE TABLE IF NOT EXISTS tbl_webhook_events (
  id INT AUTO_INCREMENT PRIMARY KEY,
  webhook_type VARCHAR(50) NOT NULL,
  webhook_code VARCHAR(50) NOT NULL,
  event_id VARCHAR(100) NOT NULL,
  transfer_id VARCHAR(100),
  item_id VARCHAR(100),
  account_id VARCHAR(100),
  status VARCHAR(50),
  raw_data JSON NOT NULL,
  processed_result JSON,
  verified BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_webhook_type_code (webhook_type, webhook_code),
  INDEX idx_transfer_id (transfer_id),
  INDEX idx_event_id (event_id),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
`;