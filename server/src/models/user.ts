import bcrypt from 'bcrypt';

export interface User {
  id: string;
  username: string;
  passwordHash: string;
  pinHash?: string;
  failedPinAttempts?: number;
  isPinLocked?: boolean;
  pinLastSetAt?: Date;
  pinResetToken?: string;
}

export const hashPassword = async (password: string): Promise<string> => {
  const saltRounds = 10;
  const passwordHash = await bcrypt.hash(password, saltRounds);
  return passwordHash;
};

export const comparePassword = async (password: string, passwordHash: string): Promise<boolean> => {
  const isMatch = await bcrypt.compare(password, passwordHash);
  return isMatch;
};

export const hashPin = async (pin: string): Promise<string> => {
  const saltRounds = 10;
  return await bcrypt.hash(pin, saltRounds);
};

export const comparePin = async (pin: string, pinHash: string): Promise<boolean> => {
  return await bcrypt.compare(pin, pinHash);
};
