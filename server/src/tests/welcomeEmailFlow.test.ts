import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { hasUserEverHadSession, createUserSession } from '../services/sessionService';
import { sendWelcomeEmail } from '../services/emailService';
import { executeUpdate, executeQuerySingle } from '../utils/database';

// Mock the email service
jest.mock('../services/emailService', () => ({
  sendWelcomeEmail: jest.fn(() => Promise.resolve(true))
}));

// Mock the database functions
jest.mock('../utils/database', () => ({
  executeUpdate: jest.fn(),
  executeQuerySingle: jest.fn(),
  executeQuery: jest.fn()
}));

const mockExecuteUpdate = executeUpdate as jest.MockedFunction<typeof executeUpdate>;
const mockExecuteQuerySingle = executeQuerySingle as jest.MockedFunction<typeof executeQuerySingle>;
const mockSendWelcomeEmail = sendWelcomeEmail as jest.MockedFunction<typeof sendWelcomeEmail>;

describe('Welcome Email Flow for First-Time Users', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('hasUserEverHadSession', () => {
    it('should return false for users with no sessions (first-time users)', async () => {
      // Mock database response for user with no sessions
      mockExecuteQuerySingle.mockResolvedValue({ count: 0 });

      const result = await hasUserEverHadSession(123);

      expect(result).toBe(false);
      expect(mockExecuteQuerySingle).toHaveBeenCalledWith(
        'SELECT COUNT(*) as count FROM tbl_user_sessions WHERE user_id = ?',
        [123]
      );
    });

    it('should return true for users with existing sessions', async () => {
      // Mock database response for user with existing sessions
      mockExecuteQuerySingle.mockResolvedValue({ count: 3 });

      const result = await hasUserEverHadSession(456);

      expect(result).toBe(true);
      expect(mockExecuteQuerySingle).toHaveBeenCalledWith(
        'SELECT COUNT(*) as count FROM tbl_user_sessions WHERE user_id = ?',
        [456]
      );
    });

    it('should return true on database error (safe fallback)', async () => {
      // Mock database error
      mockExecuteQuerySingle.mockRejectedValue(new Error('Database connection failed'));

      const result = await hasUserEverHadSession(789);

      expect(result).toBe(true); // Safe fallback to avoid sending welcome emails on error
    });

    it('should handle null database response', async () => {
      // Mock null response from database
      mockExecuteQuerySingle.mockResolvedValue(null);

      const result = await hasUserEverHadSession(999);

      expect(result).toBe(false);
    });
  });

  describe('Welcome Email Integration', () => {
    it('should send welcome email for first-time user', async () => {
      const testUser = {
        id: 123,
        email: '<EMAIL>',
        full_name: 'John Doe'
      };

      // Mock that user has no previous sessions
      mockExecuteQuerySingle.mockResolvedValue({ count: 0 });
      
      // Mock successful session creation
      mockExecuteUpdate.mockResolvedValue({ insertId: 1, affectedRows: 1 });

      // Check if user is first-time
      const isFirstTime = !(await hasUserEverHadSession(testUser.id));
      expect(isFirstTime).toBe(true);

      // Simulate sending welcome email
      if (isFirstTime) {
        const userName = testUser.full_name || testUser.email.split('@')[0];
        await sendWelcomeEmail(testUser.email, userName);
      }

      expect(mockSendWelcomeEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        'John Doe'
      );
    });

    it('should not send welcome email for returning user', async () => {
      const testUser = {
        id: 456,
        email: '<EMAIL>',
        full_name: 'Jane Smith'
      };

      // Mock that user has previous sessions
      mockExecuteQuerySingle.mockResolvedValue({ count: 2 });

      // Check if user is first-time
      const isFirstTime = !(await hasUserEverHadSession(testUser.id));
      expect(isFirstTime).toBe(false);

      // Should not send welcome email
      if (isFirstTime) {
        await sendWelcomeEmail(testUser.email, testUser.full_name);
      }

      expect(mockSendWelcomeEmail).not.toHaveBeenCalled();
    });

    it('should use email username when full_name is not available', async () => {
      const testUser = {
        id: 789,
        email: '<EMAIL>',
        full_name: null
      };

      // Mock that user has no previous sessions
      mockExecuteQuerySingle.mockResolvedValue({ count: 0 });

      // Check if user is first-time
      const isFirstTime = !(await hasUserEverHadSession(testUser.id));
      expect(isFirstTime).toBe(true);

      // Simulate sending welcome email with fallback name
      if (isFirstTime) {
        const userName = testUser.full_name || testUser.email.split('@')[0];
        await sendWelcomeEmail(testUser.email, userName);
      }

      expect(mockSendWelcomeEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        'testuser' // Should use email username as fallback
      );
    });
  });

  describe('Session Creation Flow', () => {
    it('should properly sequence session check and creation', async () => {
      const userId = 123;
      const jwtToken = 'test-jwt-token';
      const userAgent = 'Test Browser';
      const ipAddress = '127.0.0.1';

      // Mock that user has no previous sessions (first-time user)
      mockExecuteQuerySingle
        .mockResolvedValueOnce({ count: 0 }) // First call for hasUserEverHadSession
        .mockResolvedValueOnce(null); // Second call for session invalidation check

      // Mock successful session creation
      mockExecuteUpdate
        .mockResolvedValueOnce({ affectedRows: 0 }) // No existing sessions to invalidate
        .mockResolvedValueOnce({ insertId: 1, affectedRows: 1 }); // New session created

      // Check if first-time user
      const isFirstTime = !(await hasUserEverHadSession(userId));
      expect(isFirstTime).toBe(true);

      // Create session (this would normally be done in createUserSession)
      const sessionResult = await createUserSession(userId, jwtToken, userAgent, ipAddress);

      expect(mockExecuteQuerySingle).toHaveBeenCalledWith(
        'SELECT COUNT(*) as count FROM tbl_user_sessions WHERE user_id = ?',
        [userId]
      );
    });
  });

  describe('Error Handling', () => {
    it('should not fail login if welcome email fails', async () => {
      const testUser = {
        id: 123,
        email: '<EMAIL>',
        full_name: 'Test User'
      };

      // Mock that user has no previous sessions
      mockExecuteQuerySingle.mockResolvedValue({ count: 0 });
      
      // Mock welcome email failure
      mockSendWelcomeEmail.mockRejectedValue(new Error('Email service unavailable'));

      // Check if user is first-time
      const isFirstTime = !(await hasUserEverHadSession(testUser.id));
      expect(isFirstTime).toBe(true);

      // Simulate the try-catch block from the controller
      let loginSuccessful = true;
      if (isFirstTime) {
        try {
          const userName = testUser.full_name || testUser.email.split('@')[0];
          await sendWelcomeEmail(testUser.email, userName);
        } catch (error) {
          // Don't fail the login if welcome email fails
          console.log('Welcome email failed, but login continues');
        }
      }

      expect(loginSuccessful).toBe(true);
      expect(mockSendWelcomeEmail).toHaveBeenCalled();
    });
  });
});

describe('Integration Test Scenarios', () => {
  it('should handle complete first-time user login flow', async () => {
    const mockUser = {
      id: 100,
      email: '<EMAIL>',
      full_name: 'First Time User',
      team_connect_user_id: 'tc_100',
      master_parent_user_id: 0
    };

    // Step 1: Check if user has had sessions before
    mockExecuteQuerySingle.mockResolvedValueOnce({ count: 0 });
    const isFirstTime = !(await hasUserEverHadSession(mockUser.id));
    
    // Step 2: Create session
    mockExecuteUpdate
      .mockResolvedValueOnce({ affectedRows: 0 }) // No sessions to invalidate
      .mockResolvedValueOnce({ insertId: 1, affectedRows: 1 }); // Session created

    // Step 3: Send welcome email
    mockSendWelcomeEmail.mockResolvedValue(true);

    // Simulate the flow
    expect(isFirstTime).toBe(true);
    
    if (isFirstTime) {
      const userName = mockUser.full_name || mockUser.email.split('@')[0];
      const emailSent = await sendWelcomeEmail(mockUser.email, userName);
      expect(emailSent).toBe(true);
    }

    expect(mockSendWelcomeEmail).toHaveBeenCalledWith(
      '<EMAIL>',
      'First Time User'
    );
  });
});
