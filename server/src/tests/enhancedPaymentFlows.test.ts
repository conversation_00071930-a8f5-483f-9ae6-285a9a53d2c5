import { 
  getEnhancedWalletBalance,
  createPendingDeposit,
  createWithdrawalHold,
  confirmPendingDeposit,
  confirmWithdrawalHold,
  releaseFailedHold,
  getUserPendingHolds
} from '../services/enhancedPendingBalanceService';
import { syncAllPendingTransfers, syncTransferById } from '../services/plaidStatusSyncService';
import { executeQuery, executeUpdate } from '../utils/database';
import logger from '../utils/logger';

/**
 * Test the enhanced payment flow with proper Plaid status tracking
 */
export async function testEnhancedPaymentFlows(): Promise<void> {
  logger.info('Starting enhanced payment flows test');

  try {
    // Test user ID (should exist in your test database)
    const testUserId = 3; // Update this to a valid user ID from your database
    const testAmount = 100.00;
    const testTransferId = `test_transfer_${Date.now()}`;

    // Test 1: Check initial wallet balance
    logger.info('Test 1: Checking initial wallet balance');
    const initialBalance = await getEnhancedWalletBalance(testUserId);
    if (!initialBalance) {
      throw new Error('Test user wallet not found');
    }
    logger.info('Initial balance:', initialBalance);

    // Test 2: Create pending deposit (simulating add money)
    logger.info('Test 2: Creating pending deposit');
    const depositResult = await createPendingDeposit(
      testUserId,
      testAmount,
      testTransferId,
      'Test deposit from bank',
      { testMode: true, bankName: 'Test Bank' }
    );
    
    if (!depositResult.success) {
      throw new Error(`Failed to create pending deposit: ${depositResult.message}`);
    }
    logger.info('Pending deposit created:', depositResult);

    // Test 3: Check balance after pending deposit
    logger.info('Test 3: Checking balance after pending deposit');
    const balanceAfterDeposit = await getEnhancedWalletBalance(testUserId);
    if (!balanceAfterDeposit) {
      throw new Error('Failed to get balance after deposit');
    }
    
    // Verify pending balance increased but main balance stayed same
    if (balanceAfterDeposit.pending_balance !== initialBalance.pending_balance + testAmount) {
      throw new Error('Pending balance not updated correctly');
    }
    if (balanceAfterDeposit.balance !== initialBalance.balance) {
      throw new Error('Main balance should not change for pending deposit');
    }
    logger.info('Balance after pending deposit:', balanceAfterDeposit);

    // Test 4: Confirm the deposit (simulating Plaid webhook)
    logger.info('Test 4: Confirming pending deposit');
    const confirmDepositResult = await confirmPendingDeposit(testTransferId, 'posted');
    if (!confirmDepositResult.success) {
      throw new Error(`Failed to confirm deposit: ${confirmDepositResult.message}`);
    }
    logger.info('Deposit confirmed:', confirmDepositResult);

    // Test 5: Check balance after confirmation
    logger.info('Test 5: Checking balance after deposit confirmation');
    const balanceAfterConfirmation = await getEnhancedWalletBalance(testUserId);
    if (!balanceAfterConfirmation) {
      throw new Error('Failed to get balance after confirmation');
    }
    
    // Verify main balance increased and pending balance decreased
    if (balanceAfterConfirmation.balance !== initialBalance.balance + testAmount) {
      throw new Error('Main balance not updated correctly after confirmation');
    }
    if (balanceAfterConfirmation.pending_balance !== initialBalance.pending_balance) {
      throw new Error('Pending balance should return to original after confirmation');
    }
    logger.info('Balance after confirmation:', balanceAfterConfirmation);

    // Test 6: Create withdrawal hold (simulating withdrawal)
    logger.info('Test 6: Creating withdrawal hold');
    const withdrawalTransferId = `test_withdrawal_${Date.now()}`;
    const withdrawalAmount = 50.00;
    
    const withdrawalResult = await createWithdrawalHold(
      testUserId,
      withdrawalAmount,
      withdrawalTransferId,
      'Test withdrawal to bank',
      { testMode: true, bankName: 'Test Bank' }
    );
    
    if (!withdrawalResult.success) {
      throw new Error(`Failed to create withdrawal hold: ${withdrawalResult.message}`);
    }
    logger.info('Withdrawal hold created:', withdrawalResult);

    // Test 7: Check balance after withdrawal hold
    logger.info('Test 7: Checking balance after withdrawal hold');
    const balanceAfterWithdrawal = await getEnhancedWalletBalance(testUserId);
    if (!balanceAfterWithdrawal) {
      throw new Error('Failed to get balance after withdrawal');
    }
    
    // Verify blocked balance increased and available balance decreased
    if (balanceAfterWithdrawal.blocked_balance !== balanceAfterConfirmation.blocked_balance + withdrawalAmount) {
      throw new Error('Blocked balance not updated correctly');
    }
    if (balanceAfterWithdrawal.available_balance !== balanceAfterConfirmation.available_balance - withdrawalAmount) {
      throw new Error('Available balance not updated correctly');
    }
    logger.info('Balance after withdrawal hold:', balanceAfterWithdrawal);

    // Test 8: Confirm withdrawal (simulating Plaid webhook)
    logger.info('Test 8: Confirming withdrawal');
    const confirmWithdrawalResult = await confirmWithdrawalHold(withdrawalTransferId, 'posted');
    if (!confirmWithdrawalResult.success) {
      throw new Error(`Failed to confirm withdrawal: ${confirmWithdrawalResult.message}`);
    }
    logger.info('Withdrawal confirmed:', confirmWithdrawalResult);

    // Test 9: Check final balance
    logger.info('Test 9: Checking final balance');
    const finalBalance = await getEnhancedWalletBalance(testUserId);
    if (!finalBalance) {
      throw new Error('Failed to get final balance');
    }
    
    // Verify main balance decreased and blocked balance cleared
    const expectedFinalBalance = initialBalance.balance + testAmount - withdrawalAmount;
    if (Math.abs(finalBalance.balance - expectedFinalBalance) > 0.01) {
      throw new Error(`Final balance incorrect. Expected: ${expectedFinalBalance}, Got: ${finalBalance.balance}`);
    }
    if (finalBalance.blocked_balance !== initialBalance.blocked_balance) {
      throw new Error('Blocked balance should return to original after confirmation');
    }
    logger.info('Final balance:', finalBalance);

    // Test 10: Test failed transfer scenario
    logger.info('Test 10: Testing failed transfer scenario');
    const failedTransferId = `test_failed_${Date.now()}`;
    
    // Create another withdrawal hold
    const failedWithdrawalResult = await createWithdrawalHold(
      testUserId,
      25.00,
      failedTransferId,
      'Test failed withdrawal',
      { testMode: true }
    );
    
    if (!failedWithdrawalResult.success) {
      throw new Error(`Failed to create test withdrawal: ${failedWithdrawalResult.message}`);
    }

    // Simulate failure
    const releaseResult = await releaseFailedHold(failedTransferId, 'Insufficient funds');
    if (!releaseResult.success) {
      throw new Error(`Failed to release failed hold: ${releaseResult.message}`);
    }
    logger.info('Failed hold released:', releaseResult);

    // Test 11: Check pending holds
    logger.info('Test 11: Checking pending holds');
    const pendingHolds = await getUserPendingHolds(testUserId);
    logger.info(`User has ${pendingHolds.length} pending holds:`, pendingHolds);

    // Test 12: Test sync functionality
    logger.info('Test 12: Testing sync functionality');
    const syncResult = await syncAllPendingTransfers();
    logger.info('Sync result:', syncResult);

    logger.info('✅ All enhanced payment flow tests passed successfully!');

  } catch (error) {
    logger.error('❌ Enhanced payment flow test failed:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
    throw error;
  }
}

/**
 * Test wallet balance calculations
 */
export async function testWalletBalanceCalculations(): Promise<void> {
  logger.info('Testing wallet balance calculations');

  try {
    const testUserId = 3; // Update this to a valid user ID

    // Get current balance
    const balance = await getEnhancedWalletBalance(testUserId);
    if (!balance) {
      throw new Error('Test user wallet not found');
    }

    // Verify balance calculations
    const calculatedAvailable = balance.balance - balance.blocked_balance;
    if (Math.abs(balance.available_balance - calculatedAvailable) > 0.01) {
      throw new Error(`Available balance calculation incorrect. Expected: ${calculatedAvailable}, Got: ${balance.available_balance}`);
    }

    logger.info('✅ Wallet balance calculations test passed!');

  } catch (error) {
    logger.error('❌ Wallet balance calculations test failed:', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

/**
 * Run all tests
 */
export async function runAllEnhancedPaymentTests(): Promise<void> {
  logger.info('🚀 Starting all enhanced payment tests');

  try {
    await testWalletBalanceCalculations();
    await testEnhancedPaymentFlows();
    
    logger.info('🎉 All enhanced payment tests completed successfully!');
  } catch (error) {
    logger.error('💥 Enhanced payment tests failed:', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllEnhancedPaymentTests();
}
