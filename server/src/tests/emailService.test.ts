import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import { sendOTPEmail, sendWelcomeEmail, sendTransactionNotificationEmail, getAvailableEmailTemplates } from '../services/emailService';
import EmailTemplateService from '../services/emailTemplateService';

// Mock the actual email sending for tests
jest.mock('nodemailer', () => ({
  createTransporter: jest.fn(() => ({
    verify: jest.fn((callback) => callback(null, true)),
    sendMail: jest.fn(() => Promise.resolve({ messageId: 'test-message-id' }))
  }))
}));

describe('Email Service', () => {
  beforeAll(() => {
    // Set test environment variables
    process.env.EMAIL_HOST = 'smtp.test.com';
    process.env.EMAIL_USER = '<EMAIL>';
    process.env.EMAIL_PASSWORD = 'test-password';
    process.env.FRONTEND_URL = 'http://localhost:3000';
  });

  afterAll(() => {
    // Clean up
    EmailTemplateService.clearCache();
  });

  describe('sendOTPEmail', () => {
    it('should send OTP email successfully', async () => {
      const result = await sendOTPEmail('<EMAIL>', '123456', 'login');
      expect(result).toBe(true);
    });

    it('should send password reset OTP email', async () => {
      const result = await sendOTPEmail('<EMAIL>', '654321', 'password_reset');
      expect(result).toBe(true);
    });

    it('should handle different OTP types', async () => {
      const types = ['login', 'transaction', 'email_verify', 'phone_verify'];
      
      for (const type of types) {
        const result = await sendOTPEmail('<EMAIL>', '123456', type);
        expect(result).toBe(true);
      }
    });
  });

  describe('sendWelcomeEmail', () => {
    it('should send welcome email successfully', async () => {
      const result = await sendWelcomeEmail('<EMAIL>', 'John Doe');
      expect(result).toBe(true);
    });

    it('should send welcome email with custom dashboard URL', async () => {
      const result = await sendWelcomeEmail(
        '<EMAIL>', 
        'Jane Smith', 
        'https://custom.payconnect.com/dashboard'
      );
      expect(result).toBe(true);
    });
  });

  describe('sendTransactionNotificationEmail', () => {
    it('should send credit transaction notification', async () => {
      const transactionData = {
        id: 'TXN123456',
        type: 'credit' as const,
        amount: '$100.00',
        description: 'Wallet top-up',
        date: '2024-01-15 10:30 AM',
        currentBalance: '$250.00'
      };

      const result = await sendTransactionNotificationEmail('<EMAIL>', transactionData);
      expect(result).toBe(true);
    });

    it('should send debit transaction notification', async () => {
      const transactionData = {
        id: 'TXN789012',
        type: 'debit' as const,
        amount: '$50.00',
        description: 'Payment to merchant',
        date: '2024-01-15 11:45 AM',
        currentBalance: '$200.00'
      };

      const result = await sendTransactionNotificationEmail('<EMAIL>', transactionData);
      expect(result).toBe(true);
    });
  });

  describe('getAvailableEmailTemplates', () => {
    it('should return list of available templates', async () => {
      const templates = await getAvailableEmailTemplates();
      expect(Array.isArray(templates)).toBe(true);
      expect(templates.length).toBeGreaterThan(0);
      
      // Check for expected templates
      expect(templates).toContain('otp-verification');
      expect(templates).toContain('welcome');
      expect(templates).toContain('password-reset');
      expect(templates).toContain('transaction-notification');
    });
  });
});

describe('EmailTemplateService', () => {
  afterAll(() => {
    EmailTemplateService.clearCache();
  });

  describe('loadTemplate', () => {
    it('should load HTML template successfully', async () => {
      const template = await EmailTemplateService.loadTemplate('otp-verification', 'html');
      expect(typeof template).toBe('string');
      expect(template).toContain('{{OTP_CODE}}');
      expect(template).toContain('{{ACTION}}');
    });

    it('should load text template successfully', async () => {
      const template = await EmailTemplateService.loadTemplate('otp-verification', 'txt');
      expect(typeof template).toBe('string');
      expect(template).toContain('{{OTP_CODE}}');
      expect(template).toContain('{{ACTION}}');
    });

    it('should throw error for non-existent template', async () => {
      await expect(
        EmailTemplateService.loadTemplate('non-existent-template')
      ).rejects.toThrow('Email template not found');
    });
  });

  describe('processTemplate', () => {
    it('should replace template variables correctly', () => {
      const template = 'Hello {{USER_NAME}}, your code is {{OTP_CODE}}';
      const variables = {
        USER_NAME: 'John Doe',
        OTP_CODE: '123456'
      };

      const result = EmailTemplateService.processTemplate(template, variables);
      expect(result).toBe('Hello John Doe, your code is 123456');
    });

    it('should handle multiple occurrences of same variable', () => {
      const template = '{{USER_NAME}} welcome! Hi {{USER_NAME}}!';
      const variables = { USER_NAME: 'Alice' };

      const result = EmailTemplateService.processTemplate(template, variables);
      expect(result).toBe('Alice welcome! Hi Alice!');
    });
  });

  describe('loadAndProcessBothTemplates', () => {
    it('should load and process both HTML and text templates', async () => {
      const variables = {
        OTP_CODE: '123456',
        ACTION: 'testing'
      };

      const result = await EmailTemplateService.loadAndProcessBothTemplates('otp-verification', variables);
      
      expect(result).toHaveProperty('html');
      expect(result).toHaveProperty('text');
      expect(typeof result.html).toBe('string');
      expect(typeof result.text).toBe('string');
      expect(result.html).toContain('123456');
      expect(result.text).toContain('123456');
    });
  });

  describe('htmlToText', () => {
    it('should convert HTML to plain text', () => {
      const html = '<h1>Hello</h1><p>This is a <strong>test</strong> message.</p>';
      const text = EmailTemplateService.htmlToText(html);
      
      expect(text).toBe('Hello This is a test message.');
      expect(text).not.toContain('<');
      expect(text).not.toContain('>');
    });

    it('should handle HTML entities', () => {
      const html = 'Price: $100 &amp; free shipping &lt;offer&gt;';
      const text = EmailTemplateService.htmlToText(html);
      
      expect(text).toBe('Price: $100 & free shipping <offer>');
    });
  });
});
