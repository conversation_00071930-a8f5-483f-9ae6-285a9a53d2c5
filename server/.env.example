# Server Configuration
PORT=4000
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here

# Encryption Configuration
ENCRYPTION_KEY=your_32_character_encryption_key_here

# Primary Database Configuration (Main Operations)
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_primary_database_password
DB_DATABASE=payments

# Secondary Database Configuration (Analytics/Reporting)
# Optional: If not provided, will use primary database settings
DB_SECONDARY_HOST=localhost
DB_SECONDARY_USER=root
DB_SECONDARY_PASSWORD=your_secondary_database_password
DB_SECONDARY_DATABASE=payments_secondary

# Alternative Secondary Database Configurations:

# Same server, different database:
# DB_SECONDARY_DATABASE=payments_analytics

# Different server (read replica):
# DB_SECONDARY_HOST=replica-db.example.com
# DB_SECONDARY_USER=readonly_user
# DB_SECONDARY_PASSWORD=readonly_password
# DB_SECONDARY_DATABASE=payments

# Analytics server:
# DB_SECONDARY_HOST=analytics-db.example.com
# DB_SECONDARY_USER=analytics_user
# DB_SECONDARY_PASSWORD=analytics_password
# DB_SECONDARY_DATABASE=payments_analytics

# Email Configuration
EMAIL_HOST=smtp.office365.com
EMAIL_SERVICE=Outlook365
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password

# SMS Configuration (if needed)
# TWILIO_ACCOUNT_SID=your_twilio_account_sid
# TWILIO_AUTH_TOKEN=your_twilio_auth_token
# TWILIO_PHONE_NUMBER=your_twilio_phone_number

# Redis Configuration (if using Redis for caching)
# REDIS_HOST=localhost
# REDIS_PORT=6379
# REDIS_PASSWORD=your_redis_password

# Logging Configuration
LOG_LEVEL=info

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# File Upload Configuration
MAX_FILE_SIZE=********
UPLOAD_PATH=./uploads

# External API Keys
# PLAID_CLIENT_ID=your_plaid_client_id
# PLAID_SECRET=your_plaid_secret
# PLAID_ENV=sandbox
