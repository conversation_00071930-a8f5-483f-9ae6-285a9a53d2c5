import Decimal from 'decimal.js';

export interface PaymentBreakdownItem {
  label: string;
  value: number | string;
}

export interface CalculationOptions {
  items: PaymentBreakdownItem[];
  taxPercent?: number;
  discountPercent?: number;
  additionalFees?: PaymentBreakdownItem[];
}

export interface CalculationResult {
  breakdown: PaymentBreakdownItem[];
  subtotal: string;
  tax: string;
  discount: string;
  total: string;
}

export function calculatePayment({
  items,
  taxPercent = 0,
  discountPercent = 0,
  additionalFees = [],
}: CalculationOptions): CalculationResult {
  let subtotal = new Decimal(0);
  const breakdown: PaymentBreakdownItem[] = [];

  for (const item of items) {
    const value = new Decimal(item.value);
    subtotal = subtotal.plus(value);
    breakdown.push({ ...item, value: value.toFixed(2) });
  }

  for (const fee of additionalFees) {
    const value = new Decimal(fee.value);
    subtotal = subtotal.plus(value);
    breakdown.push({ ...fee, value: value.toFixed(2) });
  }

  const tax = subtotal.mul(taxPercent).div(100);
  const discount = subtotal.mul(discountPercent).div(100);
  const total = subtotal.plus(tax).minus(discount);

  return {
    breakdown,
    subtotal: subtotal.toFixed(2),
    tax: tax.toFixed(2),
    discount: discount.toFixed(2),
    total: total.toFixed(2),
  };
} 