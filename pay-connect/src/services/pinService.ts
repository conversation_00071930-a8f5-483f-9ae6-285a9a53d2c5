export interface PinVerificationResult {
  success: boolean;
  message: string;
}

// Check if PIN is set up
export const isPinSetup = (): boolean => {
  return localStorage.getItem('pinSetupComplete') === 'true';
};

// Get stored PIN (for verification purposes)
export const getStoredPin = (): string | null => {
  return localStorage.getItem('userPin');
};

// Verify PIN
export const verifyPin = (enteredPin: string): PinVerificationResult => {
  const storedPin = getStoredPin();
  
  if (!storedPin) {
    return {
      success: false,
      message: 'PIN not set up. Please set up your PIN first.'
    };
  }
  
  if (enteredPin === storedPin) {
    return {
      success: true,
      message: 'PIN verified successfully'
    };
  } else {
    return {
      success: false,
      message: 'Incorrect PIN. Please try again.'
    };
  }
};

// Set up PIN
export const setupPin = (pin: string): Promise<boolean> => {
  return new Promise((resolve) => {
    try {
      localStorage.setItem('userPin', pin);
      localStorage.setItem('pinSetupComplete', 'true');
      resolve(true);
    } catch (error) {
      console.error('Failed to set up PIN:', error);
      resolve(false);
    }
  });
};

// Mark PIN as set up (without storing the actual PIN)
export const setPinSetup = (): void => {
  localStorage.setItem('pinSetupComplete', 'true');
};

// Reset PIN (for forgot PIN functionality)
export const resetPin = (): void => {
  localStorage.removeItem('userPin');
  localStorage.removeItem('pinSetupComplete');
};

// Check if PIN is required for a transaction
export const isPinRequired = (): boolean => {
  return isPinSetup();
}; 