import { ConnectedBankAccount } from './plaid';

interface BalanceCache {
  accounts: ConnectedBankAccount[];
  timestamp: Date;
  totalBalance: number;
  totalAvailable: number;
}

class BankBalanceService {
  private cache: BalanceCache | null = null;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

  /**
   * Check if cached data is still valid
   */
  private isCacheValid(): boolean {
    if (!this.cache) return false;
    
    const now = new Date().getTime();
    const cacheTime = this.cache.timestamp.getTime();
    return (now - cacheTime) < this.CACHE_DURATION;
  }

  /**
   * Calculate totals from accounts
   */
  private calculateTotals(accounts: ConnectedBankAccount[]) {
    const totalBalance = accounts.reduce((sum, account) => {
      return sum + (account.balance.current || 0);
    }, 0);

    const totalAvailable = accounts.reduce((sum, account) => {
      return sum + (account.balance.available || account.balance.current || 0);
    }, 0);

    return { totalBalance, totalAvailable };
  }

  /**
   * Update cache with new account data
   */
  updateCache(accounts: ConnectedBankAccount[]): void {
    const { totalBalance, totalAvailable } = this.calculateTotals(accounts);
    
    this.cache = {
      accounts,
      timestamp: new Date(),
      totalBalance,
      totalAvailable
    };

    // Log balance update for debugging
    console.log(`💰 Balance Cache Updated: Total: $${totalBalance.toFixed(2)}, Available: $${totalAvailable.toFixed(2)}`);
  }

  /**
   * Get cached balance data if valid
   */
  getCachedBalance(): BalanceCache | null {
    if (this.isCacheValid()) {
      return this.cache;
    }
    return null;
  }

  /**
   * Get total balance (from cache if valid, otherwise null)
   */
  getTotalBalance(): number | null {
    const cached = this.getCachedBalance();
    return cached ? cached.totalBalance : null;
  }

  /**
   * Get total available balance (from cache if valid, otherwise null)
   */
  getTotalAvailable(): number | null {
    const cached = this.getCachedBalance();
    return cached ? cached.totalAvailable : null;
  }

  /**
   * Check if balance data needs refresh
   */
  needsRefresh(): boolean {
    return !this.isCacheValid();
  }

  /**
   * Clear cache (useful for forced refresh)
   */
  clearCache(): void {
    this.cache = null;
  }

  /**
   * Get cache age in minutes
   */
  getCacheAge(): number | null {
    if (!this.cache) return null;
    
    const now = new Date().getTime();
    const cacheTime = this.cache.timestamp.getTime();
    return Math.floor((now - cacheTime) / (1000 * 60));
  }

  /**
   * Get formatted cache status for UI
   */
  getCacheStatus(): string {
    if (!this.cache) return 'No data';
    
    const age = this.getCacheAge();
    if (age === null) return 'No data';
    
    if (age < 1) return 'Just updated';
    if (age === 1) return '1 minute ago';
    return `${age} minutes ago`;
  }
}

// Export singleton instance
export const bankBalanceService = new BankBalanceService();

// Export types
export type { BalanceCache };
