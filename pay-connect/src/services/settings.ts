import api from './api';

export interface PaymentRole {
  id: string;
  name: string;
  description: string;
  isEnabled: boolean;
  defaultAmount: number;
  paymentFrequency: 'Weekly' | 'Monthly' | 'Quarterly' | 'Yearly' | 'One-time';
  dueDate: string; // Format: DD (for monthly), or specific date
  autoProcessing: boolean;
  reminderDays: number; // Days before due date to send reminder
}

export interface PaymentSettings {
  currency: string;
  defaultPaymentMethod: 'Wallet' | 'Bank Transfer' | 'Cash';
  autoApprovalLimit: number;
  requireOTPForPayments: boolean;
  requirePINForPayments: boolean;
  maxDailyTransactionLimit: number;
  maxMonthlyTransactionLimit: number;
  enableBulkPayments: boolean;
  enableScheduledPayments: boolean;
}

export interface NotificationSettings {
  emailNotifications: boolean;
  smsNotifications: boolean;
  pushNotifications: boolean;
  paymentReminders: boolean;
  paymentConfirmations: boolean;
  overdueNotifications: boolean;
  weeklyReports: boolean;
  monthlyReports: boolean;
}

export interface SecuritySettings {
  sessionTimeout: number; // in minutes
  requireTwoFactorAuth: boolean;
  allowMultipleSessions: boolean;
  passwordExpiryDays: number;
  maxLoginAttempts: number;
  lockoutDuration: number; // in minutes
}

export interface AppSettings {
  paymentRoles: PaymentRole[];
  paymentSettings: PaymentSettings;
  notificationSettings: NotificationSettings;
  securitySettings: SecuritySettings;
}

// Mock data for payment roles
export const mockPaymentRoles: PaymentRole[] = [
  {
    id: '1',
    name: 'Staff',
    description: 'Administrative and office support staff',
    isEnabled: true,
    defaultAmount: 1000,
    paymentFrequency: 'Monthly',
    dueDate: '01', // 1st of every month
    autoProcessing: false,
    reminderDays: 3,
  },
  {
    id: '2',
    name: 'Referee',
    description: 'Sports referees and officials',
    isEnabled: true,
    defaultAmount: 500,
    paymentFrequency: 'Weekly',
    dueDate: '01', // Every Monday
    autoProcessing: true,
    reminderDays: 1,
  }
];

// Mock default settings
export const mockDefaultSettings: AppSettings = {
  paymentRoles: mockPaymentRoles,
  paymentSettings: {
    currency: 'USD',
    defaultPaymentMethod: 'Wallet',
    autoApprovalLimit: 0,
    requireOTPForPayments: true,
    requirePINForPayments: true,
    maxDailyTransactionLimit: 10000,
    maxMonthlyTransactionLimit: 100000,
    enableBulkPayments: true,
    enableScheduledPayments: true,
  },
  notificationSettings: {
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    paymentReminders: true,
    paymentConfirmations: true,
    overdueNotifications: true,
    weeklyReports: false,
    monthlyReports: true,
  },
  securitySettings: {
    sessionTimeout: 30,
    requireTwoFactorAuth: false,
    allowMultipleSessions: false,
    passwordExpiryDays: 90,
    maxLoginAttempts: 5,
    lockoutDuration: 15,
  },
};

// Get all application settings
export const getAppSettings = async (): Promise<AppSettings> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // For development, return mock data
    return Promise.resolve(mockDefaultSettings);
    
    // When API is ready, uncomment this:
    // const response = await api.get('/settings');
    // return response.data;
  } catch (error) {
    console.error('Error fetching app settings:', error);
    throw error;
  }
};

// Update payment roles
export const updatePaymentRoles = async (roles: PaymentRole[]): Promise<{ success: boolean; message: string }> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // For development, simulate success
    return {
      success: true,
      message: 'Payment roles updated successfully'
    };
    
    // When API is ready, uncomment this:
    // const response = await api.put('/settings/payment-roles', { roles });
    // return response.data;
  } catch (error) {
    console.error('Error updating payment roles:', error);
    return {
      success: false,
      message: 'Failed to update payment roles'
    };
  }
};

// Update payment settings
export const updatePaymentSettings = async (settings: PaymentSettings): Promise<{ success: boolean; message: string }> => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 600));
    
    return {
      success: true,
      message: 'Payment settings updated successfully'
    };
    
    // When API is ready, uncomment this:
    // const response = await api.put('/settings/payment', settings);
    // return response.data;
  } catch (error) {
    console.error('Error updating payment settings:', error);
    return {
      success: false,
      message: 'Failed to update payment settings'
    };
  }
};

// Update notification settings
export const updateNotificationSettings = async (settings: NotificationSettings): Promise<{ success: boolean; message: string }> => {
  try {
    await new Promise(resolve => setTimeout(resolve, 400));
    
    return {
      success: true,
      message: 'Notification settings updated successfully'
    };
  } catch (error) {
    console.error('Error updating notification settings:', error);
    return {
      success: false,
      message: 'Failed to update notification settings'
    };
  }
};

// Update security settings
export const updateSecuritySettings = async (settings: SecuritySettings): Promise<{ success: boolean; message: string }> => {
  try {
    await new Promise(resolve => setTimeout(resolve, 700));
    
    return {
      success: true,
      message: 'Security settings updated successfully'
    };
  } catch (error) {
    console.error('Error updating security settings:', error);
    return {
      success: false,
      message: 'Failed to update security settings'
    };
  }
};

// Reset settings to default
export const resetToDefaults = async (): Promise<{ success: boolean; message: string }> => {
  try {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return {
      success: true,
      message: 'Settings reset to defaults successfully'
    };
  } catch (error) {
    console.error('Error resetting settings:', error);
    return {
      success: false,
      message: 'Failed to reset settings'
    };
  }
};
