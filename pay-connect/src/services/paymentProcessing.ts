import api from './api';
import { markDueAsPaid } from './dues';

export interface PaymentMethod {
  id: string;
  type: 'card' | 'bank' | 'wallet';
  name: string;
  last4?: string;
  brand?: string;
  isDefault: boolean;
}

export interface PaymentRequest {
  dueId: string;
  amount: number;
  paymentMethodId: string;
  description: string;
}

export interface PaymentResponse {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  transactionId?: string;
  message?: string;
  redirectUrl?: string;
}

export interface PaymentConfirmation {
  paymentId: string;
  dueId: string;
  amount: number;
  status: 'completed' | 'failed';
  transactionId?: string;
  timestamp: string;
}

// Mock payment methods for development
export const mockPaymentMethods: PaymentMethod[] = [
  {
    id: '1',
    type: 'card',
    name: 'Visa ending in 4242',
    last4: '4242',
    brand: 'visa',
    isDefault: true,
  },
  {
    id: '2',
    type: 'card',
    name: 'Mastercard ending in 5555',
    last4: '5555',
    brand: 'mastercard',
    isDefault: false,
  },
  {
    id: '3',
    type: 'bank',
    name: 'Chase Bank ****1234',
    last4: '1234',
    isDefault: false,
  },
];

// Mock payment processing
export const processPayment = async (paymentRequest: PaymentRequest): Promise<PaymentResponse> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Simulate payment processing with 90% success rate
  const isSuccess = Math.random() > 0.1;
  
  if (isSuccess) {
    // Mark the due as paid in the dues service
    try {
      await markDueAsPaid(paymentRequest.dueId);
    } catch (error) {
      console.error('Failed to update due status:', error);
    }
    
    return {
      id: `pay_${Math.random().toString(36).substr(2, 9)}`,
      status: 'completed',
      transactionId: `txn_${Math.random().toString(36).substr(2, 9)}`,
      message: 'Payment processed successfully',
    };
  } else {
    return {
      id: `pay_${Math.random().toString(36).substr(2, 9)}`,
      status: 'failed',
      message: 'Payment failed. Please try again.',
    };
  }
};

export const getPaymentMethods = async (): Promise<PaymentMethod[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  return Promise.resolve(mockPaymentMethods);
};

export const addPaymentMethod = async (paymentMethod: Omit<PaymentMethod, 'id'>): Promise<PaymentMethod> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  const newPaymentMethod: PaymentMethod = {
    ...paymentMethod,
    id: Math.random().toString(36).substr(2, 9),
  };
  
  mockPaymentMethods.push(newPaymentMethod);
  return Promise.resolve(newPaymentMethod);
};

export const confirmPayment = async (paymentId: string): Promise<PaymentConfirmation> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  return {
    paymentId,
    dueId: 'mock-due-id',
    amount: 500,
    status: 'completed',
    transactionId: `txn_${Math.random().toString(36).substr(2, 9)}`,
    timestamp: new Date().toISOString(),
  };
};

// When API is ready, uncomment these:
/*
export const processPayment = async (paymentRequest: PaymentRequest): Promise<PaymentResponse> => {
  const response = await api.post('/payments/process', paymentRequest);
  return response.data;
};

export const getPaymentMethods = async (): Promise<PaymentMethod[]> => {
  const response = await api.get('/payment-methods');
  return response.data;
};

export const addPaymentMethod = async (paymentMethod: Omit<PaymentMethod, 'id'>): Promise<PaymentMethod> => {
  const response = await api.post('/payment-methods', paymentMethod);
  return response.data;
};

export const confirmPayment = async (paymentId: string): Promise<PaymentConfirmation> => {
  const response = await api.post(`/payments/${paymentId}/confirm`);
  return response.data;
};
*/ 