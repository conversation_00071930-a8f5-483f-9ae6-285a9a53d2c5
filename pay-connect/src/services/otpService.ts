export interface OTPRequest {
  paymentId: string;
  phoneNumber?: string;
  email?: string;
}

export interface OTPResponse {
  success: boolean;
  message: string;
  otpId?: string;
}

export interface OTPVerification {
  otpId: string;
  otp: string;
}

export interface OTPVerificationResponse {
  success: boolean;
  message: string;
  paymentId?: string;
}

// Mock OTP service for development
export const sendOTP = async (request: OTPRequest): Promise<OTPResponse> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Simulate OTP generation
  const otpId = `otp_${Math.random().toString(36).substr(2, 9)}`;
  
  // In a real implementation, this would send OTP via SMS/Email
  console.log(`OTP sent to ${request.phoneNumber || request.email}. OTP ID: ${otpId}`);
  
  return {
    success: true,
    message: 'OTP sent successfully',
    otpId,
  };
};

export const verifyOTP = async (verification: OTPVerification): Promise<OTPVerificationResponse> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Mock verification - in real implementation, this would verify against stored OTP
  // For demo purposes, we'll accept any 6-digit OTP
  const isValidOTP = /^\d{6}$/.test(verification.otp);
  
  if (isValidOTP) {
    return {
      success: true,
      message: 'OTP verified successfully',
      paymentId: `pay_${Math.random().toString(36).substr(2, 9)}`,
    };
  } else {
    return {
      success: false,
      message: 'Invalid OTP. Please try again.',
    };
  }
};

// When API is ready, uncomment these:
/*
export const sendOTP = async (request: OTPRequest): Promise<OTPResponse> => {
  const response = await api.post('/otp/send', request);
  return response.data;
};

export const verifyOTP = async (verification: OTPVerification): Promise<OTPVerificationResponse> => {
  const response = await api.post('/otp/verify', verification);
  return response.data;
};
*/ 