import api from './api';

export interface WithdrawalStep {
  key: string;
  title: string;
  description: string;
  status: 'pending' | 'completed' | 'failed' | 'skipped';
  completedAt?: string;
  notes?: string;
  order: number;
}

export interface WithdrawalTransaction {
  id: number;
  amount: number;
  description: string;
  created_at: string;
  paymentMethod: string;
  bankName: string;
  estimatedArrival: string;
  processingFee: number;
}

export interface WithdrawalStatus {
  overall: 'pending' | 'completed' | 'failed' | 'reversed';
  currentStep: string;
  currentStepTitle: string;
  currentStepDescription: string;
  lastUpdated: string;
}

export interface WithdrawalProgress {
  completed: number;
  total: number;
  percentage: number;
}

export interface TimelineEvent {
  title: string;
  description: string;
  timestamp: string;
  status: 'completed' | 'failed';
}

export interface WithdrawalStatusData {
  transaction: WithdrawalTransaction;
  status: WithdrawalStatus;
  progress: WithdrawalProgress;
  steps: WithdrawalStep[];
  timeline: TimelineEvent[];
}

/**
 * Get withdrawal status for a specific transaction
 */
export const getWithdrawalStatus = async (transactionId: number): Promise<WithdrawalStatusData> => {
  try {
    const response = await api.get(`/wallet/withdrawal-status/${transactionId}`);
    
    if (response.data && response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data?.message || 'Failed to fetch withdrawal status');
    }
  } catch (error: unknown) {
    console.error('Error fetching withdrawal status:', error);
    
    const err = error as any;
    
    // Log detailed error information for debugging
    console.error('Withdrawal Status API Error Details:', {
      status: err.response?.status,
      statusText: err.response?.statusText,
      data: err.response?.data,
      message: err.message,
      code: err.code
    });
    
    throw new Error(err.response?.data?.message || 'Failed to fetch withdrawal status. Please check your connection and try again.');
  }
};

/**
 * Get all withdrawal transactions for the current user
 */
export const getWithdrawalHistory = async (): Promise<WithdrawalTransaction[]> => {
  try {
    const response = await api.get('/wallet/withdrawals');
    
    if (response.data && response.data.success) {
      return response.data.data.withdrawals || [];
    } else {
      throw new Error(response.data?.message || 'Failed to fetch withdrawal history');
    }
  } catch (error: unknown) {
    console.error('Error fetching withdrawal history:', error);
    
    const err = error as any;
    throw new Error(err.response?.data?.message || 'Failed to fetch withdrawal history. Please check your connection and try again.');
  }
};

/**
 * Cancel a pending withdrawal (if supported)
 */
export const cancelWithdrawal = async (transactionId: number): Promise<{ success: boolean; message?: string }> => {
  try {
    const response = await api.post(`/wallet/withdrawal/${transactionId}/cancel`);
    
    if (response.data && response.data.success) {
      return {
        success: true,
        message: response.data.message || 'Withdrawal cancelled successfully'
      };
    } else {
      return {
        success: false,
        message: response.data?.message || 'Failed to cancel withdrawal'
      };
    }
  } catch (error: unknown) {
    console.error('Error cancelling withdrawal:', error);
    
    const err = error as any;
    return {
      success: false,
      message: err.response?.data?.message || 'Failed to cancel withdrawal. Please check your connection and try again.'
    };
  }
};

/**
 * Retry a failed withdrawal (if supported)
 */
export const retryWithdrawal = async (transactionId: number): Promise<{ success: boolean; message?: string; newTransactionId?: number }> => {
  try {
    const response = await api.post(`/wallet/withdrawal/${transactionId}/retry`);
    
    if (response.data && response.data.success) {
      return {
        success: true,
        message: response.data.message || 'Withdrawal retry initiated successfully',
        newTransactionId: response.data.data?.transactionId
      };
    } else {
      return {
        success: false,
        message: response.data?.message || 'Failed to retry withdrawal'
      };
    }
  } catch (error: unknown) {
    console.error('Error retrying withdrawal:', error);
    
    const err = error as any;
    return {
      success: false,
      message: err.response?.data?.message || 'Failed to retry withdrawal. Please check your connection and try again.'
    };
  }
};

/**
 * Format withdrawal status for display
 */
export const formatWithdrawalStatus = (status: string): { text: string; color: string; bgColor: string } => {
  switch (status.toLowerCase()) {
    case 'pending':
      return {
        text: 'Processing',
        color: 'text-blue-600',
        bgColor: 'bg-blue-50'
      };
    case 'completed':
      return {
        text: 'Completed',
        color: 'text-green-600',
        bgColor: 'bg-green-50'
      };
    case 'failed':
      return {
        text: 'Failed',
        color: 'text-red-600',
        bgColor: 'bg-red-50'
      };
    case 'reversed':
      return {
        text: 'Reversed',
        color: 'text-orange-600',
        bgColor: 'bg-orange-50'
      };
    case 'cancelled':
      return {
        text: 'Cancelled',
        color: 'text-gray-600',
        bgColor: 'bg-gray-50'
      };
    default:
      return {
        text: 'Unknown',
        color: 'text-gray-600',
        bgColor: 'bg-gray-50'
      };
  }
};

/**
 * Get estimated completion time based on payment method
 */
export const getEstimatedCompletionTime = (paymentMethod: string): string => {
  switch (paymentMethod.toLowerCase()) {
    case 'ach_same_day':
      return 'Same business day';
    case 'ach_standard':
      return '1-3 business days';
    case 'wire_transfer':
      return 'Within 2 hours';
    default:
      return '1-3 business days';
  }
};

/**
 * Check if withdrawal can be cancelled
 */
export const canCancelWithdrawal = (status: string, currentStep: string): boolean => {
  const cancellableStatuses = ['pending'];
  const cancellableSteps = ['initiated', 'validated', 'under_review'];
  
  return cancellableStatuses.includes(status.toLowerCase()) && 
         cancellableSteps.includes(currentStep.toLowerCase());
};

/**
 * Check if withdrawal can be retried
 */
export const canRetryWithdrawal = (status: string): boolean => {
  const retryableStatuses = ['failed', 'reversed'];
  return retryableStatuses.includes(status.toLowerCase());
};
