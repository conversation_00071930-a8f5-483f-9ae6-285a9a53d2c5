import api from './api';

// Utility function to safely parse balance values
const safeParseBalance = (value: unknown): number => {
  if (typeof value === 'number') return value;
  if (typeof value === 'string') {
    const parsed = parseFloat(value);
    return isNaN(parsed) ? 0 : parsed;
  }
  return 0;
};

export interface Wallet {
  balance: number;
  primaryBank: string;
  walletId?: string;
  currency?: string;
}

export interface UserProfile {
  id: string;
  name: string;
  avatarUrl?: string;
  email?: string;
}

export interface TransferRecipient {
  id: number;
  name: string;
  email: string;
  avatar?: string;
  hasWallet: boolean;
}

// Remove mock data - using real database only

export const getWallet = async (): Promise<Wallet> => {
  try {
    const response = await api.get('/wallet/enhanced-info');

    if (response.data && response.data.success && response.data.data.hasWallet) {
      const walletData = response.data.data;
      const wallet: Wallet = {
        balance: safeParseBalance(walletData.wallet.balance),
        primaryBank: walletData.primaryBank?.bankName || 'No Bank Connected',
        walletId: walletData.wallet.walletUniqueId,
        currency: 'USD'
      };

      return wallet;
    } else {
      // No wallet found, return default
      return {
        balance: 0,
        primaryBank: 'No Bank Connected',
        walletId: 'No Wallet',
        currency: 'USD'
      };
    }
  } catch (error: unknown) {
    console.error('Error fetching wallet:', error);

    const err = error as any;

    // Log detailed error information for debugging
    console.error('Wallet API Error Details:', {
      status: err.response?.status,
      statusText: err.response?.statusText,
      data: err.response?.data,
      message: err.message,
      code: err.code
    });

    throw new Error(`Failed to fetch wallet: ${err.response?.data?.message || err.message || 'Unknown error'}`);
  }
};

export const getUserProfile = async (): Promise<UserProfile> => {
  try {
    const response = await api.get('/user/profile');
    return response.data.data;
  } catch (error) {
    console.error('Error fetching user profile:', error);
    throw new Error('Failed to fetch user profile');
  }
};

export const deductFromWallet = async (amount: number): Promise<{ success: boolean; newBalance: number; message?: string }> => {
  try {
    const response = await api.post('/wallet/deduct', { amount });

    if (response.data && response.data.success) {
      return {
        success: true,
        newBalance: safeParseBalance(response.data.data.newBalance),
        message: response.data.message
      };
    } else {
      return {
        success: false,
        newBalance: 0,
        message: response.data?.message || 'Failed to deduct from wallet'
      };
    }
  } catch (error: unknown) {
    console.error('Error deducting from wallet:', error);

    const err = error as any;
    return {
      success: false,
      newBalance: 0,
      message: err.response?.data?.message || 'Failed to deduct from wallet'
    };
  }
};

export const createWallet = async (pin: string): Promise<{ success: boolean; walletId?: string; message?: string }> => {
  try {
    const response = await api.post('/wallet/create', {
      pin,
      timestamp: new Date().toISOString()
    });

    if (response.data && response.status === 200) {
      return {
        success: true,
        walletId: response.data.walletId,
        message: 'Wallet created successfully'
      };
    } else {
      return {
        success: false,
        message: 'Failed to create wallet'
      };
    }
  } catch (error) {
    console.error('Error creating wallet:', error);
    return {
      success: false,
      message: 'Failed to create wallet. Please try again.'
    };
  }
};

export const addMoneyToWallet = async (amount: number, pin: string, bankAccountId?: string): Promise<{ 
  success: boolean; 
  newBalance: number; 
  message?: string;
}> => {
  try {
    const response = await api.post('/wallet/add-money', {
      amount,
      pin,
      paymentMethod: 'bank_transfer',
      bankAccountId // Optional bank account ID
    });

    if (response.data && response.data.success) {
      const newBalance = safeParseBalance(response.data.data.newBalance);
      return {
        success: true,
        newBalance: newBalance,
        message: response.data.message
      };
    } else {
      return {
        success: false,
        newBalance: 0,
        message: response.data?.message || 'Failed to add money'
      };
    }
  } catch (error: unknown) {
    console.error('Error adding money to wallet:', error);

    const err = error as any;

    // Log detailed error information for debugging
    console.error('Add Money API Error Details:', {
      status: err.response?.status,
      statusText: err.response?.statusText,
      data: err.response?.data,
      message: err.message,
      code: err.code
    });

    return {
      success: false,
      newBalance: 0,
      message: err.response?.data?.message || 'Failed to add money to wallet. Please check your connection and try again.'
    };
  }
};

export const withdrawFromWallet = async (amount: number, pin: string, bankAccountId?: string, paymentMethod?: string): Promise<{ 
  success: boolean; 
  newBalance: number; 
  message?: string;
}> => {
  try {
    // Format the amount to have exactly 2 decimal places
    const formattedAmount = parseFloat(amount.toFixed(2));
    
    const response = await api.post('/wallet/withdraw', {
      amount: formattedAmount,
      pin,
      bankAccountId,
      paymentMethod
    });

    if (response.data && response.data.success) {
      const newBalance = safeParseBalance(response.data.data.newBalance);
      return {
        success: true,
        newBalance: newBalance,
        message: response.data.message
      };
    } else {
      return {
        success: false,
        newBalance: 0,
        message: response.data?.message || 'Failed to withdraw money'
      };
    }
  } catch (error: unknown) {
    console.error('Error withdrawing from wallet:', error);

    const err = error as any;

    // Log detailed error information for debugging
    console.error('Withdraw API Error Details:', {
      status: err.response?.status,
      statusText: err.response?.statusText,
      data: err.response?.data,
      message: err.message,
      code: err.code
    });

    return {
      success: false,
      newBalance: 0,
      message: err.response?.data?.message || 'Failed to withdraw from wallet. Please check your connection and try again.'
    };
  }
};

export const getWalletTransactions = async (limit: number = 50, offset: number = 0): Promise<any[]> => {
  try {
    const response = await api.get(`/wallet/transactions?limit=${limit}&offset=${offset}`);

    if (response.data && response.data.success) {
      return response.data.data.transactions;
    }

    return [];
  } catch (error: unknown) {
    console.error('Error fetching wallet transactions:', error);

    const err = error as any;

    // Log detailed error information for debugging
    console.error('Transactions API Error Details:', {
      status: err.response?.status,
      statusText: err.response?.statusText,
      data: err.response?.data,
      message: err.message,
      code: err.code
    });

    // Return empty array instead of mock data
    return [];
  }
};



export const sendPinChangeOTP = async (): Promise<{ success: boolean; message?: string; email?: string; otp?: string; expiresInMinutes?: number }> => {
  try {
    const response = await api.post('/wallet/send-pin-change-otp');

    if (response.data && response.data.success) {
      return {
        success: true,
        message: response.data.message || 'OTP sent successfully',
        email: response.data.data?.email,
        otp: response.data.data?.otp, // Only available in development
        expiresInMinutes: response.data.data?.expiresInMinutes
      };
    } else {
      return {
        success: false,
        message: response.data?.message || 'Failed to send OTP'
      };
    }
  } catch (error: unknown) {
    console.error('Error sending PIN change OTP:', error);

    const err = error as any;

    // Log detailed error information for debugging
    console.error('Send PIN Change OTP API Error Details:', {
      status: err.response?.status,
      statusText: err.response?.statusText,
      data: err.response?.data,
      message: err.message,
      code: err.code
    });

    return {
      success: false,
      message: err.response?.data?.message || 'Failed to send OTP. Please check your connection and try again.'
    };
  }
};

export const changeWalletPin = async (currentPin: string, newPin: string, otpCode: string): Promise<{ success: boolean; message?: string }> => {
  try {
    const response = await api.post('/wallet/change-pin', {
      currentPin,
      newPin,
      otpCode
    });

    if (response.data && response.data.success) {
      return {
        success: true,
        message: response.data.message || 'PIN changed successfully'
      };
    } else {
      return {
        success: false,
        message: response.data?.message || 'Failed to change PIN'
      };
    }
  } catch (error: unknown) {
    console.error('Error changing wallet PIN:', error);

    const err = error as any;

    // Log detailed error information for debugging
    console.error('Change PIN API Error Details:', {
      status: err.response?.status,
      statusText: err.response?.statusText,
      data: err.response?.data,
      message: err.message,
      code: err.code
    });

    return {
      success: false,
      message: err.response?.data?.message || 'Failed to change PIN. Please check your connection and try again.'
    };
  }
};

export const sendPinResetOTP = async (): Promise<{ success: boolean; message?: string; email?: string; otp?: string; expiresInMinutes?: number }> => {
  try {
    const response = await api.post('/wallet/send-pin-reset-otp');

    if (response.data && response.data.success) {
      return {
        success: true,
        message: response.data.message || 'PIN reset OTP sent successfully',
        email: response.data.data?.email,
        otp: response.data.data?.otp, // Only in development
        expiresInMinutes: response.data.data?.expiresInMinutes
      };
    } else {
      return {
        success: false,
        message: response.data?.message || 'Failed to send PIN reset OTP'
      };
    }
  } catch (error: unknown) {
    console.error('Error sending PIN reset OTP:', error);

    const err = error as any;

    // Log detailed error information for debugging
    console.error('Send PIN Reset OTP API Error Details:', {
      status: err.response?.status,
      statusText: err.response?.statusText,
      data: err.response?.data,
      message: err.message,
      code: err.code
    });

    return {
      success: false,
      message: err.response?.data?.message || 'Failed to send PIN reset OTP. Please check your connection and try again.'
    };
  }
};

export const resetWalletPin = async (newPin: string, otpCode: string): Promise<{ success: boolean; message?: string }> => {
  try {
    const response = await api.post('/wallet/reset-pin', {
      newPin,
      otpCode
    });

    if (response.data && response.data.success) {
      return {
        success: true,
        message: response.data.message || 'PIN reset successfully'
      };
    } else {
      return {
        success: false,
        message: response.data?.message || 'Failed to reset PIN'
      };
    }
  } catch (error: unknown) {
    console.error('Error resetting wallet PIN:', error);

    const err = error as any;

    // Log detailed error information for debugging
    console.error('Reset PIN API Error Details:', {
      status: err.response?.status,
      statusText: err.response?.statusText,
      data: err.response?.data,
      message: err.message,
      code: err.code
    });

    return {
      success: false,
      message: err.response?.data?.message || 'Failed to reset PIN. Please check your connection and try again.'
    };
  }
};

export const searchTransferRecipients = async (searchTerm: string = ''): Promise<TransferRecipient[]> => {
  try {
    const response = await api.get(`/wallet/search-users?search=${encodeURIComponent(searchTerm)}`);

    if (response.data && response.data.success) {
      return response.data.data || [];
    }

    return [];
  } catch (error: unknown) {
    console.error('Error searching transfer recipients:', error);

    const err = error as any;

    // Log detailed error information for debugging
    console.error('Search Users API Error Details:', {
      status: err.response?.status,
      statusText: err.response?.statusText,
      data: err.response?.data,
      message: err.message,
      code: err.code
    });

    return [];
  }
};

export const transferToWallet = async (
  recipientUserId: number,
  amount: number,
  pin: string
): Promise<{ success: boolean; newBalance?: number; message?: string; transactionId?: number }> => {
  try {
    const response = await api.post('/wallet/transfer', {
      recipientUserId,
      amount,
      pin
    });

    if (response.data && response.data.success) {
      const newBalance = safeParseBalance(response.data.data.newBalance);
      return {
        success: true,
        newBalance: newBalance,
        message: response.data.message,
        transactionId: response.data.data.transactionId
      };
    } else {
      return {
        success: false,
        message: response.data?.message || 'Failed to transfer money'
      };
    }
  } catch (error: unknown) {
    console.error('Error transferring to wallet:', error);

    const err = error as any;

    // Log detailed error information for debugging
    console.error('Transfer API Error Details:', {
      status: err.response?.status,
      statusText: err.response?.statusText,
      data: err.response?.data,
      message: err.message,
      code: err.code
    });

    return {
      success: false,
      message: err.response?.data?.message || 'Failed to transfer money. Please check your connection and try again.'
    };
  }
};

// Real-time API endpoints (ready for implementation)
/*
export const getWallet = async (): Promise<Wallet> => {
  const response = await api.get('/wallet');
  return response.data;
};

export const getUserProfile = async (): Promise<UserProfile> => {
  const response = await api.get('/user/profile');
  return response.data;
};

export const deductFromWallet = async (amount: number): Promise<{ success: boolean; newBalance: number; message?: string }> => {
  const response = await api.post('/wallet/deduct', { amount });
  return response.data;
};

export const addMoneyToWallet = async (amount: number): Promise<{ success: boolean; newBalance: number; message?: string }> => {
  const response = await api.post('/wallet/add', { amount });
  return response.data;
};

export const withdrawFromWallet = async (amount: number): Promise<{ success: boolean; newBalance: number; message?: string }> => {
  const response = await api.post('/wallet/withdraw', { amount });
  return response.data;
};
*/
