import api from './api';
import { Transaction } from './transactions';

export interface StaffDetail {
  id: string;
  full_name: string;
  email: string;
  phone: string;
  location: string;
  role: string;
  profile_pic: string;
  totalPaymentPaid: number;
  lastPaidDate: string;
  upcomingPayment: number;
  paymentDate: string;
  overduePayment: number;
  gameActivityCount: number;
  transactionsCount: number;
  transactions: Transaction[];

  // In a real app, gameActivity would be a separate array/data structure
  // gameActivities: GameActivity[]; 
}

export const mockStaffDetails: StaffDetail = {
  id: '1',
  full_name: '<PERSON><PERSON><PERSON>',
  email: '<PERSON><PERSON><PERSON>.<PERSON><PERSON>@gmail.com',
  phone: '0987654321',
  location: 'USA, California',
  role: 'Referee',
  profile_pic: 'https://randomuser.me/api/portraits/men/1.jpg',
  totalPaymentPaid: 1562.00,
  lastPaidDate: '2025-05-24',
  upcomingPayment: 1562.00,
  paymentDate: '2025-06-24',
  overduePayment: 1562.00,
  gameActivityCount: 10,
  transactionsCount: 8,
  transactions: [
    {
      id: 'txn1',
      transactionId: 'TXN-928374560',
      date: '2025-05-21',
      paidByReceivedFrom: 'Summer League 2025',
      paidTo: 'Summer',
      type: 'Referee',
      amount: 5000.00,
      status: 'Paid',
    },
    {
      id: 'txn2',
      transactionId: 'TXN-928374560',
      date: '2025-05-21',
      paidByReceivedFrom: 'Summer League 2025',
      paidTo: 'Summer',
      type: 'Team Manager',
      amount: 5000.00,
      status: 'Failed',
    },
    {
      id: 'txn3',
      transactionId: 'TXN-928374560',
      date: '2025-05-21',
      paidByReceivedFrom: 'Summer League 2025',
      paidTo: 'Summer',
      type: 'Referee',
      amount: 5000.00,
      status: 'Paid',
    },
    {
      id: 'txn4',
      transactionId: 'TXN-928374560',
      date: '2025-05-21',
      paidByReceivedFrom: 'Summer League 2025',
      paidTo: 'Summer',
      type: 'Team Manager',
      amount: 5000.00,
      status: 'Failed',
    },
    {
      id: 'txn5',
      transactionId: 'TXN-928374560',
      date: '2025-05-21',
      paidByReceivedFrom: 'Summer League 2025',
      paidTo: 'Summer',
      type: 'Referee',
      amount: 5000.00,
      status: 'Paid',
    },
    {
      id: 'txn6',
      transactionId: 'TXN-928374560',
      date: '2025-05-21',
      paidByReceivedFrom: 'Summer League 2025',
      paidTo: 'Summer',
      type: 'Team Manager',
      amount: 5000.00,
      status: 'Refund',
    },
    {
      id: 'txn7',
      transactionId: 'TXN-928374560',
      date: '2025-05-21',
      paidByReceivedFrom: 'Summer League 2025',
      paidTo: 'Summer',
      type: 'Referee',
      amount: 5000.00,
      status: 'Paid',
    },
    {
      id: 'txn8',
      transactionId: 'TXN-928374560',
      date: '2025-05-21',
      paidByReceivedFrom: 'Summer League 2025',
      paidTo: 'Summer',
      type: 'Team Manager',
      amount: 5000.00,
      status: 'Paid',
    },
  ],
};

export const getStaffDetails = async (id: string): Promise<StaffDetail> => {
  // For development, return mock data regardless of ID
  // return Promise.resolve(mockStaffDetails);
   const response = await api.get(`/staff/details?id=${id}`);

    return response.data.data ;

  // When API is ready, uncomment this:
  // const response = await api.get(`/staff/${id}`);
  // return response.data;
};

// Staff Transactions API
export interface StaffTransactionFilters {
  limit?: number;
  offset?: number;
  search?: string;
  type?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface StaffTransactionResponse {
  transactions: Transaction[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
    currentPage: number;
    totalPages: number;
  };
  filters: StaffTransactionFilters;
}

export const getStaffTransactions = async (
  userId: string,
  filters: StaffTransactionFilters = {}
): Promise<StaffTransactionResponse> => {
  try {
    console.log('getStaffTransactions called with:', { userId, filters });

    const params = new URLSearchParams();
    params.append('userId', userId);

    if (filters.limit) params.append('limit', filters.limit.toString());
    if (filters.offset) params.append('offset', filters.offset.toString());
    if (filters.search) params.append('search', filters.search);
    if (filters.type) params.append('type', filters.type);
    if (filters.status) params.append('status', filters.status);
    if (filters.dateFrom) params.append('dateFrom', filters.dateFrom);
    if (filters.dateTo) params.append('dateTo', filters.dateTo);

    const url = `/staff/transactions?${params.toString()}`;
    console.log('Making API request to:', url);

    const response = await api.get(url);

    if (response.data && response.data.success) {
      return response.data.data;
    }

    // Return empty response if API fails
    return {
      transactions: [],
      pagination: {
        total: 0,
        limit: filters.limit || 20,
        offset: filters.offset || 0,
        hasMore: false,
        currentPage: 1,
        totalPages: 0
      },
      filters
    };
  } catch (error) {
    console.error('Error fetching staff transactions:', error);

    // Return empty response on error
    return {
      transactions: [],
      pagination: {
        total: 0,
        limit: filters.limit || 20,
        offset: filters.offset || 0,
        hasMore: false,
        currentPage: 1,
        totalPages: 0
      },
      filters
    };
  }
};
