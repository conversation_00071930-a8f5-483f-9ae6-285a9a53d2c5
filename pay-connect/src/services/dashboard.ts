import api from './api';

export interface DashboardStats {
  totalWalletBalance: number;
  totalAmountDue: number;
  totalAwaitingAmount: number;
  currency: string;
}

export interface AwaitingPayment {
  id: string;
  title: string;
  subtitle: string;
  amount: string;
  date: string;
  status: string;
}

export interface DueItem {
  id: string;
  title: string;
  subtitle: string;
  amount: string;
  date: string;
  status: string;
}

export interface TransactionItem {
  id: string;
  title: string;
  subtitle: string;
  amount: string;
  date: string;
  type: 'received' | 'withdrawn' | 'refund';
  from: string;
}

export interface TransactionHistory {
  dateHeader: string;
  items: TransactionItem[];
}

export interface DashboardData {
  stats: DashboardStats;
  awaitingPayments: AwaitingPayment[];
  dueLists: DueItem[];
  transactionHistory: TransactionHistory[];
  bankAccount: {
    accountNumber: string;
    bankName: string;
    cardType: string;
  };
}

// Mock data for development
const mockDashboardData: DashboardData = {
  stats: {
    totalWalletBalance: 0,
    totalAmountDue: 0,
    totalAwaitingAmount: 0,
    currency: 'USD'
  },
  awaitingPayments: [
    {
      id: '1',
      title: 'Coming Soon - Payment Feature',
      subtitle: 'Awaiting payments will appear here',
      amount: '$0.00',
      date: 'Feature in development',
      status: 'pending'
    },
    {
      id: '2',
      title: 'Payment Processing',
      subtitle: 'Real-time payment tracking',
      amount: '$0.00',
      date: 'Available soon',
      status: 'pending'
    },
    {
      id: '3',
      title: 'Transaction Monitoring',
      subtitle: 'Live payment updates',
      amount: '$0.00',
      date: 'Coming soon',
      status: 'pending'
    }
  ],
  dueLists: [
    {
      id: '1',
      title: 'Due Management System',
      subtitle: 'Track your pending payments',
      amount: '$0.00',
      date: 'Feature in development',
      status: 'pending'
    },
    {
      id: '2',
      title: 'Payment Reminders',
      subtitle: 'Automated due notifications',
      amount: '$0.00',
      date: 'Available soon',
      status: 'pending'
    }
  ],
  transactionHistory: [], // Will be populated by real data
  bankAccount: {
    accountNumber: '••••••••••••0000',
    bankName: 'Connect Soon',
    cardType: 'Coming Soon'
  }
};

export const getDashboardData = async (): Promise<DashboardData> => {
  try {
    // Import wallet service to get real data
    const { getWallet } = await import('./wallet');

    // Get real wallet data
    const wallet = await getWallet();

    // Get real transaction history
    const transactionHistory = await getTransactionHistory();

    // Get real bank account data (from wallet service)
    const bankAccount = {
      accountNumber: '••••••••••••0000', // Masked for security
      bankName: wallet.primaryBank,
      cardType: 'Primary Account'
    };

    // Calculate real stats
    const stats: DashboardStats = {
      totalWalletBalance: wallet.balance,
      totalAmountDue: 0, // TODO: Implement when dues system is ready
      totalAwaitingAmount: 0, // TODO: Implement when payment system is ready
      currency: wallet.currency || 'USD'
    };

    return {
      stats,
      awaitingPayments: mockDashboardData.awaitingPayments, // Keep mock until payment system is ready
      dueLists: mockDashboardData.dueLists, // Keep mock until dues system is ready
      transactionHistory,
      bankAccount
    };

  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    // Fallback to mock data if wallet API fails
    return mockDashboardData;
  }
};

export const getDashboardStats = async (): Promise<DashboardStats> => {
  try {
    // Import wallet service to get real data
    const { getWallet } = await import('./wallet');

    // Get real wallet data
    const wallet = await getWallet();

    return {
      totalWalletBalance: wallet.balance,
      totalAmountDue: 0, // TODO: Implement when dues system is ready
      totalAwaitingAmount: 0, // TODO: Implement when payment system is ready
      currency: wallet.currency || 'USD'
    };

  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    // Fallback to mock data if wallet API fails
    return mockDashboardData.stats;
  }
};

export const getAwaitingPayments = async (): Promise<AwaitingPayment[]> => {
  try {
    // For development, return mock data
    await new Promise(resolve => setTimeout(resolve, 300));
    return Promise.resolve(mockDashboardData.awaitingPayments);
    
    // When API is ready, uncomment this:
    // const response = await api.get('/dashboard/awaiting-payments');
    // return response.data;
  } catch (error) {
    console.error('Error fetching awaiting payments:', error);
    throw error;
  }
};

export const getDueLists = async (): Promise<DueItem[]> => {
  try {
    // For development, return mock data
    await new Promise(resolve => setTimeout(resolve, 300));
    return Promise.resolve(mockDashboardData.dueLists);
    
    // When API is ready, uncomment this:
    // const response = await api.get('/dashboard/due-lists');
    // return response.data;
  } catch (error) {
    console.error('Error fetching due lists:', error);
    throw error;
  }
};

export const getTransactionHistory = async (): Promise<TransactionHistory[]> => {
  try {
    // Import wallet service to get real transactions
    const { getWalletTransactions } = await import('./wallet');
    const transactions = await getWalletTransactions(10, 0); // Get last 10 transactions

    if (transactions.length === 0) {
      return [{
        dateHeader: 'No Transactions Yet',
        items: [{
          id: '0',
          title: 'No transaction history',
          subtitle: 'Start using your wallet to see transactions here',
          amount: '$0.00',
          date: 'No transactions',
          type: 'received',
          from: 'PayConnect'
        }]
      }];
    }

    // Group transactions by date
    const groupedTransactions: { [key: string]: any[] } = {};

    transactions.forEach((tx: any) => {
      const date = new Date(tx.date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      if (!groupedTransactions[date]) {
        groupedTransactions[date] = [];
      }

      groupedTransactions[date].push({
        id: tx.id.toString(),
        title: tx.description,
        subtitle: tx.provider || 'Wallet Transaction',
        amount: `$${parseFloat(tx.amount).toFixed(2)}`,
        date: new Date(tx.date).toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit'
        }),
        type: parseFloat(tx.amount) >= 0 ? 'received' : 'withdrawn',
        from: tx.provider || 'Bank Transfer'
      });
    });

    // Convert to TransactionHistory format
    return Object.entries(groupedTransactions).map(([date, items]) => ({
      dateHeader: date,
      items
    }));

  } catch (error) {
    console.error('Error fetching transaction history:', error);
    // Return empty state instead of mock data
    return [{
      dateHeader: 'Error Loading Transactions',
      items: [{
        id: 'error',
        title: 'Failed to load transactions',
        subtitle: 'Please refresh the page to try again',
        amount: '$0.00',
        date: 'Error',
        type: 'received',
        from: 'System'
      }]
    }];
  }
};

// Alternative: Use dedicated dashboard API endpoint (when available)
export const getDashboardDataFromAPI = async (): Promise<DashboardData> => {
  try {
    const response = await api.get('/wallet/dashboard');
    const data = response.data.data;

    // Transform backend data to frontend format
    const stats: DashboardStats = {
      totalWalletBalance: data.wallet?.balance || 0,
      totalAmountDue: 0, // TODO: Implement when dues system is ready
      totalAwaitingAmount: 0, // TODO: Implement when payment system is ready
      currency: 'USD'
    };

    const bankAccount = {
      accountNumber: '••••••••••••0000', // Masked for security
      bankName: data.primaryBank?.bankName || 'No Bank Connected',
      cardType: 'Primary Account'
    };

    // Transform transactions
    const transactionHistory = await getTransactionHistory();

    return {
      stats,
      awaitingPayments: mockDashboardData.awaitingPayments, // Keep mock until payment system is ready
      dueLists: mockDashboardData.dueLists, // Keep mock until dues system is ready
      transactionHistory,
      bankAccount
    };

  } catch (error) {
    console.error('Error fetching dashboard data from API:', error);
    // Fallback to current implementation
    return getDashboardData();
  }
};
