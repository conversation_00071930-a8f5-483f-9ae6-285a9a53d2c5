import api from './api';

export interface Payment {
  id: string;
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed';
  date: string;
  description: string;
  recipient: string;
}

// Static data for development
export const mockPayments: Payment[] = [
  {
    id: '1',
    amount: 1500.00,
    currency: 'USD',
    status: 'completed',
    date: '2024-03-15',
    description: 'Monthly Rent',
    recipient: 'Landlord Corp'
  },
  {
    id: '2',
    amount: 299.99,
    currency: 'USD',
    status: 'pending',
    date: '2024-03-14',
    description: 'Office Supplies',
    recipient: 'Office Depot'
  },
  {
    id: '3',
    amount: 89.50,
    currency: 'USD',
    status: 'failed',
    date: '2024-03-13',
    description: 'Internet Bill',
    recipient: 'Comcast'
  }
];

export const getPayments = async (): Promise<Payment[]> => {
  // For development, return mock data
  return Promise.resolve(mockPayments);
  
  // When API is ready, uncomment this:
  // const response = await api.get('/payments');
  // return response.data;
};

export const createPayment = async (payment: Omit<Payment, 'id'>): Promise<Payment> => {
  // For development, return mock data
  return Promise.resolve({
    ...payment,
    id: Math.random().toString(36).substr(2, 9)
  });
  
  // When API is ready, uncomment this:
  // const response = await api.post('/payments', payment);
  // return response.data;
};

export const updatePayment = async (id: string, payment: Partial<Payment>): Promise<Payment> => {
  // For development, return mock data
  return Promise.resolve({
    ...mockPayments.find(p => p.id === id)!,
    ...payment
  });
  
  // When API is ready, uncomment this:
  // const response = await api.put(`/payments/${id}`, payment);
  // return response.data;
};

export const deletePayment = async (id: string): Promise<void> => {
  // For development, return mock data
  return Promise.resolve();
  
  // When API is ready, uncomment this:
  // await api.delete(`/payments/${id}`);
}; 