import api from './api';

export interface Invoice {
  id: string;
  invoiceNumber: string;
  clientName: string;
  clientEmail: string;
  clientPhone: string;
  league: string;
  season: string;
  amount: number;
  issueDate: string;
  dueDate: string;
  status: 'Draft' | 'Sent' | 'Paid' | 'Overdue' | 'Cancelled';
  description: string;
  avatar: string;
  items: InvoiceItem[];
  taxPercent: number;
  discountPercent: number;
  notes?: string;
}

export interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

// Static data for development
export const mockInvoices: Invoice[] = [
  {
    id: '1',
    invoiceNumber: 'INV-2025-001',
    clientName: 'Mumbai Cricket League',
    clientEmail: '<EMAIL>',
    clientPhone: '+91 9876543210',
    league: 'Mumbai Premier League',
    season: 'Summer 2025',
    amount: 15000,
    issueDate: '2025-01-15',
    dueDate: '2025-02-15',
    status: 'Sent',
    description: 'Tournament registration and facility fees',
    avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
    items: [
      {
        id: '1',
        description: 'Tournament Registration Fee',
        quantity: 1,
        unitPrice: 10000,
        total: 10000
      },
      {
        id: '2',
        description: 'Ground Facility Charges',
        quantity: 5,
        unitPrice: 1000,
        total: 5000
      }
    ],
    taxPercent: 18,
    discountPercent: 0,
    notes: 'Payment due within 30 days of invoice date.'
  },
  {
    id: '2',
    invoiceNumber: 'INV-2025-002',
    clientName: 'Delhi Football Club',
    clientEmail: '<EMAIL>',
    clientPhone: '+91 8765432109',
    league: 'Delhi Super League',
    season: 'Winter 2024',
    amount: 25000,
    issueDate: '2024-12-01',
    dueDate: '2024-12-31',
    status: 'Overdue',
    description: 'Annual membership and training facility fees',
    avatar: 'https://randomuser.me/api/portraits/men/2.jpg',
    items: [
      {
        id: '1',
        description: 'Annual Membership Fee',
        quantity: 1,
        unitPrice: 20000,
        total: 20000
      },
      {
        id: '2',
        description: 'Training Facility Access',
        quantity: 1,
        unitPrice: 5000,
        total: 5000
      }
    ],
    taxPercent: 18,
    discountPercent: 10,
    notes: 'Late payment charges may apply after due date.'
  },
  {
    id: '3',
    invoiceNumber: 'INV-2025-003',
    clientName: 'Bangalore Basketball Academy',
    clientEmail: '<EMAIL>',
    clientPhone: '+91 7654321098',
    league: 'Karnataka Basketball Championship',
    season: 'Spring 2025',
    amount: 18500,
    issueDate: '2025-01-20',
    dueDate: '2025-03-20',
    status: 'Paid',
    description: 'Championship hosting and equipment fees',
    avatar: 'https://randomuser.me/api/portraits/women/1.jpg',
    items: [
      {
        id: '1',
        description: 'Championship Hosting Fee',
        quantity: 1,
        unitPrice: 15000,
        total: 15000
      },
      {
        id: '2',
        description: 'Equipment Rental',
        quantity: 1,
        unitPrice: 3500,
        total: 3500
      }
    ],
    taxPercent: 18,
    discountPercent: 0,
    notes: 'Thank you for your prompt payment!'
  },
  {
    id: '4',
    invoiceNumber: 'INV-2025-004',
    clientName: 'Chennai Tennis Club',
    clientEmail: '<EMAIL>',
    clientPhone: '+91 6543210987',
    league: 'South India Tennis Tournament',
    season: 'Summer 2025',
    amount: 12000,
    issueDate: '2025-01-25',
    dueDate: '2025-02-25',
    status: 'Draft',
    description: 'Court booking and tournament organization fees',
    avatar: 'https://randomuser.me/api/portraits/women/2.jpg',
    items: [
      {
        id: '1',
        description: 'Court Booking (10 days)',
        quantity: 10,
        unitPrice: 800,
        total: 8000
      },
      {
        id: '2',
        description: 'Tournament Organization Fee',
        quantity: 1,
        unitPrice: 4000,
        total: 4000
      }
    ],
    taxPercent: 18,
    discountPercent: 5,
    notes: 'Draft invoice - pending client approval.'
  },
  {
    id: '5',
    invoiceNumber: 'INV-2025-005',
    clientName: 'Hyderabad Volleyball Association',
    clientEmail: '<EMAIL>',
    clientPhone: '+91 5432109876',
    league: 'National Volleyball Championship',
    season: 'Winter 2025',
    amount: 22000,
    issueDate: '2025-01-10',
    dueDate: '2025-02-10',
    status: 'Sent',
    description: 'Championship venue and logistics fees',
    avatar: 'https://randomuser.me/api/portraits/men/3.jpg',
    items: [
      {
        id: '1',
        description: 'Venue Rental (3 days)',
        quantity: 3,
        unitPrice: 5000,
        total: 15000
      },
      {
        id: '2',
        description: 'Logistics and Setup',
        quantity: 1,
        unitPrice: 7000,
        total: 7000
      }
    ],
    taxPercent: 18,
    discountPercent: 0,
    notes: 'Includes all setup and breakdown services.'
  }
];

export const getInvoices = async (filters?: {
  status?: string;
  search?: string;
  page?: number;
  limit?: number;
}): Promise<{ data: Invoice[]; totalPages: number }> => {
  // Add artificial delay to show the engaging loader
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  let filteredData = [...mockInvoices];

  // Apply status filter
  if (filters?.status && filters.status !== 'All') {
    filteredData = filteredData.filter(invoice => invoice.status === filters.status);
  }

  // Apply search filter
  if (filters?.search) {
    const searchTerm = filters.search.toLowerCase();
    filteredData = filteredData.filter(invoice =>
      invoice.clientName.toLowerCase().includes(searchTerm) ||
      invoice.invoiceNumber.toLowerCase().includes(searchTerm) ||
      invoice.clientEmail.toLowerCase().includes(searchTerm) ||
      invoice.league.toLowerCase().includes(searchTerm) ||
      invoice.description.toLowerCase().includes(searchTerm)
    );
  }

  const totalPages = Math.ceil(filteredData.length / 10); // Assuming 10 items per page
  return Promise.resolve({ data: filteredData, totalPages });
  
  // When API is ready, uncomment this:
  // const response = await api.get('/invoices', { params: filters });
  // return response.data;
};

export const getInvoiceById = async (id: string): Promise<Invoice> => {
  // Add artificial delay to show the engaging loader
  await new Promise(resolve => setTimeout(resolve, 800));
  // For development, return mock data regardless of ID for now
  const invoice = mockInvoices.find(i => i.id === id);
  if (!invoice) throw new Error('Invoice not found');
  return Promise.resolve(invoice);

  // When API is ready, uncomment this:
  // const response = await api.get(`/invoices/${id}`);
  // return response.data;
};

export const updateInvoiceStatus = async (id: string, status: Invoice['status']): Promise<Invoice> => {
  // Add artificial delay to show the engaging loader
  await new Promise(resolve => setTimeout(resolve, 500));
  // For development, return mock data
  const invoice = mockInvoices.find(i => i.id === id);
  if (!invoice) throw new Error('Invoice not found');
  
  return Promise.resolve({
    ...invoice,
    status
  });
  
  // When API is ready, uncomment this:
  // const response = await api.patch(`/invoices/${id}`, { status });
  // return response.data;
};

export const createInvoice = async (invoiceData: Omit<Invoice, 'id' | 'invoiceNumber'>): Promise<Invoice> => {
  // Add artificial delay to show the engaging loader
  await new Promise(resolve => setTimeout(resolve, 1200));
  // For development, return mock data with generated ID and invoice number
  const newInvoice: Invoice = {
    ...invoiceData,
    id: Date.now().toString(),
    invoiceNumber: `INV-2025-${String(mockInvoices.length + 1).padStart(3, '0')}`
  };
  
  return Promise.resolve(newInvoice);
  
  // When API is ready, uncomment this:
  // const response = await api.post('/invoices', invoiceData);
  // return response.data;
};

export const getInvoiceSummary = async () => {
  // Add artificial delay to show the engaging loader
  await new Promise(resolve => setTimeout(resolve, 1200));
  const invoices = await getInvoices();
  
  return {
    totalInvoices: invoices.data.length,
    totalAmount: invoices.data.reduce((sum, i) => sum + i.amount, 0),
    paidInvoices: invoices.data.filter(i => i.status === 'Paid').length,
    paidAmount: invoices.data
      .filter(i => i.status === 'Paid')
      .reduce((sum, i) => sum + i.amount, 0),
    pendingInvoices: invoices.data.filter(i => i.status === 'Sent').length,
    pendingAmount: invoices.data
      .filter(i => i.status === 'Sent')
      .reduce((sum, i) => sum + i.amount, 0),
    overdueInvoices: invoices.data.filter(i => i.status === 'Overdue').length,
    overdueAmount: invoices.data
      .filter(i => i.status === 'Overdue')
      .reduce((sum, i) => sum + i.amount, 0),
    draftInvoices: invoices.data.filter(i => i.status === 'Draft').length
  };
};
