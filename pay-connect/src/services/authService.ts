import api from './api';

export interface LoginRequest {
  email?: string;
  password?: string;
  timestamp: string;
  auth?: string;
  source?: string;
}

export interface LoginResponse {
  success: boolean;
  data: {
    token: string;
    user: {
      id: string;
      email: string;
      fullName?: string;
      name?: string;
      organizerId?: string;
      profilePic?: string;
    };
  };
  message?: string;
}

export interface OTPVerificationRequest {
  otpCode: string;
  email: string;
  timestamp: string;
}

export interface OTPVerificationResponse {
  success: boolean;
  data: {
    user?: {
      id: string;
      email: string;
      fullName?: string;
      name?: string;
      organizerId?: string;
      profilePic?: string;
    };
    userId?: string;
    email?: string;
    requiresPinSetup?: boolean;
    token?: string;
  };
  message?: string;
}

export interface ResendOTPResponse {
  success: boolean;
  message: string;
}

// Login API call
export const login = async (request: LoginRequest): Promise<LoginResponse> => {
  try {
    const response = await api.post('/users/login', request);
    
    if (response.data && response.status === 200) {
      return {
        success: true,
        data: response.data.data,
        message: 'Login successful'
      };
    } else {
      return {
        success: false,
        data: { token: '', user: { id: '', email: '' } },
        message: 'Login failed'
      };
    }
  } catch (error) {
    console.error('Login API error:', error);
    let errorMessage = 'Authentication failed. You are not authorized to access this application.';
    
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response?: { data?: { message?: string } } };
      errorMessage = axiosError.response?.data?.message || errorMessage;
    }
    
    throw new Error(errorMessage);
  }
};

// OTP Verification API call
export const verifyOTP = async (request: OTPVerificationRequest): Promise<OTPVerificationResponse> => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('Authentication token not found. Please login again.');
    }

    const response = await api.post('/users/verify-otp', request);
    
    if (response.data && response.status === 200) {
      return {
        success: true,
        data: response.data.data || response.data,
        message: 'OTP verified successfully'
      };
    } else {
      return {
        success: false,
        data: {},
        message: 'OTP verification failed'
      };
    }
  } catch (error) {
    console.error('OTP verification API error:', error);
    let errorMessage = 'Invalid OTP. Please try again.';
    
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response?: { data?: { message?: string } } };
      errorMessage = axiosError.response?.data?.message || errorMessage;
    }
    
    throw new Error(errorMessage);
  }
};

// Resend OTP API call
export const resendOTP = async (): Promise<ResendOTPResponse> => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('Authentication token not found. Please login again.');
    }

    const response = await api.get('/users/resend-otp');
    
    if (response.data && response.status === 200) {
      return {
        success: true,
        message: 'OTP has been resent to your email'
      };
    } else {
      return {
        success: false,
        message: 'Failed to resend OTP. Please try again.'
      };
    }
  } catch (error) {
    console.error('Resend OTP API error:', error);
    let errorMessage = 'Failed to resend OTP. Please try again.';
    
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response?: { data?: { message?: string } } };
      errorMessage = axiosError.response?.data?.message || errorMessage;
    }
    
    throw new Error(errorMessage);
  }
};

// Logout function
export const logout = (): void => {
  localStorage.removeItem('token');
  localStorage.removeItem('user');
  // Note: We don't remove PIN setup status on logout as it should persist
};

// Check if user is authenticated
export const isAuthenticated = (): boolean => {
  const token = localStorage.getItem('token');
  const user = localStorage.getItem('user');
  return !!(token && user);
};

// Get current user from localStorage
export const getCurrentUser = () => {
  const userStr = localStorage.getItem('user');
  return userStr ? JSON.parse(userStr) : null;
};

// Store user data
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const storeUserData = (userData: any): void => {
  localStorage.setItem('user', JSON.stringify(userData));
};

// Store auth token
export const storeAuthToken = (token: string): void => {
  localStorage.setItem('token', token);
};
