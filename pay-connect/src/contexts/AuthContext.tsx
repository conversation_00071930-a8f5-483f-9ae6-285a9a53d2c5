import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { isPinSetup as checkPinSetup } from '../services/pinService';
import { getCurrentUser, logout as logoutAPI } from '../services/authService';

interface User {
  walletId?: string;
  id: string;
  email: string;
  name: string;
  profilePic?: string;
  organizerId?: string;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  refreshUser: () => void;
  logout: () => void;
  isPinSetup: () => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Function to refresh user data from localStorage
  const refreshUser = () => {
    const savedUser = getCurrentUser();
    setUser(savedUser);
  };

  useEffect(() => {
    // Check if user is logged in on app start
    refreshUser();
    setIsLoading(false);
  }, []);

  const isPinSetupStatus = (): boolean => {
    return checkPinSetup();
  };

  const logout = () => {
    setUser(null);
    logoutAPI(); // This clears localStorage
  };

  const value = {
    user,
    isLoading,
    refreshUser,
    logout,
    isPinSetup: isPinSetupStatus
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};