import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import DashboardLayout from './components/Layout/DashboardLayout';
import Login from './pages/Login';
import OTPVerification from './pages/OTPVerification';
import SetupPin from './pages/SetupPin';
import Dashboard from './pages/Dashboard';
import Wallet from './pages/Wallet';
import Transactions from './pages/Transactions';
import AwaitingPayments from './pages/AwaitingPayments';
import Dues from './pages/Dues';
import Staff from './pages/Staff';
import Invoices from './pages/Invoices';
import BankAccount from './pages/BankAccount';
import Settings from './pages/Settings';
import Payments from './pages/Payments';
import StaffDetail from './pages/StaffDetail';
import TransactionDetail from './pages/TransactionDetail';
import DueDetail from './pages/DueDetail';
import NotFound from './pages/NotFound';

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          {/* Public Routes */}
          <Route path="/login" element={<Login />} />
          <Route path="/verify-otp" element={<OTPVerification />} />
          <Route path="/setup-pin" element={<SetupPin />} />
          
          {/* Protected Routes */}
          <Route path="/" element={
            <ProtectedRoute>
              <DashboardLayout />
            </ProtectedRoute>
          }>
            <Route index element={<Navigate to="/dashboard" replace />} />
            <Route path="dashboard" element={<Dashboard />} />
            <Route path="wallet" element={<Wallet />} />
            <Route path="transactions" element={<Transactions />} />
            <Route path="/transactions/:id" element={<TransactionDetail />} />
            <Route path="awaiting-payments" element={<AwaitingPayments />} />
            <Route path="payments" element={<Payments />} />
            <Route path="dues" element={<Dues />} />
            <Route path="/dues/:id" element={<DueDetail />} />
            <Route path="staff" element={<Staff />} />
            <Route path="/staff/:id" element={<StaffDetail />} />
            <Route path="invoices" element={<Invoices />} />
            <Route path="bank-account" element={<BankAccount />} />
            <Route path="settings" element={<Settings />} />
          </Route>
          
          {/* 404 Not Found route */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;