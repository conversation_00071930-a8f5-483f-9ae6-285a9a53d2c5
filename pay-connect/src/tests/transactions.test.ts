import { 
  getTransactions, 
  getTransactionById, 
  getTransactionSummary,
  exportTransactionsToCSV,
  exportTransactionsToJSON,
  TransactionFilter 
} from '../services/transactions';

// Mock API for testing
jest.mock('../services/api', () => ({
  get: jest.fn()
}));

import api from '../services/api';
const mockedApi = api as jest.Mocked<typeof api>;

describe('Transaction Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getTransactions', () => {
    it('should fetch transactions with filters', async () => {
      const mockResponse = {
        data: {
          data: [
            {
              id: 1,
              user_id: 3,
              type: 'credit',
              amount: 1000.00,
              reference_id: 'BANK_123',
              payment_provider: 'bank_transfer',
              description: 'Money added from Bank of America',
              status_id: '1',
              created_at: '2025-07-10T10:00:00Z',
              meta_data: { bankName: 'Bank of America', accountMask: '0000' }
            }
          ],
          total: 1
        }
      };

      mockedApi.get.mockResolvedValue(mockResponse);

      const filters: TransactionFilter = {
        status: 'Received',
        limit: 10
      };

      const result = await getTransactions(filters);

      expect(mockedApi.get).toHaveBeenCalledWith(
        expect.stringContaining('/wallet/transactions')
      );
      expect(result.data).toHaveLength(1);
      expect(result.data[0].status).toBe('Received');
      expect(result.data[0].category).toBe('add_money');
    });

    it('should handle API errors gracefully', async () => {
      mockedApi.get.mockRejectedValue(new Error('API Error'));

      const result = await getTransactions();

      // Should fall back to mock data
      expect(result.data).toBeDefined();
      expect(Array.isArray(result.data)).toBe(true);
    });
  });

  describe('getTransactionById', () => {
    it('should fetch a specific transaction', async () => {
      const mockResponse = {
        data: {
          data: [
            {
              id: 1,
              user_id: 3,
              type: 'debit',
              amount: -500.00,
              reference_id: 'WITHDRAW_123',
              payment_provider: 'bank_transfer',
              description: 'Withdrawal to Bank of America',
              status_id: '1',
              created_at: '2025-07-10T10:00:00Z',
              meta_data: { bankName: 'Bank of America', accountMask: '0000' }
            }
          ]
        }
      };

      mockedApi.get.mockResolvedValue(mockResponse);

      const result = await getTransactionById('1');

      expect(result.id).toBe('1');
      expect(result.category).toBe('withdraw');
      expect(result.status).toBe('Received');
    });
  });

  describe('getTransactionSummary', () => {
    it('should calculate transaction summary correctly', async () => {
      const mockResponse = {
        data: {
          data: [
            {
              id: 1,
              type: 'credit',
              amount: 1000.00,
              status_id: '1',
              reference_id: 'BANK_123',
              payment_provider: 'bank_transfer',
              description: 'Money added',
              created_at: '2025-07-10T10:00:00Z',
              meta_data: {}
            },
            {
              id: 2,
              type: 'debit',
              amount: -500.00,
              status_id: '2',
              reference_id: 'WITHDRAW_456',
              payment_provider: 'bank_transfer',
              description: 'Withdrawal',
              created_at: '2025-07-10T11:00:00Z',
              meta_data: {}
            }
          ],
          total: 2
        }
      };

      mockedApi.get.mockResolvedValue(mockResponse);

      const result = await getTransactionSummary();

      expect(result.transactionCount).toBe(2);
      expect(result.totalDeposits).toBeGreaterThan(0);
      expect(result.totalWithdrawals).toBeGreaterThan(0);
    });
  });

  describe('Export Functions', () => {
    it('should export transactions to CSV format', async () => {
      const mockResponse = {
        data: {
          data: [
            {
              id: 1,
              type: 'credit',
              amount: 1000.00,
              status_id: '1',
              reference_id: 'BANK_123',
              payment_provider: 'bank_transfer',
              description: 'Money added from Bank',
              created_at: '2025-07-10T10:00:00Z',
              meta_data: { bankName: 'Test Bank' }
            }
          ],
          total: 1
        }
      };

      mockedApi.get.mockResolvedValue(mockResponse);

      const csvContent = await exportTransactionsToCSV();

      expect(csvContent).toContain('Transaction ID,Date,Time');
      expect(csvContent).toContain('BANK_123');
      expect(csvContent).toContain('$1000.00');
    });

    it('should export transactions to JSON format', async () => {
      const mockResponse = {
        data: {
          data: [
            {
              id: 1,
              type: 'credit',
              amount: 1000.00,
              status_id: '1',
              reference_id: 'BANK_123',
              payment_provider: 'bank_transfer',
              description: 'Money added from Bank',
              created_at: '2025-07-10T10:00:00Z',
              meta_data: { bankName: 'Test Bank' }
            }
          ],
          total: 1
        }
      };

      mockedApi.get.mockResolvedValue(mockResponse);

      const jsonContent = await exportTransactionsToJSON();
      const parsedData = JSON.parse(jsonContent);

      expect(parsedData.transactions).toHaveLength(1);
      expect(parsedData.totalTransactions).toBe(1);
      expect(parsedData.exportDate).toBeDefined();
    });
  });

  describe('Transaction Type Detection', () => {
    it('should correctly categorize different transaction types', async () => {
      const testCases = [
        {
          type: 'credit',
          reference_id: 'BANK_123',
          expectedCategory: 'add_money'
        },
        {
          type: 'debit',
          reference_id: 'WITHDRAW_456',
          expectedCategory: 'withdraw'
        },
        {
          type: 'debit',
          reference_id: 'W2W_789',
          expectedCategory: 'payment_sent'
        },
        {
          type: 'credit',
          reference_id: 'W2W_789',
          expectedCategory: 'payment_received'
        }
      ];

      for (const testCase of testCases) {
        const mockResponse = {
          data: {
            data: [
              {
                id: 1,
                user_id: 3,
                type: testCase.type,
                amount: testCase.type === 'debit' ? -100 : 100,
                reference_id: testCase.reference_id,
                payment_provider: 'bank_transfer',
                description: 'Test transaction',
                status_id: '1',
                created_at: '2025-07-10T10:00:00Z',
                meta_data: {}
              }
            ],
            total: 1
          }
        };

        mockedApi.get.mockResolvedValue(mockResponse);

        const result = await getTransactions();
        expect(result.data[0].category).toBe(testCase.expectedCategory);
      }
    });
  });
});

// Integration test helper
export const testTransactionModule = async () => {
  console.log('🧪 Testing Transaction Module...');
  
  try {
    // Test basic transaction fetching
    console.log('📋 Testing transaction list...');
    const transactions = await getTransactions({ limit: 5 });
    console.log(`✅ Fetched ${transactions.data.length} transactions`);
    
    // Test transaction summary
    console.log('📊 Testing transaction summary...');
    const summary = await getTransactionSummary();
    console.log(`✅ Summary: ${summary.transactionCount} total transactions`);
    
    // Test transaction detail
    if (transactions.data.length > 0) {
      console.log('🔍 Testing transaction detail...');
      const detail = await getTransactionById(transactions.data[0].id);
      console.log(`✅ Transaction detail: ${detail.transactionId}`);
    }
    
    // Test export functionality
    console.log('📤 Testing export functionality...');
    const csvData = await exportTransactionsToCSV({ limit: 10 });
    console.log(`✅ CSV export: ${csvData.split('\n').length - 1} rows`);
    
    console.log('🎉 All transaction module tests passed!');
    return true;
  } catch (error) {
    console.error('❌ Transaction module test failed:', error);
    return false;
  }
};
