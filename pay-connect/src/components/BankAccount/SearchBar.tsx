import React from 'react';

const SearchBar: React.FC = () => {
  return (
    <div className="flex items-center border rounded px-3 py-2 w-full max-w-md bg-white">
      <svg
        className="w-5 h-5 text-gray-400 mr-2"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M21 21l-4.35-4.35m0 0A7.5 7.5 0 104.5 4.5a7.5 7.5 0 0012.15 12.15z"
        />
      </svg>
      <input
        type="text"
        placeholder="Search accounts..."
        className="outline-none w-full bg-transparent"
        disabled
      />
    </div>
  );
};

export default SearchBar;
