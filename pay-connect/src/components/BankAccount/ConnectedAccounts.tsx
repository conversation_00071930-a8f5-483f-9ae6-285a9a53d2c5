import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Building2, Star, MoreVertical, Trash2, CheckCircle } from 'lucide-react';
import { ConnectedBankAccount, disconnectAccount, setPrimaryAccount } from '../../services/plaid';

interface ConnectedAccountsProps {
  accounts: ConnectedBankAccount[];
  onAccountUpdate: () => void;
  className?: string;
}

const ConnectedAccounts: React.FC<ConnectedAccountsProps> = ({ 
  accounts, 
  onAccountUpdate, 
  className = '' 
}) => {
  const [loadingActions, setLoadingActions] = useState<{ [key: string]: boolean }>({});
  const [showDropdown, setShowDropdown] = useState<string | null>(null);

  const handleDisconnect = async (accountId: string) => {
    try {
      setLoadingActions(prev => ({ ...prev, [accountId]: true }));
      await disconnectAccount(accountId);
      onAccountUpdate();
      setShowDropdown(null);
    } catch (error) {
      console.error('Error disconnecting account:', error);
    } finally {
      setLoadingActions(prev => ({ ...prev, [accountId]: false }));
    }
  };

  const handleSetPrimary = async (accountId: string) => {
    try {
      setLoadingActions(prev => ({ ...prev, [accountId]: true }));
      await setPrimaryAccount(accountId);
      onAccountUpdate();
      setShowDropdown(null);
    } catch (error) {
      console.error('Error setting primary account:', error);
    } finally {
      setLoadingActions(prev => ({ ...prev, [accountId]: false }));
    }
  };

  const getAccountIcon = (type: string, subtype: string | null) => {
    if (type === 'depository') {
      if (subtype === 'checking') return <Building2 size={20} className="text-blue-600" />;
      if (subtype === 'savings') return <Building2 size={20} className="text-green-600" />;
    }
    return <CreditCard size={20} className="text-gray-600" />;
  };

  const formatBalance = (balance: { current: number | null; currency: string | null }) => {
    if (balance.current === null) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: balance.currency || 'USD'
    }).format(balance.current);
  };

  const formatAccountType = (type: string, subtype: string | null) => {
    if (type === 'depository') {
      if (subtype === 'checking') return 'Checking';
      if (subtype === 'savings') return 'Savings';
    }
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  if (accounts.length === 0) {
    return null;
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900">Connected Accounts</h3>
      
      <div className="space-y-3">
        {accounts.map((account) => (
          <div
            key={account.id}
            className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {getAccountIcon(account.type, account.subtype)}
                
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <h4 className="font-medium text-gray-900">{account.name}</h4>
                    {account.is_primary && (
                      <div className="flex items-center space-x-1 bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
                        <Star size={12} className="fill-current" />
                        <span>Primary</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="text-sm text-gray-500">
                    {account.institution_name} • {formatAccountType(account.type, account.subtype)} • ••••{account.mask}
                  </div>
                  
                  <div className="text-lg font-semibold text-gray-900 mt-1">
                    {formatBalance(account.balance)}
                  </div>
                </div>
              </div>

              <div className="relative">
                <button
                  onClick={() => setShowDropdown(showDropdown === account.id ? null : account.id)}
                  className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                  disabled={loadingActions[account.id]}
                >
                  <MoreVertical size={16} className="text-gray-500" />
                </button>

                {showDropdown === account.id && (
                  <div className="absolute right-0 top-full mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                    {!account.is_primary && (
                      <button
                        onClick={() => handleSetPrimary(account.id)}
                        disabled={loadingActions[account.id]}
                        className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2"
                      >
                        <CheckCircle size={16} />
                        <span>Set as Primary</span>
                      </button>
                    )}
                    
                    <button
                      onClick={() => handleDisconnect(account.id)}
                      disabled={loadingActions[account.id]}
                      className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2"
                    >
                      <Trash2 size={16} />
                      <span>Disconnect</span>
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ConnectedAccounts;
