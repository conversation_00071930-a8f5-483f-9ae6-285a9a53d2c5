import React from 'react';
import { TrendingUp, TrendingDown, DollarSign, Building2, Loader2 } from 'lucide-react';
import { ConnectedBankAccount } from '../../services/plaid';

interface BankAccountSummaryProps {
  accounts: ConnectedBankAccount[];
  className?: string;
  isRefreshing?: boolean;
}

const BankAccountSummary: React.FC<BankAccountSummaryProps> = ({ accounts, className = '', isRefreshing = false }) => {
  // Calculate real-time total balance from all connected accounts
  const totalBalance = accounts.reduce((sum, account) => {
    return sum + (account.balance.current || 0);
  }, 0);

  // Calculate available balance (what can be spent immediately)
  const totalAvailable = accounts.reduce((sum, account) => {
    return sum + (account.balance.available || account.balance.current || 0);
  }, 0);

  const primaryAccount = accounts.find(account => account.is_primary);
  const checkingAccounts = accounts.filter(account => account.subtype === 'checking');
  const savingsAccounts = accounts.filter(account => account.subtype === 'savings');

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  if (accounts.length === 0) {
    return null;
  }

  return (
    <div className={`relative ${className}`}>
      {/* Loading Overlay */}
      {isRefreshing && (
        <div className="absolute inset-0 bg-white bg-opacity-75 rounded-xl flex items-center justify-center z-10">
          <div className="flex items-center space-x-2 text-blue-600">
            <Loader2 size={20} className="animate-spin" />
            <span className="text-sm font-medium">Refreshing balances...</span>
          </div>
        </div>
      )}

      <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 ${isRefreshing ? 'opacity-50' : ''}`}>
        {/* Total Balance */}
        <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-100 text-sm">Total Balance</p>
              <p className="text-2xl font-bold">{formatCurrency(totalBalance)}</p>
              <p className="text-blue-200 text-xs mt-1">
                Available: {formatCurrency(totalAvailable)}
              </p>
            </div>
            <DollarSign size={32} className="text-blue-200" />
          </div>
        </div>

        {/* Primary Account */}
        {primaryAccount ? (
          <div className="bg-white border border-gray-200 rounded-xl p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Primary Account</p>
                <p className="text-xl font-bold text-gray-900">
                  {formatCurrency(primaryAccount.balance.current || 0)}
                </p>
                <p className="text-sm text-gray-600 mt-1">
                  {primaryAccount.institution_name}
                </p>
              </div>
              <Building2 size={32} className="text-gray-400" />
            </div>
          </div>
        ) : null}

        {/* Checking Accounts */}
        <div className="bg-white border border-gray-200 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-500 text-sm">Checking ({checkingAccounts.length})</p>
              <p className="text-xl font-bold text-gray-900">
                {formatCurrency(
                  checkingAccounts.reduce((sum, acc) => sum + (acc.balance.current || 0), 0)
                )}
              </p>
            </div>
            <TrendingUp size={32} className="text-green-500" />
          </div>
        </div>

        {/* Savings Accounts */}
        <div className="bg-white border border-gray-200 rounded-xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-500 text-sm">Savings ({savingsAccounts.length})</p>
              <p className="text-xl font-bold text-gray-900">
                {formatCurrency(
                  savingsAccounts.reduce((sum, acc) => sum + (acc.balance.current || 0), 0)
                )}
              </p>
            </div>
            <TrendingDown size={32} className="text-blue-500" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BankAccountSummary;
