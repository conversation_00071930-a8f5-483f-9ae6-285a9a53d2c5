import React, { useState } from 'react';
import { Users, Plus, Edit2, Trash2, Save, X, DollarSign, Calendar, Bell } from 'lucide-react';
import { PaymentRole } from '../../services/settings';

interface PaymentRolesSettingsProps {
  roles: PaymentRole[];
  onUpdate: (roles: PaymentRole[]) => void;
  isLoading?: boolean;
}

const PaymentRolesSettings: React.FC<PaymentRolesSettingsProps> = ({ roles, onUpdate, isLoading }) => {
  const [editingRole, setEditingRole] = useState<string | null>(null);
  const [editForm, setEditForm] = useState<PaymentRole | null>(null);
  const [isAddingNew, setIsAddingNew] = useState(false);

  const handleEdit = (role: PaymentRole) => {
    setEditingRole(role.id);
    setEditForm({ ...role });
  };

  const handleSave = () => {
    if (!editForm) return;

    const updatedRoles = roles.map(role => 
      role.id === editForm.id ? editForm : role
    );
    
    onUpdate(updatedRoles);
    setEditingRole(null);
    setEditForm(null);
  };

  const handleCancel = () => {
    setEditingRole(null);
    setEditForm(null);
    setIsAddingNew(false);
  };

  const handleAddNew = () => {
    const newRole: PaymentRole = {
      id: `new_${Date.now()}`,
      name: '',
      description: '',
      isEnabled: true,
      defaultAmount: 0,
      paymentFrequency: 'Monthly',
      dueDate: '01',
      autoProcessing: false,
      reminderDays: 3,
    };
    setEditForm(newRole);
    setIsAddingNew(true);
    setEditingRole(newRole.id);
  };

  const handleSaveNew = () => {
    if (!editForm || !editForm.name.trim()) return;

    const updatedRoles = [...roles, editForm];
    onUpdate(updatedRoles);
    setIsAddingNew(false);
    setEditingRole(null);
    setEditForm(null);
  };

  const handleDelete = (roleId: string) => {
    if (window.confirm('Are you sure you want to delete this payment role?')) {
      const updatedRoles = roles.filter(role => role.id !== roleId);
      onUpdate(updatedRoles);
    }
  };

  const handleToggleEnabled = (roleId: string) => {
    const updatedRoles = roles.map(role =>
      role.id === roleId ? { ...role, isEnabled: !role.isEnabled } : role
    );
    onUpdate(updatedRoles);
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="">
        
          <div className='flex items-center space-x-3'>
            <h3 className="text-lg font-semibold text-gray-900">Payment Roles</h3>
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <Users className="w-5 h-5 text-blue-600" />
          </div>
          </div>
           
           <p className="text-sm text-gray-500">Configure roles for payment processing</p>
        </div>
        {/* <button
          onClick={handleAddNew}
          disabled={isAddingNew || isLoading}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Role
        </button> */}
      </div>

      <div className="space-y-4">
        {roles.map((role) => (
          <div
            key={role.id}
            className={`border rounded-lg p-4 transition-colors ${
              role.isEnabled ? 'border-gray-200 bg-white' : 'border-gray-200 bg-gray-50'
            }`}
          >
            {editingRole === role.id ? (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Role Name</label>
                    <input
                      type="text"
                      value={editForm?.name || ''}
                      onChange={(e) => setEditForm(prev => prev ? { ...prev, name: e.target.value } : null)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter role name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Default Amount ($)</label>
                    <input
                      type="number"
                      value={editForm?.defaultAmount || 0}
                      onChange={(e) => setEditForm(prev => prev ? { ...prev, defaultAmount: Number(e.target.value) } : null)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      min="0"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                  <textarea
                    value={editForm?.description || ''}
                    onChange={(e) => setEditForm(prev => prev ? { ...prev, description: e.target.value } : null)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    rows={2}
                    placeholder="Enter role description"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Payment Frequency</label>
                    <select
                      value={editForm?.paymentFrequency || 'Monthly'}
                      onChange={(e) => setEditForm(prev => prev ? { ...prev, paymentFrequency: e.target.value as any } : null)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="Weekly">Weekly</option>
                      <option value="Monthly">Monthly</option>
                      <option value="Quarterly">Quarterly</option>
                      <option value="Yearly">Yearly</option>
                      <option value="One-time">One-time</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Due Date</label>
                    <input
                      type="text"
                      value={editForm?.dueDate || ''}
                      onChange={(e) => setEditForm(prev => prev ? { ...prev, dueDate: e.target.value } : null)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="01 (for 1st of month)"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Reminder Days</label>
                    <input
                      type="number"
                      value={editForm?.reminderDays || 0}
                      onChange={(e) => setEditForm(prev => prev ? { ...prev, reminderDays: Number(e.target.value) } : null)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      min="0"
                      max="30"
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={editForm?.autoProcessing || false}
                      onChange={(e) => setEditForm(prev => prev ? { ...prev, autoProcessing: e.target.checked } : null)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Enable Auto Processing</span>
                  </label>
                </div>

                <div className="flex items-center space-x-3 pt-4 border-t">
                  <button
                    onClick={isAddingNew ? handleSaveNew : handleSave}
                    className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    <Save className="w-4 h-4 mr-2" />
                    Save
                  </button>
                  <button
                    onClick={handleCancel}
                    className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                  >
                    <X className="w-4 h-4 mr-2" />
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h4 className={`text-lg font-medium ${role.isEnabled ? 'text-gray-900' : 'text-gray-500'}`}>
                      {role.name}
                    </h4>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      role.isEnabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'
                    }`}>
                      {role.isEnabled ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">{role.description}</p>
                  <div className="flex items-center space-x-6 text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <DollarSign className="w-4 h-4" />
                      <span>${role.defaultAmount}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-4 h-4" />
                      <span>{role.paymentFrequency}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Bell className="w-4 h-4" />
                      <span>{role.reminderDays} days</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    disabled={true}
                    onClick={() => handleToggleEnabled(role.id)}
                    className={`px-3 py-1 text-sm rounded-lg transition-colors ${
                      role.isEnabled 
                        ? 'bg-red-100 text-red-700 hover:bg-red-200' 
                        : 'bg-green-100 text-green-700 hover:bg-green-200'
                    }`}
                  >
                    {role.isEnabled ? 'Disable' : 'Enable'}
                  </button>
                  <button
                    disabled={true}
                    onClick={() => handleEdit(role)}
                    className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                  >
                    <Edit2 className="w-4 h-4" />
                  </button>
                  <button
                    disabled={true}
                    onClick={() => handleDelete(role.id)}
                    className="p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            )}
          </div>
        ))}

        {roles.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <Users className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>No payment roles configured</p>
            <p className="text-sm">Add a role to get started</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PaymentRolesSettings;
