import React, { useState, useEffect } from 'react';
import { Shield, Lock, Clock, AlertTriangle, Key, Users } from 'lucide-react';
import { SecuritySettings as SecuritySettingsType } from '../../services/settings';

interface SecuritySettingsProps {
  settings: SecuritySettingsType;
  onUpdate: (settings: SecuritySettingsType) => void;
  isLoading?: boolean;
}

const SecuritySettings: React.FC<SecuritySettingsProps> = ({ settings, onUpdate, isLoading }) => {
  const [formData, setFormData] = useState<SecuritySettingsType>(settings);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    setFormData(settings);
    setHasChanges(false);
  }, [settings]);

  const handleChange = (field: keyof SecuritySettingsType, value: any) => {
    const newData = { ...formData, [field]: value };
    setFormData(newData);
    setHasChanges(JSON.stringify(newData) !== JSON.stringify(settings));
  };

  const handleSave = () => {
    onUpdate(formData);
    setHasChanges(false);
  };

  const handleReset = () => {
    setFormData(settings);
    setHasChanges(false);
  };

  const ToggleSwitch: React.FC<{ checked: boolean; onChange: (checked: boolean) => void }> = ({ checked, onChange }) => (
    <label className="relative inline-flex items-center cursor-pointer">
      <input
        type="checkbox"
        checked={checked}
        onChange={(e) => onChange(e.target.checked)}
        className="sr-only peer"
      />
      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
    </label>
  );

  return (
    <div className="bg-white rounded-xl shadow-sm border p-6">
      <div className="flex items-center justify-between mb-6">
        <div >
         
          <div className="flex items-center space-x-3">
            <h3 className="text-lg font-semibold text-gray-900">Security Settings</h3>
             <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
            <Shield className="w-5 h-5 text-red-600" />
          </div>
           
          </div>
           <p className="text-sm text-gray-500">Configure security and access controls</p>
        </div>
        {hasChanges && (
          <div className="flex space-x-2">
            <button
              onClick={handleReset}
              className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Reset
            </button>
            <button
              onClick={handleSave}
              disabled={isLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              Save Changes
            </button>
          </div>
        )}
      </div>

      <div className="space-y-6">
        {/* Session Management */}
        <div>
          <h4 className="text-md font-medium text-gray-900 mb-4 flex items-center">
            <Clock className="w-4 h-4 mr-2" />
            Session Management
          </h4>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Session Timeout (minutes)
                </label>
                <select
                  value={formData.sessionTimeout}
                  onChange={(e) => handleChange('sessionTimeout', Number(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value={15}>15 minutes</option>
                  <option value={30}>30 minutes</option>
                  <option value={60}>1 hour</option>
                  <option value={120}>2 hours</option>
                  <option value={240}>4 hours</option>
                  <option value={480}>8 hours</option>
                </select>
                <p className="text-xs text-gray-500 mt-1">Automatically log out inactive users</p>
              </div>

              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <h5 className="text-sm font-medium text-gray-900">Allow Multiple Sessions</h5>
                  <p className="text-sm text-gray-500">Users can be logged in from multiple devices</p>
                </div>
                <ToggleSwitch
                  checked={formData.allowMultipleSessions}
                  onChange={(checked) => handleChange('allowMultipleSessions', checked)}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Authentication */}
        <div className="border-t pt-6">
          <h4 className="text-md font-medium text-gray-900 mb-4 flex items-center">
            <Key className="w-4 h-4 mr-2" />
            Authentication
          </h4>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div>
                <h5 className="text-sm font-medium text-gray-900">Require Two-Factor Authentication</h5>
                <p className="text-sm text-gray-500">Add an extra layer of security with 2FA</p>
              </div>
              <ToggleSwitch
                checked={formData.requireTwoFactorAuth}
                onChange={(checked) => handleChange('requireTwoFactorAuth', checked)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Password Expiry (days)
              </label>
              <select
                value={formData.passwordExpiryDays}
                onChange={(e) => handleChange('passwordExpiryDays', Number(e.target.value))}
                className="w-full md:w-1/2 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value={30}>30 days</option>
                <option value={60}>60 days</option>
                <option value={90}>90 days</option>
                <option value={180}>180 days</option>
                <option value={365}>1 year</option>
                <option value={0}>Never expire</option>
              </select>
              <p className="text-xs text-gray-500 mt-1">Force users to change passwords periodically</p>
            </div>
          </div>
        </div>

        {/* Login Security */}
        <div className="border-t pt-6">
          <h4 className="text-md font-medium text-gray-900 mb-4 flex items-center">
            <AlertTriangle className="w-4 h-4 mr-2" />
            Login Security
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Login Attempts
              </label>
              <select
                value={formData.maxLoginAttempts}
                onChange={(e) => handleChange('maxLoginAttempts', Number(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value={3}>3 attempts</option>
                <option value={5}>5 attempts</option>
                <option value={10}>10 attempts</option>
                <option value={0}>Unlimited</option>
              </select>
              <p className="text-xs text-gray-500 mt-1">Lock account after failed attempts</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Lockout Duration (minutes)
              </label>
              <select
                value={formData.lockoutDuration}
                onChange={(e) => handleChange('lockoutDuration', Number(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                disabled={formData.maxLoginAttempts === 0}
              >
                <option value={5}>5 minutes</option>
                <option value={15}>15 minutes</option>
                <option value={30}>30 minutes</option>
                <option value={60}>1 hour</option>
                <option value={240}>4 hours</option>
                <option value={1440}>24 hours</option>
              </select>
              <p className="text-xs text-gray-500 mt-1">How long to lock accounts</p>
            </div>
          </div>
        </div>

        {/* Security Recommendations */}
        <div className="border-t pt-6">
          <h4 className="text-md font-medium text-gray-900 mb-4 flex items-center">
            <Shield className="w-4 h-4 mr-2" />
            Security Recommendations
          </h4>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <Shield className="w-5 h-5 text-blue-600 mt-0.5" />
              <div className="flex-1">
                <h5 className="text-sm font-medium text-blue-900 mb-2">Recommended Security Settings</h5>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Enable Two-Factor Authentication for enhanced security</li>
                  <li>• Set session timeout to 30 minutes or less</li>
                  <li>• Limit login attempts to 5 or fewer</li>
                  <li>• Set password expiry to 90 days or less</li>
                  <li>• Disable multiple sessions for sensitive environments</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Security Profiles */}
        <div className="border-t pt-6">
          <h4 className="text-md font-medium text-gray-900 mb-4">Security Profiles</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={() => {
                const basicSecurity = {
                  sessionTimeout: 60,
                  requireTwoFactorAuth: false,
                  allowMultipleSessions: true,
                  passwordExpiryDays: 180,
                  maxLoginAttempts: 10,
                  lockoutDuration: 15,
                };
                setFormData(basicSecurity);
                setHasChanges(JSON.stringify(basicSecurity) !== JSON.stringify(settings));
              }}
              className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-left"
            >
              <h5 className="font-medium text-gray-900 mb-1">Basic Security</h5>
              <p className="text-sm text-gray-500">Minimal security requirements</p>
            </button>

            <button
              onClick={() => {
                const standardSecurity = {
                  sessionTimeout: 30,
                  requireTwoFactorAuth: false,
                  allowMultipleSessions: false,
                  passwordExpiryDays: 90,
                  maxLoginAttempts: 5,
                  lockoutDuration: 30,
                };
                setFormData(standardSecurity);
                setHasChanges(JSON.stringify(standardSecurity) !== JSON.stringify(settings));
              }}
              className="p-4 border border-blue-300 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors text-left"
            >
              <h5 className="font-medium text-blue-900 mb-1">Standard Security</h5>
              <p className="text-sm text-blue-700">Recommended for most organizations</p>
            </button>

            <button
              onClick={() => {
                const highSecurity = {
                  sessionTimeout: 15,
                  requireTwoFactorAuth: true,
                  allowMultipleSessions: false,
                  passwordExpiryDays: 60,
                  maxLoginAttempts: 3,
                  lockoutDuration: 60,
                };
                setFormData(highSecurity);
                setHasChanges(JSON.stringify(highSecurity) !== JSON.stringify(settings));
              }}
              className="p-4 border border-red-300 bg-red-50 rounded-lg hover:bg-red-100 transition-colors text-left"
            >
              <h5 className="font-medium text-red-900 mb-1">High Security</h5>
              <p className="text-sm text-red-700">Maximum security for sensitive data</p>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SecuritySettings;
