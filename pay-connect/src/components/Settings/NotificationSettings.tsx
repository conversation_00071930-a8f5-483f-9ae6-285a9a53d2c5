import React, { useState, useEffect } from 'react';
import { Bell, Mail, MessageSquare, Smartphone, Calendar, FileText } from 'lucide-react';
import { NotificationSettings as NotificationSettingsType } from '../../services/settings';

interface NotificationSettingsProps {
  settings: NotificationSettingsType;
  onUpdate: (settings: NotificationSettingsType) => void;
  isLoading?: boolean;
}

const NotificationSettings: React.FC<NotificationSettingsProps> = ({ settings, onUpdate, isLoading }) => {
  const [formData, setFormData] = useState<NotificationSettingsType>(settings);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    setFormData(settings);
    setHasChanges(false);
  }, [settings]);

  const handleChange = (field: keyof NotificationSettingsType, value: boolean) => {
    const newData = { ...formData, [field]: value };
    setFormData(newData);
    setHasChanges(JSON.stringify(newData) !== JSON.stringify(settings));
  };

  const handleSave = () => {
    onUpdate(formData);
    setHasChanges(false);
  };

  const handleReset = () => {
    setFormData(settings);
    setHasChanges(false);
  };

  const ToggleSwitch: React.FC<{ checked: boolean; onChange: (checked: boolean) => void }> = ({ checked, onChange }) => (
    <label className="relative inline-flex items-center cursor-pointer">
      <input
        type="checkbox"
        checked={checked}
        onChange={(e) => onChange(e.target.checked)}
        className="sr-only peer"
      />
      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
    </label>
  );

  return (
    <div className="bg-white rounded-xl shadow-sm border p-6">
      <div className="flex items-center justify-between mb-6">
        <div >
          
          <div className="flex items-center space-x-3">
            <h3 className="text-lg font-semibold text-gray-900">Notification Settings</h3>
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
            <Bell className="w-5 h-5 text-purple-600" />
          </div>
            
          </div>
          <p className="text-sm text-gray-500">Configure how you receive notifications</p>
        </div>
        {hasChanges && (
          <div className="flex space-x-2">
            <button
              onClick={handleReset}
              className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Reset
            </button>
            <button
              onClick={handleSave}
              disabled={isLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              Save Changes
            </button>
          </div>
        )}
      </div>

      <div className="space-y-6">
        {/* Communication Channels */}
        <div>
          <h4 className="text-md font-medium text-gray-900 mb-4">Communication Channels</h4>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Mail className="w-5 h-5 text-blue-600" />
                <div>
                  <h5 className="text-sm font-medium text-gray-900">Email Notifications</h5>
                  <p className="text-sm text-gray-500">Receive notifications via email</p>
                </div>
              </div>
              <ToggleSwitch
                checked={formData.emailNotifications}
                onChange={(checked) => handleChange('emailNotifications', checked)}
              />
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <MessageSquare className="w-5 h-5 text-green-600" />
                <div>
                  <h5 className="text-sm font-medium text-gray-900">SMS Notifications</h5>
                  <p className="text-sm text-gray-500">Receive notifications via SMS</p>
                </div>
              </div>
              <ToggleSwitch
                checked={formData.smsNotifications}
                onChange={(checked) => handleChange('smsNotifications', checked)}
              />
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Smartphone className="w-5 h-5 text-purple-600" />
                <div>
                  <h5 className="text-sm font-medium text-gray-900">Push Notifications</h5>
                  <p className="text-sm text-gray-500">Receive push notifications in the app</p>
                </div>
              </div>
              <ToggleSwitch
                checked={formData.pushNotifications}
                onChange={(checked) => handleChange('pushNotifications', checked)}
              />
            </div>
          </div>
        </div>

        {/* Payment Notifications */}
        <div className="border-t pt-6">
          <h4 className="text-md font-medium text-gray-900 mb-4">Payment Notifications</h4>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Bell className="w-5 h-5 text-yellow-600" />
                <div>
                  <h5 className="text-sm font-medium text-gray-900">Payment Reminders</h5>
                  <p className="text-sm text-gray-500">Get reminded about upcoming payments</p>
                </div>
              </div>
              <ToggleSwitch
                checked={formData.paymentReminders}
                onChange={(checked) => handleChange('paymentReminders', checked)}
              />
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Bell className="w-5 h-5 text-green-600" />
                <div>
                  <h5 className="text-sm font-medium text-gray-900">Payment Confirmations</h5>
                  <p className="text-sm text-gray-500">Get notified when payments are processed</p>
                </div>
              </div>
              <ToggleSwitch
                checked={formData.paymentConfirmations}
                onChange={(checked) => handleChange('paymentConfirmations', checked)}
              />
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Bell className="w-5 h-5 text-red-600" />
                <div>
                  <h5 className="text-sm font-medium text-gray-900">Overdue Notifications</h5>
                  <p className="text-sm text-gray-500">Get alerted about overdue payments</p>
                </div>
              </div>
              <ToggleSwitch
                checked={formData.overdueNotifications}
                onChange={(checked) => handleChange('overdueNotifications', checked)}
              />
            </div>
          </div>
        </div>

        {/* Reports */}
        <div className="border-t pt-6">
          <h4 className="text-md font-medium text-gray-900 mb-4">Reports</h4>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Calendar className="w-5 h-5 text-blue-600" />
                <div>
                  <h5 className="text-sm font-medium text-gray-900">Weekly Reports</h5>
                  <p className="text-sm text-gray-500">Receive weekly payment summary reports</p>
                </div>
              </div>
              <ToggleSwitch
                checked={formData.weeklyReports}
                onChange={(checked) => handleChange('weeklyReports', checked)}
              />
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <FileText className="w-5 h-5 text-green-600" />
                <div>
                  <h5 className="text-sm font-medium text-gray-900">Monthly Reports</h5>
                  <p className="text-sm text-gray-500">Receive monthly payment summary reports</p>
                </div>
              </div>
              <ToggleSwitch
                checked={formData.monthlyReports}
                onChange={(checked) => handleChange('monthlyReports', checked)}
              />
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="border-t pt-6">
          <h4 className="text-md font-medium text-gray-900 mb-4">Quick Actions</h4>
          <div className="flex flex-wrap gap-3">
            <button
              onClick={() => {
                const allEnabled = {
                  emailNotifications: true,
                  smsNotifications: true,
                  pushNotifications: true,
                  paymentReminders: true,
                  paymentConfirmations: true,
                  overdueNotifications: true,
                  weeklyReports: true,
                  monthlyReports: true,
                };
                setFormData(allEnabled);
                setHasChanges(JSON.stringify(allEnabled) !== JSON.stringify(settings));
              }}
              className="px-4 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors"
            >
              Enable All
            </button>
            <button
              onClick={() => {
                const allDisabled = {
                  emailNotifications: false,
                  smsNotifications: false,
                  pushNotifications: false,
                  paymentReminders: false,
                  paymentConfirmations: false,
                  overdueNotifications: false,
                  weeklyReports: false,
                  monthlyReports: false,
                };
                setFormData(allDisabled);
                setHasChanges(JSON.stringify(allDisabled) !== JSON.stringify(settings));
              }}
              className="px-4 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors"
            >
              Disable All
            </button>
            <button
              onClick={() => {
                const essentialOnly = {
                  emailNotifications: true,
                  smsNotifications: false,
                  pushNotifications: true,
                  paymentReminders: true,
                  paymentConfirmations: true,
                  overdueNotifications: true,
                  weeklyReports: false,
                  monthlyReports: true,
                };
                setFormData(essentialOnly);
                setHasChanges(JSON.stringify(essentialOnly) !== JSON.stringify(settings));
              }}
              className="px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
            >
              Essential Only
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationSettings;
