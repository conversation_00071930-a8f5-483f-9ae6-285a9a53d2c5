import React from 'react';
import { Wallet, Calendar, CreditCard } from 'lucide-react';

interface SummaryCardProps {
  title: string;
  value: number;
  className?: string;
  onClick?: () => void;
  selected?: boolean;
}

const SummaryCard: React.FC<SummaryCardProps> = ({
  title,
  value,
  className,
  onClick,
  selected = false
}) => {
  const formatValue = (val: number) => {
    return `$${val.toLocaleString(undefined, { minimumFractionDigits: 2 })}`;
  };

  const getIcon = () => {
    const iconProps = {
      size: 24,
      className: `transition-colors ${selected ? 'text-blue-600' : 'text-gray-500'}`
    };

    switch (title) {
      case "Total Received Amount":
        return <Wallet {...iconProps} />;
      case "Over due Amount":
        return <Calendar {...iconProps} />;
      case "Pending league Payment":
        return <CreditCard {...iconProps} />;
      default:
        return null;
    }
  };

  return (
    <div
      className={`
        rounded-[25px] shadow-sm flex items-center justify-between h-[120px] border p-8 mb-8 cursor-pointer transition-all duration-200
        ${selected
          ? 'border-blue-300 bg-blue-50 shadow-md transform scale-105'
          : 'border-gray-200 bg-white hover:shadow-md hover:border-gray-300'}
        ${className}
      `}
      onClick={onClick}
    >
      <div className="flex-1">
        <h3
          className={`text-sm font-medium mb-2 ${
            selected ? 'text-blue-700' : 'text-gray-600'
          }`}
        >
          {title}
        </h3>
        <p
          className={`text-2xl font-bold ${
            selected ? 'text-blue-900' : 'text-gray-900'
          }`}
        >
          {formatValue(value)}
        </p>
      </div>

      <div
        className={`w-12 h-12 rounded-full flex items-center justify-center border 
          ${selected ? 'bg-white border-blue-500' : 'bg-gray-100 border-white'}
        `}
      >
        {getIcon()}
      </div>
    </div>
  );
};

export default SummaryCard;
