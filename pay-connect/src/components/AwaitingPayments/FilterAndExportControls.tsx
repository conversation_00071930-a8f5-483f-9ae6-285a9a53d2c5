import React, { useEffect, useState } from 'react';
import { getAwaitingFiltersById } from '../../services/awaitingPayments';
import { Download, Filter } from 'lucide-react';

interface FilterAndExportControlsProps {
  className?: string;
  selectedGame: string | null;
  selectedSeason: string | null;
  selectedGroup: string | null;
  onGameChange: (value: string | null) => void;
  onSeasonChange: (value: string | null) => void;
  onGroupChange: (value: string | null) => void;
}


// Add interface for filter modal options
interface ModalFilterOption {
  game_id: number;
  game_title: string;
  season_id: number;
  season_name: string;
  game_group_id: number;
  group_name: string;
  status_id: number;
  created_at: string;
}

const FilterAndExportControls: React.FC<FilterAndExportControlsProps> = ({ className, selectedGame, selectedSeason, selectedGroup, onGameChange, onSeasonChange, onGroupChange }) => {
  const [filters, setFilters] = useState<ModalFilterOption[]>([]);
  // Local state for filter selections
  const [localGame, setLocalGame] = useState<string | null>(selectedGame);
  const [localSeason, setLocalSeason] = useState<string | null>(selectedSeason);
  const [localGroup, setLocalGroup] = useState<string | null>(selectedGroup);

  useEffect(() => {
    setLocalGame(selectedGame);
    setLocalSeason(selectedSeason);
    setLocalGroup(selectedGroup);
  }, [selectedGame, selectedSeason, selectedGroup]);

  useEffect(() => {
    const fetchFilters = async () => {
      const data = await getAwaitingFiltersById();
      setFilters(data || []);
    };
    fetchFilters();
  }, []);

  // Extract unique games
  const games = Array.from(new Set(filters.map(f => JSON.stringify({ id: f.game_id, title: f.game_title })))).map(s => JSON.parse(s));

  // Filter seasons based on selected game
  const filteredSeasons = filters.filter(f => !localGame || String(f.game_id) === localGame);
  const seasons = Array.from(new Set(filteredSeasons.map(f => JSON.stringify({ id: f.season_id, name: f.season_name })))).map(s => JSON.parse(s));

  // Filter groups based on selected game and season
  const filteredGroups = filters.filter(f =>
    (!localGame || String(f.game_id) === localGame) &&
    (!localSeason || String(f.season_id) === localSeason)
  );
  const groups = Array.from(new Set(filteredGroups.map(f => JSON.stringify({ id: f.game_group_id, name: f.group_name })))).map(s => JSON.parse(s));

  // When a higher filter changes, reset lower filters
  const handleGameChange = (value: string) => {
    setLocalGame(value === "" ? null : value);
    setLocalSeason(null);
    setLocalGroup(null);
  };
  const handleSeasonChange = (value: string) => {
    setLocalSeason(value === "" ? null : value)

    setLocalGroup(null);
  };

  const handleExport = () => {
    console.log('Exporting data...');
    // Implement export logic here
  };

  // When Filter button is clicked, call parent handlers
  const handleApplyFilters = () => {
    onGameChange(localGame);
    onSeasonChange(localSeason);
    onGroupChange(localGroup);
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 items-end">
      {/* Game Filter */}
      <div>
        <label className="block text-xs font-semibold text-gray-600 mb-1">Game</label>
        <select
          className="border rounded px-2 py-1 text-sm w-full"
          value={localGame ?? ""}
          onChange={e => handleGameChange(e.target.value)}
        >
          <option value="">All Games</option>
          {games.map(game => (
            <option key={game.id} value={game.id}>{game.title}</option>
          ))}
        </select>
      </div>

      {/* Season Filter */}
      <div>
        <label className="block text-xs font-semibold text-gray-600 mb-1">Season</label>
        <select
          className="border rounded px-2 py-1 text-sm w-full"
          value={localSeason ?? ""}
          onChange={e => handleSeasonChange(e.target.value)}
          disabled={!localGame}
        >
          <option value="">All Seasons</option>
          {seasons.map(season => (
            <option key={season.id} value={season.id}>{season.name}</option>
          ))}
        </select>
      </div>

      {/* Group Filter */}
      <div>
        <label className="block text-xs font-semibold text-gray-600 mb-1">Group</label>
        <select
          className="border rounded px-2 py-1 text-sm w-full"
          value={localGroup || ""}
          onChange={e => setLocalGroup(e.target.value === "" ? null : e.target.value)}
          disabled={!localSeason}
        >
          <option value="">All Groups</option>
          {groups.map(group => (
            <option key={group.id} value={group.id}>{group.name}</option>
          ))}
        </select>
      </div>

      {/* Filter Button */}
      <div className="flex items-center justify-center gap-3" >
        <div >
          <button
            type="button"
            className="inline-flex items-center rounded-md border border-blue-600 bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 w-full"
            onClick={handleApplyFilters}
          >
            Filter
            <Filter size={20} className="text-white ml-3" />
          </button>
        </div>

        {/* Export Button */}
        <div >
          <button
            type="button"
            className="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 w-full"
            onClick={handleExport}
          >
            Export
            <Download size={14} className="ml-1" />
          </button>
        </div>
      </div>
    </div>

  );
};

export default FilterAndExportControls;
