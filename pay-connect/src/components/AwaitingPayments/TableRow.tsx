import React from 'react';
import { AwaitingPayment } from '../../services/awaitingPayments';
import { Bell, Eye } from 'lucide-react';

interface TableRowProps extends AwaitingPayment {
  onStatusUpdate: (id: string, status: AwaitingPayment['status']) => void;
  onViewDetails: (id: string) => void;
}

const TableRow = React.forwardRef<HTMLTableRowElement, TableRowProps>(({
  id,
  payerName,
  team_name,
  email,
  phone,
  game_title,
  season_name,
  players_per_team,
  paid_amount,
  expected_amount,
  team_logo,
  end_date,
  status,
  avatar,
  onStatusUpdate,
  onViewDetails
}, ref) => {
  const getStatusColor = (status: AwaitingPayment['status']) => {
    switch (status) {
      case 'Paid':
        return 'bg-green-100 text-green-800';
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'Overdue':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleOpenOffcanvas = () => {
    onViewDetails(id);
  };

  return (
    <tr className="hover:bg-gray-50" ref={ref}>

      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {game_title} - {season_name}
      </td>


      <td
        className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 flex items-center cursor-pointer"
        onClick={handleOpenOffcanvas}
      >
        <img src={team_logo || `https://ui-avatars.com/api/?name=${encodeURIComponent(team_name)}`} className="h-8 w-8 rounded-full mr-3" />

        {team_name}
      </td>
      {/* <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{}</td> */}
      <td className="px-6 py-4 whitespace-nowrap" style={{ textAlign: 'end' }}>
        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full `}>
          {'$' + expected_amount}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap" style={{ textAlign: 'end' }}>

        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full `}>
          {'$' + paid_amount || 0}
        </span>
      </td>
      {/* <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${price.toFixed(2)}</td> */}
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {new Date(end_date).toLocaleDateString('en-US', { day: 'numeric', month: 'short', year: 'numeric' })}
      </td>
      <td className="px-6 py-4 whitespace-nowrap" style={{ textAlign: 'end' }}>
        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full`}>
          {players_per_team}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(status)}`}>
          {status}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium flex items-center justify-end space-x-2">
        <div className="relative group inline-block cursor-pointer ml-3 mt-1 p-2 rounded-full bg-blue-200 hover:bg-blue-300 text-blue-600 transition">
          <Bell size={18} className="send" />
          <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-1 
                  opacity-0 group-hover:opacity-100 transition 
                  text-xs text-white bg-black px-2 py-1 rounded whitespace-nowrap z-10">
            Send Reminder
          </div>
        </div>

        <button
          onClick={handleOpenOffcanvas}
          className="text-orange-500 hover:text-orange-500   ml-3 mt-1 p-2 rounded-full bg-orange-50 hover:bg-orange-100  text-orange-500 transition"
        >
          <Eye size={18} />
        </button>
        {/* <select
          value={status}
          onChange={(e) => onStatusUpdate(id, e.target.value as AwaitingPayment['status'])}
          className="text-sm border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="Pending">Pending</option>
          <option value="Overdue">Overdue</option>
          <option value="Paid">Paid</option>
        </select> */}
      </td>
    </tr>
  );
});

export default TableRow;
