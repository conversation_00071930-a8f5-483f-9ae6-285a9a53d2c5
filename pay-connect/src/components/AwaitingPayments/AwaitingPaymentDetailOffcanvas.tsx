import React, { useState, useEffect } from 'react';
import { getAwaitingPaymentById, sendReminder } from '../../services/awaitingPayments';
import { Mail, Phone, Bell } from 'lucide-react';
import { calculatePayment } from '../../utils/paymentUtils';
import PaymentLoader from '../common/PaymentLoader';
import Notification from '../common/Notification';

interface AwaitingPaymentDetailOffcanvasProps {
  paymentId: string | null;
  onClose: () => void;
}

interface AwaitingPaymentUser {
  user_id: number | null;
  firstname: string;
  avatar:string;
  email: string | null;
  role_name: string | null;
  await_payment?: number;
  status?: string;
  payment_date?: string; // Added
  player_charged_amount?: number; // Added
  paid?: number; // Added
}

interface AwaitingPaymentTeam {
  team_id: number;
  team_name: string;
  avatar:string;
  team_logo: string;
  season_name: string;
  league_name?: string;
  plan_date: string;
  final_day_of_payment: string;
  player_users:AwaitingPaymentUser[];
  other_users: AwaitingPaymentUser[];
}

const AwaitingPaymentDetailOffcanvas: React.FC<AwaitingPaymentDetailOffcanvasProps> = ({
  paymentId,
  onClose,
}) => {
  const [teamPayment, setTeamPayment] = useState<AwaitingPaymentTeam | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [notification, setNotification] = useState<{
    type: 'success' | 'error' | 'info';
    title: string;
    message?: string;
    isVisible: boolean;
  }>({
    type: 'info',
    title: '',
    isVisible: false,
  });
  const [isSendingReminder, setIsSendingReminder] = useState(false);

  useEffect(() => {
    if (paymentId) {
      fetchPaymentDetails(paymentId);
    } else {
      setTeamPayment(null);
      setLoading(false);
      setError(null);
    }
  }, [paymentId]);

  const fetchPaymentDetails = async (id: string) => {
    try {
      setLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      const data = await getAwaitingPaymentById(id);
      setTeamPayment(Array.isArray(data) ? data[0] : data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch awaiting payment details');
    } finally {
      setLoading(false);
    }
  };

  const handleSendReminder = async (user:any,final_day_of_payment:any , players:any) => {
    let userData={  final_day_of_payment, ...user }
    if(players && players.length){
      userData.players=players
    }
    setIsSendingReminder(true);
    try {
      await sendReminder(userData);
      setNotification({
        type: 'success',
        title: 'Reminder Sent!',
        message: 'Payment reminder has been sent successfully.',
        isVisible: true,
      });
    } catch (err: any) {
      setNotification({
        type: 'error',
        title: 'Failed to Send Reminder',
        message: err?.message || 'An error occurred while sending the reminder.',
        isVisible: true,
      });
    } finally {
      setIsSendingReminder(false);
    }
  };

  if (!paymentId) {
    return null;
  }

  if (loading) {
    return (
      <div className="p-6 text-center">
        <PaymentLoader
          type="team"
          message="Loading Team details..."
          size="medium"
          showQuotes={true}
        />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 bg-red-100 border border-red-400 text-red-700 rounded">
        {error}
      </div>
    );
  }

  if (!teamPayment) {
    return (
      <div className="p-6 text-gray-600">
        Team Members details not found.
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Notification Toast */}
      <Notification
        type={notification.type}
        title={notification.title}
        message={notification.message}
        isVisible={notification.isVisible}
        onClose={() => setNotification(prev => ({ ...prev, isVisible: false }))}
      />
      {/* Team/Payment Details */}
      <div className="bg-white rounded-xl shadow-sm border p-4 space-y-3">
        <div className="flex items-center space-x-3 mb-2">
            <img
        src={
         teamPayment.team_logo ||
          `https://ui-avatars.com/api/?name=${encodeURIComponent(teamPayment.team_name)}`
        }
        alt="Avatar"
        className="h-10 w-10 rounded-full mr-3 object-cover"
      />
          
          <div>
            <h3 className="text-lg font-bold text-gray-900">{teamPayment.team_name}</h3>
            <p className="text-sm text-gray-500">Season: <span className="font-semibold text-gray-900">{teamPayment.season_name}</span></p>
            {teamPayment.league_name && (
              <p className="text-sm text-gray-500">League: <span className="font-semibold text-gray-900">{teamPayment.league_name}</span></p>
            )}
            {/* <p className="text-sm text-gray-500">Plan Date: <span className="font-semibold text-gray-900">{teamPayment.plan_date}</span></p> */}
            <p className="text-sm text-gray-500">Final Day of Payment: <span className="font-semibold text-gray-900">{teamPayment.final_day_of_payment}</span></p>
          </div>
        </div>
      </div>
          <div className="space-y-2">
        {teamPayment.other_users.map((user, idx) => (
 <div
  key={user.user_id || idx}
  className="bg-white rounded-lg p-4 shadow-md mb-3 flex justify-between items-start"
>
  {/* Left: Avatar & Details */}
  <div className="flex-1">
    <div className="flex items-center mb-2">
      <img
        src={
          user.avatar ||
          `https://ui-avatars.com/api/?name=${encodeURIComponent(user.firstname)}`
        }
        alt="Avatar"
        className="h-10 w-10 rounded-full mr-3 object-cover"
      />
      <div>
        <p className="text-sm font-semibold text-gray-800">{user.firstname || 'N/A'}</p>
        <p className="text-xs text-gray-500">{user.email || 'N/A'}</p>
      </div>
    </div>

    <div className="grid grid-cols-2 gap-y-1 text-xs text-gray-500">
      <p className="font-medium text-gray-600">Role:</p>
      <p>{user.role_name || 'N/A'}</p>

      <p className="font-medium text-gray-600">Payment Date:</p>
      <p>{user.payment_date || 'N/A'}</p>

      <p className="font-medium text-gray-600">Charges:</p>
      <p  className={user.paid == 1 ? 'text-green-600 font-medium' : 'text-red-500 font-medium'}>{user.player_charged_amount !== undefined ? user.player_charged_amount : 'N/A'}</p>

   </div>
  </div>

  {/* Right: Reminder Button */}
  {user.paid != 1 && (
    <button  
      onClick={() => handleSendReminder(user,teamPayment.final_day_of_payment ,teamPayment?.player_users.length ? teamPayment?.player_users.filter((item=>item.paid == 0)) :null )}
      className="ml-3 mt-1 p-2 rounded-full bg-blue-600 hover:bg-blue-700 text-white transition"
      title="Send Payment Reminder"
      disabled={isSendingReminder}
    >
      <Bell size={16} />
    </button>


  )}
</div>


        ))}
      </div>
      <div className="space-y-2">
        {teamPayment.player_users.map((user, idx) => (
 <div
  key={user.user_id || idx}
  className="bg-white rounded-lg p-4 shadow-md mb-3 flex justify-between items-start"
>
  {/* Left: Avatar & Details */}
  <div className="flex-1">
    <div className="flex items-center mb-2">
      <img
        src={
          user.avatar ||
          `https://ui-avatars.com/api/?name=${encodeURIComponent(user.firstname)}`
        }
        alt="Avatar"
        className="h-10 w-10 rounded-full mr-3 object-cover"
      />
      <div>
        <p className="text-sm font-semibold text-gray-800">{user.firstname || 'N/A'}</p>
        <p className="text-xs text-gray-500">{user.email || 'N/A'}</p>
      </div>
    </div>

    <div className="grid grid-cols-2 gap-y-1 text-xs text-gray-500">
      <p className="font-medium text-gray-600">Role:</p>
      <p>{user.role_name || 'N/A'}</p>

      <p className="font-medium text-gray-600">Payment Date:</p>
      <p>{user.payment_date || 'N/A'}</p>

      <p className="font-medium text-gray-600">Charges:</p>
      <p  className={user.paid == 1 ? 'text-green-600 font-medium' : 'text-red-500 font-medium'}>{user.player_charged_amount !== undefined ? user.player_charged_amount : 'N/A'}</p>

   </div>
  </div>

  {/* Right: Reminder Button */}
  {user.paid != 1 && (
    <button
      onClick={() => handleSendReminder(user,teamPayment.final_day_of_payment,null)}
      className="ml-3 mt-1 p-2 rounded-full bg-blue-600 hover:bg-blue-700 text-white transition"
      title="Send Payment Reminder"
      disabled={isSendingReminder}
    >
      <Bell size={16} />
    </button>
  )}
</div>


        ))}
      </div>
    
    </div>
  );
};

export default AwaitingPaymentDetailOffcanvas;
