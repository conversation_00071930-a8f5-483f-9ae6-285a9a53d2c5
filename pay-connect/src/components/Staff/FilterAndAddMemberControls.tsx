import React from 'react';
import { Download } from 'lucide-react';

interface FilterAndAddMemberControlsProps {
  className?: string;
  onFilterChange: (role: string) => void;
  onImportUsers: () => void;
  currentFilter: string;
}

const FilterAndAddMemberControls: React.FC<FilterAndAddMemberControlsProps> = ({
  className,
  onFilterChange,
  onImportUsers,
  currentFilter
}) => {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 gap-4 items-center ${className}`}>
      {/* Left Column (Filter) */}
      <div className="col-span-1 flex items-center gap-3">
        <label htmlFor="filter" className="text-gray-700 text-sm font-medium whitespace-nowrap">Filter:</label>
        <select
          id="filter"
          className="w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          value={currentFilter}
          onChange={(e) => onFilterChange(e.target.value)}
        >
          <option value="All">All</option>
          <option value="Office Staff">Office Staff</option>
          <option value="Referee">Referee</option>
          <option value="Other Staff">Other Staff</option>
        </select>
      </div>

      {/* Right Column (Import Button) */}
      <div className="col-span-1 flex justify-start ">
        <button
          onClick={onImportUsers}
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Import Users
          <Download className="ml-3 h-4 w-4" aria-hidden="true" />
        </button>
      </div>
    </div>

  );
};

export default FilterAndAddMemberControls; 