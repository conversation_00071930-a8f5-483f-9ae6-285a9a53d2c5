import React from 'react';
import { StaffMember } from '../../services/staff';
import { Building2, Send, History, ExternalLink } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface StaffTableRowProps {
  staff: StaffMember;
  onDelete: (id: string) => void;
  onAddBankAccount: (staff: StaffMember) => void;
  onBankTransfer: (staff: StaffMember) => void;
  onViewTransactions?: (staff: StaffMember) => void;
}

const StaffTableRow: React.FC<StaffTableRowProps> = ({ 
  staff, 
  onAddBankAccount, 
  onBankTransfer
}) => {
  const navigate = useNavigate();

  const handleViewDetails = () => {
    navigate(`/staff/${staff.id}`);
  };

  const handleAddBankAccount = () => {
    onAddBankAccount(staff);
  };

  const handleBankTransfer = () => {
    onBankTransfer(staff);
  };


  return (
    <tr className="hover:bg-gray-50">
      <td className="px-6 py-4 whitespace-nowrap">
        <input type="checkbox" className="form-checkbox h-4 w-4 text-indigo-600 transition duration-150 ease-in-out" />
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            <img className="h-10 w-10 rounded-full" src={staff.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(staff.name)}` } alt="" />
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">
              <button 
                onClick={handleViewDetails}
                className="hover:text-blue-600 flex items-center"
              >
                {staff.name}
                <ExternalLink size={14} className="ml-1 text-gray-400" />
              </button>
            </div>
          </div>
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {staff.email}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {staff.contact}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
          staff.role === 'Referee' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
        }`}>
          {staff.role}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <div className="flex items-center justify-start space-x-2">
          <button
            onClick={handleAddBankAccount}
            className="text-blue-500 hover:text-blue-500 ml-3 mt-1 p-2 rounded-full bg-blue-50 hover:bg-blue-100 text-blue-500 transition"
            title="Add Bank Account"
          >
            <Building2 size={18} />
          </button>
          <button
            onClick={handleBankTransfer}
            className="text-green-500 hover:text-green-500 ml-3 mt-1 p-2 rounded-full bg-green-50 hover:bg-green-100 text-green-500 transition"
            title="Bank Transfer"
          >
            <Send size={18} />
          </button>
          <button
             onClick={handleViewDetails}
            className="text-purple-500 hover:text-purple-500 ml-3 mt-1 p-2 rounded-full bg-purple-50 hover:bg-purple-100 text-purple-500 transition"
            title="View Transaction History"
          >
            <History size={18} />
          </button>
        </div>
      </td>
    </tr>
  );
};

export default StaffTableRow; 
