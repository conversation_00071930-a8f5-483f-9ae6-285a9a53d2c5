import React, { useState, useEffect } from 'react';
import { X, Search, Users, Download, Check, Loader2, Filter } from 'lucide-react';
import { ImportableUser, getImportableUsers, importUsers, getAvailableDepartments } from '../../services/staff';
import PaymentLoader from '../common/PaymentLoader';

interface ImportUsersModalProps {
  isOpen: boolean;
  onClose: () => void;
  onImportSuccess: () => void;
}

const ImportUsersModal: React.FC<ImportUsersModalProps> = ({ isOpen, onClose, onImportSuccess }) => {
  const [users, setUsers] = useState<ImportableUser[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<ImportableUser[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<Set<string>>(new Set());
  const [searchTerm, setSearchTerm] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState('All');
  const [isLoading, setIsLoading] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [departments, setDepartments] = useState<string[]>([]);

  useEffect(() => {
    if (isOpen) {
      fetchUsers();
      fetchDepartments();
    }
  }, [isOpen]);

  const fetchDepartments = async () => {
    try {
      const departments = await getAvailableDepartments();
      setDepartments(departments);
    } catch (error) {
      console.error('Error fetching departments:', error);
    }
  };

  const filterUsers = React.useCallback(() => {
    let filtered = [...users];

    if (departmentFilter !== 'All') {
      filtered = filtered.filter(user => user.department === departmentFilter);
    }

    if (searchTerm) {
      const search = searchTerm.toLowerCase();
      filtered = filtered.filter(user =>
        user.name.toLowerCase().includes(search) ||
        user.email.toLowerCase().includes(search) ||
        user.position.toLowerCase().includes(search)
      );
    }

    setFilteredUsers(filtered);
  }, [users, searchTerm, departmentFilter]);

  useEffect(() => {
    filterUsers();
  }, [users, searchTerm, departmentFilter, filterUsers]);

  const fetchUsers = async () => {
    setIsLoading(true);
    try {
      const data = await getImportableUsers({ department: departmentFilter, search: searchTerm });
      setUsers(data);
    } catch (error) {
      console.error('Error fetching importable users:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUserSelect = (userId: string) => {
    const newSelected = new Set(selectedUsers);
    if (newSelected.has(userId)) {
      newSelected.delete(userId);
    } else {
      newSelected.add(userId);
    }
    setSelectedUsers(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedUsers.size === filteredUsers.length) {
      setSelectedUsers(new Set());
    } else {
      setSelectedUsers(new Set(filteredUsers.map(user => user.id)));
    }
  };

  const handleImport = async () => {
    if (selectedUsers.size === 0) return;

    setIsImporting(true);
    try {
      const usersToImport = users.filter(user => selectedUsers.has(user.id));
      const result = await importUsers(usersToImport, 'Staff');

      if (result.success) {
        alert(result.message);
        onImportSuccess();
        onClose();
      } else {
        alert(result.message);
      }
    } catch (error) {
      alert('Failed to import users. Please try again.');
    } finally {
      setIsImporting(false);
    }
  };

  const handleClose = () => {
    setSelectedUsers(new Set());
    setSearchTerm('');
    setDepartmentFilter('All');
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <Users className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Import Users</h2>
              <p className="text-sm text-gray-500">Select users from external database to import as staff</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100 transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Filters */}
        <div className="p-6 border-b border-gray-200 bg-gray-50">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                disabled={true}
                type="text"
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Department Filter */}
            <div className="relative">
              <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <select
               disabled={true}
                value={departmentFilter}
                onChange={(e) => setDepartmentFilter(e.target.value)}
                className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              >
                <option value="All">All Departments</option>
                {departments.map(dept => (
                  <option key={dept} value={dept}>{dept}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Selection Summary */}
          <div className="flex items-center justify-between mt-4">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleSelectAll}
                className="text-sm text-blue-600 hover:text-blue-700 font-medium"
              >
                {selectedUsers.size === filteredUsers.length ? 'Deselect All' : 'Select All'}
              </button>
              <span className="text-sm text-gray-600">
                {selectedUsers.size} of {filteredUsers.length} users selected
              </span>
            </div>
          </div>
        </div>

        {/* User List */}
        <div className="flex-1 overflow-y-auto max-h-96">
          {isLoading ? (
            <div className="py-12">
              <PaymentLoader
                type="setup"
                message="Loading users..."
                size="medium"
                showQuotes={true}
              />
            </div>
          ) : filteredUsers.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 text-gray-500">
              <Users className="w-12 h-12 mb-4 text-gray-300" />
              <p className="text-lg font-medium">No users found</p>
              <p className="text-sm">Try adjusting your search or filter criteria</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {filteredUsers.map((user) => (
                <div
                  key={user.id}
                  className={`p-4 hover:bg-gray-50 transition-colors cursor-pointer ${
                    selectedUsers.has(user.id) ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                  }`}
                  onClick={() => handleUserSelect(user.id)}
                >
                  <div className="flex items-center space-x-4">
                    <div className="relative">
                      <img
                        src={user.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name)}`}
                        alt={user.name}
                        className="w-12 h-12 rounded-full object-cover"
                      />
                      {selectedUsers.has(user.id) && (
                        <div className="absolute -top-1 -right-1 w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center">
                          <Check className="w-3 h-3 text-white" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h3 className="text-sm font-medium text-gray-900 truncate">{user.name}</h3>
                        <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                          {user.department}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 truncate">{user.position}</p>
                      <div className="flex items-center space-x-4 mt-1">
                        <span className="text-xs text-gray-500">{user.email}</span>
                        <span className="text-xs text-gray-500">{user.contact}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-600">
            {selectedUsers.size > 0 && (
              <span>{selectedUsers.size} user{selectedUsers.size !== 1 ? 's' : ''} selected for import</span>
            )}
          </div>
          <div className="flex space-x-3">
            <button
              onClick={handleClose}
              className="px-4 py-2 text-red-600 bg-white border border-red-300 rounded-lg hover:bg-red-600 hover:text-white  transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleImport}
              disabled={selectedUsers.size === 0 || isImporting}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
            >
              {isImporting ? (
                <div className="flex items-center justify-center space-x-3">
                  <PaymentLoader
                    type="setup"
                    size="small"
                    showQuotes={false}
                  />
                  <span>Importing users...</span>
                </div>
              ) : (
                <>
                  <Download className="w-4 h-4" />
                  <span>Import Selected ({selectedUsers.size})</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImportUsersModal;
