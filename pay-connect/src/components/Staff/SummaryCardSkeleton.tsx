import React from 'react';

const SummaryCardSkeleton: React.FC = () => {
  return (
    <div className="rounded-[20px] shadow-sm flex items-center justify-between h-[120px] border p-8 mb-8 bg-white animate-pulse">
      <div className="flex-1">
        {/* Title skeleton */}
        <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
        {/* Value skeleton */}
        <div className="h-8 bg-gray-200 rounded w-16"></div>
      </div>
      
      {/* Icon skeleton */}
      <div className="w-12 h-12 rounded-full bg-gray-200"></div>
    </div>
  );
};

export default SummaryCardSkeleton;