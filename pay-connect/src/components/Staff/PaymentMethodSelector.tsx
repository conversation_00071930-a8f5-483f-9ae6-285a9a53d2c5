import React, { useState, useEffect } from 'react';
import { ChevronDown, Clock, DollarSign, Info } from 'lucide-react';
import { StaffBankAccount } from '../../services/staff';
import { getAvailablePaymentMethods, getPaymentMethods } from '../../services/paymentMethods';

export interface PaymentMethod {
  id: string;
  name: string;
  type: 'ach' | 'wire' | 'instant';
  processingTime: string;
  fee: number | null;
  feeDescription?: string;
  description: string;
  isDefault?: boolean;
  method_id?: string;
  fee_type?: 'fixed' | 'percentage' | 'hybrid';
  percentage_fee?: number;
  min_fee?: number;
  max_fee?: number;
}

// Backend PaymentMethod interface (for API responses)
interface BackendPaymentMethod {
  method_id: string;
  name: string;
  description: string;
  processing_time: string;
  fee_type: 'fixed' | 'percentage' | 'hybrid';
  percentage_fee?: number;
  min_fee?: number;
  max_fee?: number;
  is_active: boolean;
  method_type: 'withdrawal' | 'deposit' | 'both';
  min_amount: number;
  max_amount: number | null;
  display_order: number;
}

interface PaymentMethodSelectorProps {
  selectedBankAccount: StaffBankAccount | null;
  selectedPaymentMethod: PaymentMethod | null;
  onPaymentMethodChange: (method: PaymentMethod) => void;
  disabled?: boolean;
}

const PaymentMethodSelector: React.FC<PaymentMethodSelectorProps> = ({
  selectedBankAccount,
  selectedPaymentMethod,
  onPaymentMethodChange,
  disabled = false
}) => {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDropdown, setShowDropdown] = useState(false);

  useEffect(() => {
    if (selectedBankAccount) {
      // With single bank account model, we just need to know there's an account
      // The specific ID is less important since there's only one active account
      fetchPaymentMethods(selectedBankAccount.id.toString());
    } else {
      setPaymentMethods([]);
      if (selectedPaymentMethod) {
        onPaymentMethodChange(null as unknown as PaymentMethod);
      }
    }
  }, [selectedBankAccount]);

  const fetchPaymentMethods = async (bankAccountId: string) => {
    try {
      setLoading(true);
      setError(null);

      // Get real payment methods from database via API
      const methods = await getPaymentMethods('withdrawal') as unknown as BackendPaymentMethod[];

      if (methods && methods.length > 0) {
        // Convert database PaymentMethod to component PaymentMethod format
        const convertedMethods: PaymentMethod[] = methods.map((method: BackendPaymentMethod) => {
          // Calculate display fee based on fee_type
          let displayFee = 0;
          let feeDescription = '';

          switch (method.fee_type) {
            case 'fixed': {
              displayFee = parseFloat(method.min_fee?.toString() || '0') || 0;
              feeDescription = displayFee === 0 ? 'Free' : `$${displayFee.toFixed(2)}`;
              break;
            }
            case 'percentage': {
              const percentageFee = parseFloat(method.percentage_fee?.toString() || '0') || 0;
              const minFee = parseFloat(method.min_fee?.toString() || '0') || 0;
              const maxFee = parseFloat(method.max_fee?.toString() || '0') || 0;
              feeDescription = `${percentageFee}%`;
              if (minFee > 0 || maxFee > 0) {
                feeDescription += ` (min $${minFee.toFixed(2)}`;
                if (maxFee > 0) {
                  feeDescription += `, max $${maxFee.toFixed(2)}`;
                }
                feeDescription += ')';
              }
              displayFee = percentageFee;
              break;
            }
            case 'hybrid': {
              const hybridPercentage = parseFloat(method.percentage_fee?.toString() || '0') || 0;
              const hybridMin = parseFloat(method.min_fee?.toString() || '0') || 0;
              const hybridMax = parseFloat(method.max_fee?.toString() || '0') || 0;
              feeDescription = `$${hybridMin.toFixed(2)} + ${hybridPercentage}%`;
              if (hybridMax > 0) {
                feeDescription += ` (max $${hybridMax.toFixed(2)})`;
              }
              displayFee = hybridPercentage;
              break;
            }
            default: {
              displayFee = 0;
              feeDescription = 'Free';
            }
          }

          return {
            id: method.method_id,
            name: method.name,
            type: method.method_id.includes('wire') ? 'wire' :
                  method.method_id.includes('instant') ? 'instant' : 'ach',
            processingTime: method.processing_time,
            fee: displayFee,
            feeDescription: feeDescription, // Add fee description for display
            description: method.description,
            isDefault: method.method_id === 'ach_standard', // Set ACH Standard as default
            method_id: method.method_id,
            fee_type: method.fee_type,
            percentage_fee: method.percentage_fee,
            min_fee: method.min_fee,
            max_fee: method.max_fee
          };
        });

        setPaymentMethods(convertedMethods);

        // Auto-select default method if none selected
        if (!selectedPaymentMethod && convertedMethods.length > 0) {
          const defaultMethod = convertedMethods.find(m => m.isDefault) || convertedMethods[0];
          onPaymentMethodChange(defaultMethod);
        }
      } else {
        // Fallback to mock data if database is empty
        console.warn('No payment methods found in database, using fallback');
        const mockMethods = getMockPaymentMethods();
        setPaymentMethods(mockMethods);

        if (mockMethods.length > 0) {
          const defaultMethod = mockMethods.find(method => method.isDefault) || mockMethods[0];
          onPaymentMethodChange(defaultMethod);
        }
      }
    } catch (err) {
      console.error('Error fetching payment methods from database:', err);
      setError('Failed to load payment methods from database');

      // Use mock data as fallback
      const mockMethods = getMockPaymentMethods();
      setPaymentMethods(mockMethods);

      if (mockMethods.length > 0) {
        const defaultMethod = mockMethods.find(method => method.isDefault) || mockMethods[0];
        onPaymentMethodChange(defaultMethod);
      }
    } finally {
      setLoading(false);
    }
  };
  
  // Updated payment methods to match Plaid capabilities
  const getMockPaymentMethods = (): PaymentMethod[] => {
    return [
      {
        id: 'ach-standard',
        name: 'ACH Standard',
        type: 'ach',
        processingTime: '1-3 business days',
        fee: 0,
        description: 'Free standard transfer - most economical',
        isDefault: true
      },
      {
        id: 'ach-same-day',
        name: 'ACH Same Day',
        type: 'ach',
        processingTime: 'Same business day',
        fee: 1,
        description: 'Faster processing for $1 fee'
      },
      {
        id: 'wire-transfer',
        name: 'Wire Transfer',
        type: 'wire',
        processingTime: 'Same business day',
        fee: 25,
        description: 'Secure transfer for large amounts ($1,000+)'
      }
    ];
  };

  const formatFee = (method: PaymentMethod): string => {
    // Use feeDescription if available, otherwise fall back to fee formatting
    if (method.feeDescription) {
      return method.feeDescription;
    }

    if (method.fee === null || method.fee === 0) return 'Free';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(method.fee);
  };

  const getMethodTypeIcon = (type: string) => {
    switch (type) {
      case 'ach':
        return <Clock size={16} className="text-blue-500" />;
      case 'wire':
        return <DollarSign size={16} className="text-green-500" />;
      case 'instant':
        return (
          <svg className="w-4 h-4 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
          </svg>
        );
      default:
        return <Info size={16} className="text-gray-500" />;
    }
  };

  if (!selectedBankAccount) {
    return (
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 text-center">
        <p className="text-sm text-gray-500">Select a bank account to view payment methods</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 text-center">
        <p className="text-sm text-gray-500">Loading payment methods...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-3 text-center">
        <p className="text-sm text-red-600">{error}</p>
      </div>
    );
  }

  if (paymentMethods.length === 0) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 text-center">
        <p className="text-sm text-yellow-700">No payment methods available for this account</p>
      </div>
    );
  }

  return (
    <div className="relative">
      <div className="flex items-center justify-between mb-2">
        <label className="block text-sm font-medium text-gray-700">
          Payment Method
        </label>
        <div className="relative group">
          <div className="cursor-help flex items-center text-gray-500 hover:text-gray-700">
            <Info size={14} />
          </div>
          <div className="absolute right-0 bottom-full mb-2 w-64 bg-gray-800 text-white text-xs rounded p-2 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity z-10">
            Different payment methods have varying processing times and fees.
            Select the method that best suits your needs.
          </div>
        </div>
      </div>
      <button
        type="button"
        onClick={() => !disabled && setShowDropdown(!showDropdown)}
        className={`w-full bg-white border ${disabled ? 'border-gray-200 bg-gray-50' : 'border-gray-300 hover:border-blue-500'
          } rounded-lg p-3 flex items-center justify-between focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-colors`}
        disabled={disabled}
      >
        {selectedPaymentMethod ? (
          <div className="flex items-center space-x-3">
            {getMethodTypeIcon(selectedPaymentMethod.type)}
            <div className="text-left">
              <div className="font-medium text-sm">{selectedPaymentMethod.name}</div>
              <div className="text-xs text-gray-500 flex items-center space-x-2">
                <span>{selectedPaymentMethod.processingTime}</span>
                <span>•</span>
                <span>{formatFee(selectedPaymentMethod)}</span>
              </div>
            </div>
          </div>
        ) : (
          <span className="text-gray-500">Select payment method</span>
        )}
        <ChevronDown size={16} className={`text-gray-400 transition-transform ${showDropdown ? 'rotate-180' : ''}`} />
      </button>

      {showDropdown && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto">
          {paymentMethods.map((method) => (
            <button
              key={method.id}
              type="button"
              onClick={() => {
                onPaymentMethodChange(method);
                setShowDropdown(false);
              }}
              className={`w-full p-3 text-left hover:bg-gray-50 flex items-center space-x-3 ${selectedPaymentMethod?.id === method.id ? 'bg-blue-50' : ''
                }`}
            >
              {getMethodTypeIcon(method.type)}
              <div>
                <div className="font-medium text-sm">{method.name}</div>
                <div className="text-xs text-gray-500">{method.processingTime} • {formatFee(method)}</div>
                <div className="text-xs text-gray-500 mt-1">{method.description}</div>
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default PaymentMethodSelector;