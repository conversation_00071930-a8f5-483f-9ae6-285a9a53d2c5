import React, { useState, useEffect } from 'react';
import { X, Send, Building2, Wallet, ChevronDown, AlertCircle, Star, Lock } from 'lucide-react';
import { transferToStaffBank, getStaffBankAccountsById, StaffBankAccount } from '../../services/staff';
import PaymentMethodSelector, { PaymentMethod } from './PaymentMethodSelector';
import { getWallet } from '../../services/wallet';

interface StaffBankTransferModalProps {
  isOpen: boolean;
  onClose: () => void;
  staffId: string;
  staffName: string;
  onSuccess: () => void;
}

const StaffBankTransferModal: React.FC<StaffBankTransferModalProps> = ({
  isOpen,
  onClose,
  staffId,
  staffName,
  onSuccess
}) => {
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [pin, setPin] = useState('');
  const [staffBankAccounts, setStaffBankAccounts] = useState<StaffBankAccount[]>([]);
  const [selectedStaffBankAccount, setSelectedStaffBankAccount] = useState<StaffBankAccount | null>(null);
  const [showStaffBankDropdown, setShowStaffBankDropdown] = useState(false);
  const [walletBalance, setWalletBalance] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loadingStaffAccounts, setLoadingStaffAccounts] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null);
  const [pinError, setPinError] = useState<string | null>(null);
  const [statusMessage, setStatusMessage] = useState<{
    type: 'success' | 'error' | 'info' | null;
    message: string | null;
  }>({ type: null, message: null });
  
  // Transfer status tracking
  const [transferStatus, setTransferStatus] = useState<{
    status: 'idle' | 'processing' | 'completed' | 'failed';
    transactionId?: number;
    details?: string;
  }>({ status: 'idle' });

  useEffect(() => {
    if (isOpen) {
      fetchWalletBalance();
      fetchStaffBankAccounts();
    }
  }, [isOpen, staffId]);

  const fetchWalletBalance = async () => {
    try {
      const wallet = await getWallet();
      setWalletBalance(wallet.balance);
    } catch (error) {
      console.error('Error fetching wallet balance:', error);
    }
  };


  
  const fetchStaffBankAccounts = async () => {
    if (!staffId) return;
    
    try {
      setLoadingStaffAccounts(true);
      setError(null);
      const accounts = await getStaffBankAccountsById(staffId);
      
      setStaffBankAccounts(accounts);
      
      // Select the primary account by default
      if (accounts.length > 0) {
        const primaryAccount = accounts.find(acc => acc.isPrimary) || accounts[0];
        setSelectedStaffBankAccount(primaryAccount);
      } else {
        setError("No bank accounts found for this staff member. Please add a bank account first.");
      }
    } catch (err) {
      console.error('Error fetching staff bank accounts:', err);
      setError("Failed to fetch staff bank accounts");
    } finally {
      setLoadingStaffAccounts(false);
    }
  };

  // Function to poll for transfer status updates using real Plaid API
  const pollTransferStatus = async (transactionId: number, transferId: string) => {
    try {
      // Import the real transfer status polling function
      const { pollPlaidTransferStatus } = await import('../../services/transactionStatus');
      
      // Set initial processing status
      setTransferStatus({
        status: 'processing',
        transactionId,
        details: 'Transfer initiated'
      });
      
      // Start polling for real transfer status updates
      const { stop } = pollPlaidTransferStatus(
        transferId,
        (statusResult) => {
          // Map Plaid status to our UI status
          let uiStatus: 'idle' | 'processing' | 'completed' | 'failed' = 'processing';
          let details = statusResult.message;
          
          // Map Plaid statuses to UI statuses
          switch (statusResult.status) {
            case 'pending':
            case 'processing':
              uiStatus = 'processing';
              details = 'Processing wallet transfer';
              break;
            case 'posted':
            case 'settled':
              uiStatus = 'completed';
              details = 'Transfer completed successfully';
              break;
            case 'failed':
            case 'cancelled':
            case 'returned':
              uiStatus = 'failed';
              details = statusResult.failureReason || 'Transfer failed';
              break;
            default:
              uiStatus = 'processing';
              details = statusResult.message;
          }
          
          // Update transfer status
          setTransferStatus({
            status: uiStatus,
            transactionId,
            details
          });
          
          // Handle completed transfers
          if (uiStatus === 'completed') {
            setStatusMessage({
              type: 'success',
              message: `Transfer of ${formatCurrency(parseFloat(amount))} completed successfully`
            });
            
            // Notify parent component of success
            onSuccess();
            
            // Close modal after a delay
            setTimeout(() => {
              onClose();
            }, 2000);
          }
          
          // Handle failed transfers
          if (uiStatus === 'failed') {
            setStatusMessage({
              type: 'error',
              message: details
            });
          }
        },
        5000, // Poll every 5 seconds
        24     // Poll for up to 2 minutes (24 * 5 seconds)
      );
      
      // Store the stop function for cleanup
      return stop;
    } catch (error) {
      console.error('Error polling transfer status:', error);
      setTransferStatus({
        status: 'failed',
        transactionId,
        details: 'Failed to get transfer status updates'
      });
      
      setStatusMessage({
        type: 'error',
        message: 'Failed to track transfer status'
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Reset status message and transfer status
    setStatusMessage({ type: null, message: null });
    setTransferStatus({ status: 'idle' });
    
    // Validate amount
    const transferAmount = parseFloat(amount);
    if (isNaN(transferAmount) || transferAmount <= 0) {
      setStatusMessage({
        type: 'error',
        message: 'Please enter a valid amount'
      });
      return;
    }
    
    if (transferAmount < 1 || transferAmount > 10000) {
      setStatusMessage({
        type: 'error',
        message: 'Amount must be between $1 and $10,000'
      });
      return;
    }

    // Validate staff bank account
    if (!selectedStaffBankAccount) {
      setStatusMessage({
        type: 'error',
        message: 'Please select a destination bank account for the staff member'
      });
      return;
    }
    
    // Check if the bank account is active
    if (selectedStaffBankAccount.status === 'inactive') {
      setStatusMessage({
        type: 'error',
        message: 'Cannot transfer to an inactive bank account. Please select an active account.'
      });
      return;
    }

    // Validate wallet balance (wallet-only transfers)
    if (transferAmount > walletBalance) {
      setStatusMessage({
        type: 'error',
        message: `Insufficient wallet balance. Available: ${formatCurrency(walletBalance)}`
      });
      return;
    }
    
    // Validate PIN for all transfers
    if (!pin) {
      setPinError('Security PIN is required');
      setStatusMessage({
        type: 'error',
        message: 'Please enter your security PIN'
      });
      return;
    }
    
    if (pin.length < 4) {
      setPinError('PIN must be at least 4 digits');
      setStatusMessage({
        type: 'error',
        message: 'PIN must be at least 4 digits long'
      });
      return;
    }
    
    // Validate PIN format (only numbers)
    if (!/^\d+$/.test(pin)) {
      setPinError('PIN must contain only numbers');
      setStatusMessage({
        type: 'error',
        message: 'PIN must contain only numbers'
      });
      return;
    }
    
    // Validate payment method
    if (!selectedPaymentMethod) {
      setStatusMessage({
        type: 'error',
        message: 'Please select a payment method'
      });
      return;
    }

    setIsSubmitting(true);
    
    // Update transfer status to processing
    setTransferStatus({
      status: 'processing',
      details: 'Initiating transfer...'
    });

    try {
      // Include the selected staff bank account ID and payment method in the transfer request
      const result = await transferToStaffBank(
        staffId,
        transferAmount,
        description || `Payment to ${staffName}'s ${selectedStaffBankAccount.bankName} account`,
        pin, // PIN is now required for all transfers
        'wallet', // Always use wallet as payment source
        undefined, // No bank account ID needed for wallet transfers
        selectedStaffBankAccount.id.toString(), // Pass the selected staff bank account ID
        selectedPaymentMethod?.id // Pass the selected payment method ID
      );
      
      if (result.success) {
        // Check if transfer message indicates completion
        const isCompleted = result.message?.toLowerCase().includes('completed') ||
                           result.message?.toLowerCase().includes('successful');

        if (isCompleted) {
          // Transfer is already completed - show success immediately
          setTransferStatus({
            status: 'completed',
            transactionId: result.transactionId || 0,
            details: 'Transfer completed successfully'
          });

          setStatusMessage({
            type: 'success',
            message: `Transfer of ${formatCurrency(parseFloat(amount))} completed successfully`
          });

          // Notify parent component of success
          onSuccess();

          // Close modal after a short delay
          setTimeout(() => {
            onClose();
          }, 2000);

        } else {
          // Transfer is pending - start with processing status
          setTransferStatus({
            status: 'processing',
            transactionId: result.transactionId || 0,
            details: 'Transfer initiated - waiting for confirmation'
          });

          // Show info message that transfer is pending
          setStatusMessage({
            type: 'info',
            message: 'Transfer initiated successfully. Status will update automatically.'
          });

          // For now, just show the processing status without polling
          // Real polling would require the transferId from the backend
          setStatusMessage({
            type: 'info',
            message: 'Transfer initiated. Please check transaction history for updates.'
          });
        }
      } else {
        // Update transfer status to failed
        setTransferStatus({
          status: 'failed',
          details: result.message || 'Transfer failed',
          transactionId: result.transactionId
        });
        
        setStatusMessage({
          type: 'error',
          message: result.message || 'Failed to transfer funds'
        });
      }
    } catch (error) {
      console.error('Transfer error:', error);
      
      // Extract error message from API response if available
      let errorMessage = 'Failed to transfer funds. Please try again.';
      let isPinError = false;
      
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { data?: { message?: string; code?: string } } };
        errorMessage = axiosError.response?.data?.message || errorMessage;
        
        // Check if this is a PIN verification error
        if (
          axiosError.response?.data?.code === 'INVALID_PIN' || 
          errorMessage.toLowerCase().includes('pin') || 
          errorMessage.toLowerCase().includes('security code')
        ) {
          isPinError = true;
          setPinError('Invalid PIN. Please try again.');
          setPin(''); // Clear the PIN input for security
        }
      } else if (error instanceof Error) {
        errorMessage = error.message;
        
        // Check if error message indicates PIN issue
        if (error.message.toLowerCase().includes('pin') || error.message.toLowerCase().includes('security code')) {
          isPinError = true;
          setPinError('Invalid PIN. Please try again.');
          setPin(''); // Clear the PIN input for security
        }
      }
      
      // Update transfer status to failed
      setTransferStatus({
        status: 'failed',
        details: errorMessage
      });
      
      setStatusMessage({
        type: 'error',
        message: errorMessage
      });
      
      // Focus on PIN input if it was a PIN error
      if (isPinError) {
        // Use setTimeout to ensure the DOM has updated
        setTimeout(() => {
          const pinInput = document.getElementById('security-pin-input');
          if (pinInput) {
            pinInput.focus();
          }
        }, 100);
      }
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Function to retry a failed transfer
  const handleRetryTransfer = async () => {
    if (!transferStatus.transactionId) {
      // If no transaction ID, just restart the form submission
      handleSubmit(new Event('submit') as React.FormEvent);
      return;
    }
    
    try {
      setIsSubmitting(true);
      setStatusMessage({ type: 'info', message: 'Retrying transfer...' });
      
      // Use the retryTransaction function from transactionStatus service
      const { retryTransaction } = await import('../../services/transactionStatus');
      const updatedStatus = await retryTransaction(transferStatus.transactionId);
      
      // Update the transfer status
      setTransferStatus({
        status: updatedStatus.status as 'idle' | 'processing' | 'completed' | 'failed',
        transactionId: parseInt(updatedStatus.transactionId),
        details: updatedStatus.details
      });
      
      // If the retry was successful, start polling for status updates
      if (updatedStatus.status === 'pending' || updatedStatus.status === 'processing') {
        // Note: For retry, we would need the transferId from the original transaction
        // For now, we'll just update the status without polling
        // pollTransferStatus(updatedStatus.transactionId, transferId);
      } else if (updatedStatus.status === 'completed') {
        setStatusMessage({
          type: 'success',
          message: 'Transfer completed successfully'
        });
        
        // Notify parent component of success
        onSuccess();
        
        // Close modal after a short delay to show success message
        setTimeout(() => {
          onClose();
        }, 2000);
      } else {
        setStatusMessage({
          type: 'error',
          message: updatedStatus.details || 'Failed to retry transfer'
        });
      }
    } catch (error) {
      console.error('Error retrying transfer:', error);
      
      let errorMessage = 'Failed to retry transfer. Please try again.';
      if (error instanceof Error) {
        errorMessage = error.message;
      }
      
      setStatusMessage({
        type: 'error',
        message: errorMessage
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Calculate fee based on payment method and amount
  const calculateFee = (paymentMethod: PaymentMethod, amount: number): number => {
    if (!paymentMethod) return 0;

    // If we have fee_type, use the new calculation logic
    if (paymentMethod.fee_type) {
      const minFee = parseFloat(paymentMethod.min_fee?.toString() || '0') || 0;
      const maxFee = parseFloat(paymentMethod.max_fee?.toString() || '0') || 0;
      const percentageFee = parseFloat(paymentMethod.percentage_fee?.toString() || '0') || 0;

      let calculatedFee = 0;

      switch (paymentMethod.fee_type) {
        case 'fixed':
          calculatedFee = minFee;
          break;
        case 'percentage':
          if (percentageFee > 0) {
            calculatedFee = (amount * percentageFee) / 100;
          }
          break;
        case 'hybrid':
          calculatedFee = minFee;
          if (percentageFee > 0) {
            calculatedFee += (amount * percentageFee) / 100;
          }
          break;
      }

      // Apply min/max constraints for percentage and hybrid types
      if (paymentMethod.fee_type !== 'fixed') {
        if (minFee > 0) calculatedFee = Math.max(calculatedFee, minFee);
        if (maxFee > 0) calculatedFee = Math.min(calculatedFee, maxFee);
      }

      return Math.round(calculatedFee * 100) / 100;
    }

    // Fallback to old fee calculation
    return parseFloat(paymentMethod.fee?.toString() || '0') || 0;
  };

  // Get current fee for display
  const getCurrentFee = (): number => {
    const numAmount = parseFloat(amount) || 0;
    if (!selectedPaymentMethod || numAmount <= 0) return 0;
    return calculateFee(selectedPaymentMethod, numAmount);
  };

  // Get total amount including fees
  const getTotalAmount = (): number => {
    const numAmount = parseFloat(amount) || 0;
    const fee = getCurrentFee();
    return numAmount + fee;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Building2 className="text-blue-600" size={20} />
            <h3 className="text-lg font-semibold text-gray-900">
              Transfer to {staffName}
            </h3>
          </div>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X size={20} />
          </button>
        </div>

        {/* Status message */}
        {statusMessage.type && (
          <div className={`mb-4 p-3 rounded-lg ${
            statusMessage.type === 'success' ? 'bg-green-100 text-green-800' : 
            statusMessage.type === 'info' ? 'bg-blue-100 text-blue-800' : 
            'bg-red-100 text-red-800'
          }`}>
            <div className="flex items-center">
              {statusMessage.type === 'success' ? (
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              ) : statusMessage.type === 'info' ? (
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              ) : (
                <AlertCircle size={20} className="mr-2" />
              )}
              <span>{statusMessage.message}</span>
            </div>
          </div>
        )}
        
        {/* Transfer Status Tracker */}
        {transferStatus.status !== 'idle' && (
          <div className="mb-4 border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium text-gray-700">Transfer Status</h4>
              {transferStatus.transactionId && (
                <span className="text-xs text-gray-500">ID: {transferStatus.transactionId}</span>
              )}
            </div>
            
            <div className="relative">
              {/* Status Bar */}
              <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-gray-200">
                <div 
                  className={`shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center ${
                    transferStatus.status === 'completed' ? 'bg-green-500' : 
                    transferStatus.status === 'failed' ? 'bg-red-500' : 
                    'bg-blue-500'
                  }`} 
                  style={{ 
                    width: transferStatus.status === 'completed' ? '100%' : 
                           transferStatus.status === 'failed' ? '100%' : 
                           '60%',
                    transition: 'width 1s ease-in-out'
                  }}
                ></div>
              </div>
              
              {/* Status Details */}
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`rounded-full w-8 h-8 flex items-center justify-center mr-3 ${
                    transferStatus.status === 'completed' ? 'bg-green-100 text-green-600' : 
                    transferStatus.status === 'failed' ? 'bg-red-100 text-red-600' : 
                    'bg-blue-100 text-blue-600'
                  }`}>
                    {transferStatus.status === 'completed' ? (
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    ) : transferStatus.status === 'failed' ? (
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    ) : (
                      <svg className="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    )}
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {transferStatus.status === 'completed' ? 'Transfer Completed' : 
                       transferStatus.status === 'failed' ? 'Transfer Failed' : 
                       'Processing Transfer'}
                    </div>
                    <div className="text-xs text-gray-500">{transferStatus.details}</div>
                  </div>
                </div>
                
                {/* Retry button for failed transfers */}
                {transferStatus.status === 'failed' && (
                  <button
                    type="button"
                    onClick={handleRetryTransfer}
                    disabled={isSubmitting}
                    className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors flex items-center space-x-1"
                  >
                    {isSubmitting ? (
                      <>
                        <svg className="w-4 h-4 animate-spin mr-1" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Retrying...</span>
                      </>
                    ) : (
                      <>
                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                        </svg>
                        <span>Retry</span>
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>
        )}

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
          <p className="text-sm text-blue-800">
            💡 Transfer funds to {staffName}'s registered bank account from your wallet.
          </p>
        </div>
        
        {/* Wallet Balance Display */}
        <div className="mb-4">
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Wallet size={16} className="text-blue-600" />
                <span className="text-sm font-medium text-gray-700">Wallet Balance</span>
              </div>
              <div className={`text-sm font-semibold ${walletBalance <= 0 ? 'text-red-600' : 'text-green-600'}`}>
                {formatCurrency(walletBalance)}
              </div>
            </div>
          </div>
        </div>
        
        {/* Staff Bank Account Selection */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Destination Bank Account
          </label>
          {loadingStaffAccounts ? (
            <div className="bg-gray-50 rounded-lg p-3 text-center">
              <p className="text-sm text-gray-500">Loading staff bank accounts...</p>
            </div>
          ) : error ? (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3 text-center">
              <div className="flex items-center justify-center text-red-600 mb-1">
                <AlertCircle size={16} className="mr-1" />
                <p className="text-sm font-medium">Error</p>
              </div>
              <p className="text-sm text-red-600">{error}</p>
            </div>
          ) : staffBankAccounts.length === 0 ? (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 text-center">
              <p className="text-sm text-yellow-700">No bank accounts found for this staff member. Please add a bank account first.</p>
            </div>
          ) : (
            <div className="relative">
              <button
                type="button"
                onClick={() => setShowStaffBankDropdown(!showStaffBankDropdown)}
                className="w-full bg-white border border-gray-300 rounded-lg p-3 flex items-center justify-between hover:border-blue-500 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-colors"
                disabled={staffBankAccounts.length === 0}
              >
                {selectedStaffBankAccount ? (
                  <div className="flex items-center space-x-3">
                    <Building2 size={16} className="text-gray-400" />
                    <div className="text-left">
                      <div className="font-medium text-sm flex items-center">
                        {selectedStaffBankAccount.bankName}
                        {selectedStaffBankAccount.isPrimary && (
                          <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-blue-100 text-blue-800">
                            <Star size={10} className="mr-1" /> Primary
                          </span>
                        )}
                      </div>
                      <div className="text-xs text-gray-500">
                        {selectedStaffBankAccount.accountType} • {selectedStaffBankAccount.accountNumber}
                      </div>
                    </div>
                  </div>
                ) : (
                  <span className="text-gray-500">Select staff bank account</span>
                )}
                <ChevronDown size={16} className={`text-gray-400 transition-transform ${showStaffBankDropdown ? 'rotate-180' : ''}`} />
              </button>

              {showStaffBankDropdown && staffBankAccounts.length > 0 && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-40 overflow-y-auto">
                  {staffBankAccounts.map((account) => (
                    <button
                      key={account.id}
                      type="button"
                      onClick={() => {
                        setSelectedStaffBankAccount(account);
                        setShowStaffBankDropdown(false);
                      }}
                      className="w-full p-3 text-left hover:bg-gray-50 flex items-center space-x-3"
                    >
                      <Building2 size={16} className="text-gray-400" />
                      <div>
                        <div className="font-medium text-sm flex items-center">
                          {account.bankName}
                          {account.isPrimary && (
                            <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-blue-100 text-blue-800">
                              <Star size={10} className="mr-1" /> Primary
                            </span>
                          )}
                        </div>
                        <div className="text-xs text-gray-500">
                          {account.accountType} • {account.accountNumber} • {account.accountHolderName}
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
        
        {/* Payment Method Selection */}
        {selectedStaffBankAccount && (
          <div className="mb-4">
            <PaymentMethodSelector
              selectedBankAccount={selectedStaffBankAccount}
              selectedPaymentMethod={selectedPaymentMethod}
              onPaymentMethodChange={setSelectedPaymentMethod}
              disabled={isSubmitting || transferStatus.status !== 'idle'}
            />
            
            {/* Payment Method Impact Information */}
            {selectedPaymentMethod && (
              <div className="mt-2 bg-blue-50 border border-blue-100 rounded-lg p-3">
                <div className="flex items-start">
                  <div className="flex-shrink-0 mt-0.5">
                    <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-2">
                    <h4 className="text-sm font-medium text-blue-800">Payment Method Details</h4>
                    <ul className="mt-1 text-xs text-blue-700 space-y-1">
                      <li className="flex items-center">
                        <span className="font-medium mr-1">Processing Time:</span> 
                        {selectedPaymentMethod.processingTime}
                      </li>
                      <li className="flex items-center">
                        <span className="font-medium mr-1">Fee:</span>
                        {selectedPaymentMethod.feeDescription || 'Free'}
                      </li>
                      <li className="mt-1">{selectedPaymentMethod.description}</li>
                    </ul>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Amount Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Amount
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span className="text-gray-500 sm:text-sm">$</span>
              </div>
              <input
                type="text"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                className="w-full pl-7 pr-12 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="0.00"
                aria-describedby="price-currency"
              />
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <span className="text-gray-500 sm:text-sm" id="price-currency">
                  USD
                </span>
              </div>
            </div>
          </div>

          {/* Total Charges Display */}
          {amount && parseFloat(amount) > 0 && selectedPaymentMethod && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-green-900 mb-3">Payment Summary</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Transfer Amount:</span>
                  <span className="font-medium">${parseFloat(amount).toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Processing Fee:</span>
                  <span className="font-medium">
                    {getCurrentFee() > 0 ? `$${getCurrentFee().toFixed(2)}` : 'Free'}
                  </span>
                </div>
                {selectedPaymentMethod && selectedPaymentMethod.feeDescription && (
                  <div className="text-xs text-gray-500">
                    Fee Structure: {selectedPaymentMethod.feeDescription}
                  </div>
                )}
                <div className="border-t border-green-200 pt-2 flex justify-between font-semibold">
                  <span className="text-green-900">Total Amount:</span>
                  <span className="text-green-900">${getTotalAmount().toFixed(2)}</span>
                </div>
                <div className="text-xs text-gray-500">
                  Processing Time: {selectedPaymentMethod.processingTime}
                </div>
              </div>
            </div>
          )}

          {/* Description Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description (Optional)
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter a description for this transfer"
              rows={2}
            ></textarea>
          </div>

          {/* Security PIN (required for all transfers) */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Security PIN
            </label>
            <div className="relative">
              <input
                id="security-pin-input"
                type="password"
                value={pin}
                onChange={(e) => {
                  setPin(e.target.value);
                  setPinError(null); // Clear error when user types
                }}
                className={`w-full px-3 py-2 pl-9 border ${
                  pinError ? 'border-red-500' : 'border-gray-300'
                } rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                placeholder="Enter your security PIN"
                maxLength={6}
              />
              <Lock size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            </div>
            {pinError ? (
              <p className="mt-1 text-xs text-red-600 flex items-center">
                <AlertCircle size={12} className="mr-1" />
                {pinError}
              </p>
            ) : (
              <p className="mt-1 text-xs text-gray-500">
                Enter your security PIN to authorize this transfer
              </p>
            )}
          </div>

          <div className="pt-4">
            <button
              type="submit"
              disabled={isSubmitting || transferStatus.status !== 'idle'}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors flex items-center justify-center space-x-2"
            >
              {isSubmitting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>Processing...</span>
                </>
              ) : (
                <>
                  <Send size={16} />
                  <span>Transfer Funds</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default StaffBankTransferModal;