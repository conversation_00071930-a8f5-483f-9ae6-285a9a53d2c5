import React from 'react';
import images, { BankLogo } from '../../components/importImages/images';

interface BankCardProps {
  accountNumber: string;
  bankName: string;
  cardType: string;
}

const BankCard: React.FC<BankCardProps> = ({ accountNumber, bankName, cardType }) => {
  return (
    <div className="relative w-full max-w-sm sm:max-w-md md:max-w-[382px] mx-auto">
      {/* Bottom layered shadows */}
      <div className="absolute bottom-[-8px] left-[5%] w-[90%] h-[30px] bg-gray-300 rounded-full opacity-50 z-0"></div>
      <div className="absolute bottom-[-16px] left-[10%] w-[80%] h-[30px] bg-gray-200 rounded-full opacity-50 z-0"></div>

      {/* Main Card */}
      <div className="relative z-10 w-full aspect-[382/245] bg-[#2F94ED] rounded-[30px] p-5 text-white overflow-hidden shadow-lg">
        {/* Background decorative circles */}
        <div className="absolute bg-blue-300 opacity-20 rounded-full w-[260px] h-[260px] left-[-42px] top-[-55px]"></div>
        <div className="absolute bg-blue-700 opacity-20 rounded-full w-[243px] h-[243px] left-[31px] top-[78px]"></div>
        <div className="absolute bg-blue-700 opacity-20 rounded-full w-[202px] h-[202px] left-[203px] top-[95px]"></div>

        {/* Card Type Badge */}
        <div className="absolute top-1 left-5 sm:left-8">
          <span className="bg-orange-500 text-white px-3 py-1.5 rounded-md text-xs font-semibold shadow">
            {cardType}
          </span>
        </div>

        {/* Bank Logo */}
        <div className="absolute top-4 right-4 sm:top-5 sm:right-5">
          <img
            src={BankLogo}
            alt="Bank Logo"
            className="w-12 h-12 sm:w-[61.99px] sm:h-[61.99px] object-contain rounded-full"
          />
        </div>

        {/* Account Info */}
        <div className="relative z-10 mt-16 sm:mt-12">
          <p className="text-sm sm:text-md text-white/70 mb-1">A/C No</p>
          <p className="text-base sm:text-lg font-mono tracking-wider">{accountNumber}</p>
        </div>

        {/* Bank Name */}
        <div className="absolute bottom-4 right-4 text-sm sm:text-md text-white/90 font-medium">
          {bankName}
        </div>
      </div>
    </div>
  );
};

export default BankCard;
