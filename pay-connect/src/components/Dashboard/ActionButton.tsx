import React from 'react';

interface ActionButtonProps {
  label: string;
  imageSrc: string; // Required image source
  onClick: () => void;
  variant?: 'primary' | 'secondary';
}

const ActionButton: React.FC<ActionButtonProps> = ({
  label,
  imageSrc,
  onClick,
  variant = 'primary'
}) => {
  const baseClasses =
    "flex items-center justify-center space-x-3 px-6 mt-4 py-4 rounded-lg font-medium transition-colors w-full";

  const variantClasses =
    variant === "primary"
      ? "text-white"
      : "bg-white text-gray-700 hover:bg-gray-50 border border-gray-300";

  const gradientStyle =
    variant === "primary"
      ? { background: "linear-gradient(90deg, #1F8157 0%, #27AE60 100%)" }
      : {};

  return (
    <button
      onClick={onClick}
      className={`${baseClasses} ${variantClasses}`}
      style={gradientStyle}
    >
      <img src={imageSrc} alt="icon" className="w-8 h-8" />
      <span className="text-sm font-medium">{label}</span>
    </button>
  );
};

export default ActionButton;
