import React from 'react';
import { type LucideIcon } from 'lucide-react';

interface SummaryCardProps {
  title: string;
  value: string;
  imageSrc: string; // ✅ Required image path
  // bgColor: string;
  iconColor: string; // still used for image background color
}

const SummaryCard: React.FC<SummaryCardProps> = ({
  title,
  value,
  imageSrc,
  // bgColor,
  iconColor,
}) => {
  return (
    <div className="bg-[#EFEDEC]  rounded-[30px] py-[25px] px-[15px] border-0 shadow-sm flex flex-col items-center justify-center h-45">
      {/* <div className={`w-12 h-12 ${bgColor} ${iconColor} rounded-lg flex items-center justify-center mb-4`}> */}
          <div className={`w-100 h-100  ${iconColor} rounded-lg flex items-center justify-center mb-4`}>
        <img src={imageSrc} alt="wallet" className="w-100 h-100 object-contain" />
      </div>
      <div className="text-center">
        <p className="text-sm text-gray-500 mb-2">{title}</p>
        <p className="text-2xl font-bold text-gray-900">{value}</p>
      </div>
    </div>
  );
};

export default SummaryCard;
