import React from 'react';
import { ChevronRight } from 'lucide-react';

interface DashboardListItemProps {
  title: string;
  subtitle: string;
  amount: string;
  date: string;
  showPayButton?: boolean; // New prop for showing the Pay button
}

const DashboardListItem: React.FC<DashboardListItemProps> = ({
  title,
  subtitle,
  amount,
  date,
  showPayButton = false,
}) => {
  return (
    <div className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors cursor-pointer">
      <div className="flex-1">
        <h4 className="font-normal text-gray-500 text-xs">{subtitle}</h4>
        <p className="text-base text-blue-600 font-bold">{title}</p>
        <p className="text-xs text-gray-500">{date}</p>
      </div>

      <div className="text-right flex items-center space-x-2">
        <p className="font-bold text-blue-600 text-base">
          {amount}
        </p>
        {showPayButton ? (
          <button className="bg-green-500 text-white px-3 py-1 rounded-md text-xs font-medium hover:bg-green-600 transition-colors">
            Pay
          </button>
        ) : (
          <ChevronRight size={16} className="text-gray-400" />
        )}
      </div>
    </div>
  );
};

export default DashboardListItem;
