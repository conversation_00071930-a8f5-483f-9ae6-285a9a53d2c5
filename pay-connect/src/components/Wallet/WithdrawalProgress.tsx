import React, { useState, useEffect } from 'react';
import { X, CheckCircle, Clock, AlertCircle, RefreshCw, DollarSign, Building2, Calendar, Info } from 'lucide-react';
import { getWithdrawalStatus, WithdrawalStatusData } from '../../services/withdrawalStatus';

interface WithdrawalProgressProps {
  isOpen: boolean;
  onClose: () => void;
  transactionId: number;
}

const WithdrawalProgress: React.FC<WithdrawalProgressProps> = ({ isOpen, onClose, transactionId }) => {
  const [data, setData] = useState<WithdrawalStatusData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  const fetchWithdrawalStatus = async () => {
    try {
      const result = await getWithdrawalStatus(transactionId);
      setData(result);
      setError(null);

      // Stop auto-refresh if withdrawal is completed or failed
      if (result.status.overall === 'completed' || result.status.overall === 'failed' || result.status.overall === 'reversed') {
        setAutoRefresh(false);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch withdrawal status');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen && transactionId) {
      fetchWithdrawalStatus();
    }
  }, [isOpen, transactionId]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isOpen && autoRefresh && data?.status.overall === 'pending') {
      interval = setInterval(fetchWithdrawalStatus, 5000); // Refresh every 5 seconds
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isOpen, autoRefresh, data?.status.overall]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'failed':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      case 'pending':
        return <Clock className="w-5 h-5 text-blue-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'failed':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'reversed':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      default:
        return 'text-blue-600 bg-blue-50 border-blue-200';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <DollarSign className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">Withdrawal Status</h2>
          </div>
          <div className="flex items-center space-x-2">
            {autoRefresh && data?.status.overall === 'pending' && (
              <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />
            )}
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>
        </div>

        <div className="overflow-y-auto max-h-[calc(90vh-80px)]">
          {loading ? (
            <div className="flex items-center justify-center p-12">
              <RefreshCw className="w-8 h-8 text-blue-500 animate-spin" />
              <span className="ml-3 text-gray-600">Loading withdrawal status...</span>
            </div>
          ) : error ? (
            <div className="p-6 text-center">
              <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
              <p className="text-red-600 mb-4">{error}</p>
              <button
                onClick={fetchWithdrawalStatus}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Try Again
              </button>
            </div>
          ) : data ? (
            <div className="p-6 space-y-6">
              {/* Transaction Summary */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">Amount</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {formatCurrency(data.transaction.amount)}
                    </p>
                    {data.transaction.processingFee > 0 && (
                      <p className="text-xs text-gray-500">
                        + {formatCurrency(data.transaction.processingFee)} fee
                      </p>
                    )}
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">To</p>
                    <div className="flex items-center space-x-2">
                      <Building2 className="w-4 h-4 text-gray-500" />
                      <p className="text-sm font-medium text-gray-900">{data.transaction.bankName}</p>
                    </div>
                    <p className="text-xs text-gray-500">{data.transaction.paymentMethod}</p>
                  </div>
                </div>
                
                <div className="mt-4 flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-600">Expected: {data.transaction.estimatedArrival}</span>
                  </div>
                  <div className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(data.status.overall)}`}>
                    {data.status.overall.charAt(0).toUpperCase() + data.status.overall.slice(1)}
                  </div>
                </div>
              </div>

              {/* Progress Bar */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Progress</span>
                  <span className="text-sm text-gray-500">{data.progress.percentage}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                    style={{ width: `${data.progress.percentage}%` }}
                  ></div>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {data.progress.completed} of {data.progress.total} steps completed
                </p>
              </div>

              {/* Current Status */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 mt-0.5">
                    {getStatusIcon(data.status.overall === 'pending' ? 'pending' : 'completed')}
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">{data.status.currentStepTitle}</h3>
                    <p className="text-sm text-gray-600 mt-1">{data.status.currentStepDescription}</p>
                    <p className="text-xs text-gray-500 mt-2">
                      Last updated: {formatDateTime(data.status.lastUpdated)}
                    </p>
                  </div>
                </div>
              </div>

              {/* Timeline */}
              {data.timeline.length > 0 && (
                <div>
                  <h3 className="font-medium text-gray-900 mb-4 flex items-center">
                    <Info className="w-4 h-4 mr-2" />
                    Activity Timeline
                  </h3>
                  <div className="space-y-3">
                    {data.timeline.map((event, index) => (
                      <div key={index} className="flex items-start space-x-3">
                        <div className="flex-shrink-0 mt-0.5">
                          {getStatusIcon(event.status)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900">{event.title}</p>
                          <p className="text-sm text-gray-600">{event.description}</p>
                          <p className="text-xs text-gray-500 mt-1">
                            {formatDateTime(event.timestamp)}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );
};

export default WithdrawalProgress;
