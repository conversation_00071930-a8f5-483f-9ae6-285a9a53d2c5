import React, { useState, useEffect } from 'react';
import { X, DollarSign, Lock, Eye, EyeOff, ArrowRight, CheckCircle, AlertCircle } from 'lucide-react';
import { transferToWallet, TransferRecipient } from '../../services/wallet';
import UserSelector from './UserSelector';
import PaymentLoader from '../common/PaymentLoader';

interface WalletTransferModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentBalance: number;
  onTransferSuccess: (newBalance: number) => void;
  preSelectedRecipient?: {
    id: number;
    name: string;
    email: string;
  };
}

type TransferStep = 'details' | 'confirmation' | 'processing' | 'success' | 'error';

const WalletTransferModal: React.FC<WalletTransferModalProps> = ({
  isOpen,
  onClose,
  currentBalance,
  onTransferSuccess,
  preSelectedRecipient
}) => {
  const [step, setStep] = useState<TransferStep>('details');
  const [selectedUser, setSelectedUser] = useState<TransferRecipient | null>(null);
  const [amount, setAmount] = useState('');
  const [pin, setPin] = useState('');
  const [showPin, setShowPin] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [transactionId, setTransactionId] = useState<number | null>(null);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setStep('details');
      // If there's a pre-selected recipient, use it; otherwise start with null
      if (preSelectedRecipient) {
        setSelectedUser({
          id: preSelectedRecipient.id,
          name: preSelectedRecipient.name,
          email: preSelectedRecipient.email,
          hasWallet: true // Assume staff members have wallets
        });
      } else {
        setSelectedUser(null);
      }
      setAmount('');
      setPin('');
      setShowPin(false);
      setError(null);
      setLoading(false);
      setTransactionId(null);
    }
  }, [isOpen, preSelectedRecipient]);

  const handleClose = () => {
    if (step === 'processing') return; // Prevent closing during processing
    onClose();
  };

  const validateForm = (): boolean => {
    setError(null);

    if (!selectedUser) {
      setError('Please select a recipient');
      return false;
    }

    if (!selectedUser.hasWallet) {
      setError('Selected user does not have a wallet');
      return false;
    }

    const amountNum = parseFloat(amount);
    if (!amount || isNaN(amountNum) || amountNum <= 0) {
      setError('Please enter a valid amount');
      return false;
    }

    if (amountNum < 1) {
      setError('Minimum transfer amount is $1.00');
      return false;
    }

    if (amountNum > 10000) {
      setError('Maximum transfer amount is $10,000.00');
      return false;
    }

    if (amountNum > currentBalance) {
      setError(`Insufficient balance. Available: $${currentBalance.toFixed(2)}`);
      return false;
    }

    if (!pin || pin.length < 4 || pin.length > 6) {
      setError('Please enter your 4-6 digit PIN');
      return false;
    }

    return true;
  };

  const handleContinue = () => {
    if (validateForm()) {
      setStep('confirmation');
    }
  };

  const handleConfirmTransfer = async () => {
    if (!selectedUser || !amount || !pin) return;

    setStep('processing');
    setLoading(true);
    setError(null);

    try {
      const result = await transferToWallet(
        selectedUser.id,
        parseFloat(amount),
        pin
      );

      if (result.success) {
        setTransactionId(result.transactionId || null);
        setStep('success');
        onTransferSuccess(result.newBalance || currentBalance);
      } else {
        setError(result.message || 'Transfer failed');
        setStep('error');
      }
    } catch (err) {
      setError('Transfer failed. Please try again.');
      setStep('error');
    } finally {
      setLoading(false);
    }
  };

  const handleBackToDetails = () => {
    setStep('details');
    setError(null);
  };

  const handleTryAgain = () => {
    setStep('details');
    setError(null);
    setPin(''); // Clear PIN for security
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-5 border-b border-gray-200">
          <div><h2 className="text-xl font-semibold text-gray-900">
            {step === 'details' && 'Transfer Money'}
            {step === 'confirmation' && 'Confirm Transfer'}
            {step === 'processing' && 'Processing Transfer'}
            {step === 'success' && 'Transfer Successful'}
            {step === 'error' && 'Transfer Failed'}
          </h2>
            <p
              className={`text-md ${currentBalance === 0
                  ? 'text-red-500'
                  : currentBalance > 0
                    ? 'text-green-600'
                    : 'text-gray-500'
                }`}
            >
              Available balance: ${currentBalance.toFixed(2)}
            </p>

          </div>
          <button
            onClick={handleClose}
            disabled={step === 'processing'}
            className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
          >
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {step === 'details' && (
            <div className="space-y-6">
              {/* User Selection */}
              <UserSelector
                onUserSelect={setSelectedUser}
                selectedUser={selectedUser}
              />

              {/* Amount Input */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Transfer Amount
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <DollarSign className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="number"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    placeholder="0.00"
                    min="1"
                    max="10000"
                    step="0.01"
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

              </div>

              {/* PIN Input */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Wallet PIN
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type={showPin ? 'text' : 'password'}
                    value={pin}
                    onChange={(e) => setPin(e.target.value)}
                    placeholder="Enter your wallet PIN"
                    maxLength={6}
                    className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPin(!showPin)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPin ? <EyeOff size={20} /> : <Eye size={20} />}
                  </button>
                </div>
                <div className="flex items-center justify-between mt-1">
                  <p className="text-xs text-gray-500">
                    Enter your 4-6 digit wallet PIN to authorize the transfer
                  </p>
                  <button
                    type="button"
                    onClick={() => window.open('/wallet?tab=settings', '_blank')}
                    className="text-xs text-blue-600 hover:text-blue-800 underline"
                  >
                    Forgot PIN?
                  </button>
                </div>
              </div>

              {/* Error Message */}
              {error && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-sm text-red-600">{error}</p>
                </div>
              )}

              {/* Continue Button */}
              <button
                onClick={handleContinue}
                disabled={!selectedUser || !amount || !pin}
                className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              >
                <span>Continue</span>
                <ArrowRight size={20} />
              </button>
            </div>
          )}

          {step === 'confirmation' && selectedUser && (
            <div className="space-y-6">
              {/* Transfer Summary */}
              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                <h3 className="font-medium text-gray-900">Transfer Summary</h3>

                <div className="flex justify-between">
                  <span className="text-gray-600">To:</span>
                  <span className="font-medium">{selectedUser.name}</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-600">Amount:</span>
                  <span className="font-medium text-lg">${parseFloat(amount).toFixed(2)}</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-600">New Balance:</span>
                  <span className="font-medium">${(currentBalance - parseFloat(amount)).toFixed(2)}</span>
                </div>
              </div>

              {/* Confirmation Buttons */}
              <div className="flex space-x-3">
                <button
                  onClick={handleBackToDetails}
                  className="flex-1 bg-gray-200 text-gray-800 py-3 px-4 rounded-lg font-medium hover:bg-gray-300"
                >
                  Back
                </button>
                <button
                  onClick={handleConfirmTransfer}
                  className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700"
                >
                  Confirm Transfer
                </button>
              </div>
            </div>
          )}

          {step === 'processing' && (
            <div className="text-center py-8">
              <PaymentLoader
                type="processing"
                message="Processing your transfer..."
                size="large"
                showQuotes={true}
              />
            </div>
          )}

          {step === 'success' && selectedUser && (
            <div className="text-center space-y-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>

              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Transfer Successful!
                </h3>
                <p className="text-gray-600">
                  ${parseFloat(amount).toFixed(2)} has been transferred to {selectedUser.name}
                </p>
                {transactionId && (
                  <p className="text-sm text-gray-500 mt-2">
                    Transaction ID: {transactionId}
                  </p>
                )}
              </div>

              <button
                onClick={handleClose}
                className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700"
              >
                Done
              </button>
            </div>
          )}

          {step === 'error' && (
            <div className="text-center space-y-6">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto">
                <AlertCircle className="w-8 h-8 text-red-600" />
              </div>

              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Transfer Failed
                </h3>
                <p className="text-gray-600">
                  {error || 'Something went wrong. Please try again.'}
                </p>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={handleClose}
                  className="flex-1 bg-gray-200 text-gray-800 py-3 px-4 rounded-lg font-medium hover:bg-gray-300"
                >
                  Cancel
                </button>
                <button
                  onClick={handleTryAgain}
                  className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700"
                >
                  Try Again
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default WalletTransferModal;
