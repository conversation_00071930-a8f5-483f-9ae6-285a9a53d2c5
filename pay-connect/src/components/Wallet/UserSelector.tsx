import React, { useState, useEffect, useCallback } from 'react';
import { Search, User, Check, AlertCircle } from 'lucide-react';
import { searchTransferRecipients, TransferRecipient } from '../../services/wallet';
import PaymentLoader from '../common/PaymentLoader';

interface UserSelectorProps {
  onUserSelect: (user: TransferRecipient) => void;
  selectedUser?: TransferRecipient | null;
  className?: string;
}

const UserSelector: React.FC<UserSelectorProps> = ({
  onUserSelect,
  selectedUser,
  className = ''
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [users, setUsers] = useState<TransferRecipient[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDropdown, setShowDropdown] = useState(false);
  const [isSearchMode, setIsSearchMode] = useState(false);

  const searchUsers = useCallback(async (term: string) => {
    if (term.length < 2) {
      setUsers([]);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const results = await searchTransferRecipients(term);
      setUsers(results);
    } catch (err) {
      setError('Failed to search users');
      setUsers([]);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      searchUsers(searchTerm);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, searchUsers]);

  // Initialize search mode based on whether there's a pre-selected user
  useEffect(() => {
    setIsSearchMode(!selectedUser);
  }, [selectedUser]);

  const handleUserSelect = (user: TransferRecipient) => {
    if (!user.hasWallet) {
      setError('This user does not have a wallet set up');
      return;
    }

    onUserSelect(user);
    setSearchTerm(user.name);
    setShowDropdown(false);
    setIsSearchMode(false);
  };

  const handleChangeRecipient = () => {
    setIsSearchMode(true);
    setSearchTerm('');
    setShowDropdown(true);
    onUserSelect(null as any); // Clear selection
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    setShowDropdown(true);
    
    if (value !== selectedUser?.name) {
      // Clear selection if user is typing something different
      onUserSelect(null as any);
    }
  };

  const handleInputFocus = () => {
    setShowDropdown(true);
  };

  const handleInputBlur = () => {
    // Delay hiding dropdown to allow for clicks
    setTimeout(() => setShowDropdown(false), 200);
  };

  return (
    <div className={`relative ${className}`}>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Select Recipient
      </label>

      {/* Show selected user in a card format when not in search mode */}
      {selectedUser && !isSearchMode ? (
        <div className="border border-gray-300 rounded-lg p-4 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <User className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="font-medium text-gray-900">{selectedUser.name}</p>
                <p className="text-sm text-gray-500">{selectedUser.email}</p>
                <div className="flex items-center mt-1">
                  <Check className="h-4 w-4 text-green-500 mr-1" />
                  <span className="text-xs text-green-600">Wallet Active</span>
                </div>
              </div>
            </div>
            <button
              onClick={handleChangeRecipient}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              Change
            </button>
          </div>
        </div>
      ) : (
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>

          <input
            type="text"
            value={searchTerm}
            onChange={handleInputChange}
            onFocus={handleInputFocus}
            onBlur={handleInputBlur}
            placeholder="Search by name or email..."
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />

          {selectedUser && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              <Check className="h-5 w-5 text-green-500" />
            </div>
          )}
        </div>
      )}

      {/* Dropdown */}
      {showDropdown && (searchTerm.length >= 2 || users.length > 0) && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
          {loading && (
            <div className="p-4 text-center">
              <PaymentLoader
                type="setup"
                message="Searching users..."
                size="small"
                showQuotes={false}
              />
            </div>
          )}
          
          {!loading && users.length === 0 && searchTerm.length >= 2 && (
            <div className="p-4 text-center text-gray-500">
              <User className="h-8 w-8 mx-auto mb-2 text-gray-300" />
              <p className="text-sm">No users found</p>
              <p className="text-xs text-gray-400">Try searching with a different name or email</p>
            </div>
          )}
          
          {!loading && users.length > 0 && (
            <div className="py-1">
              {users.map((user) => (
                <div
                  key={user.id}
                  onClick={() => handleUserSelect(user)}
                  className={`px-4 py-3 hover:bg-gray-50 cursor-pointer flex items-center space-x-3 ${
                    !user.hasWallet ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                >
                  <div className="flex-shrink-0">
                    {user.avatar ? (
                      <img
                        src={user.avatar}
                        alt={user.name}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center">
                        <User className="w-5 h-5 text-gray-500" />
                      </div>
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {user.name}
                    </p>
                    <p className="text-sm text-gray-500 truncate">
                      {user.email}
                    </p>
                  </div>
                  
                  <div className="flex-shrink-0">
                    {user.hasWallet ? (
                      <div className="flex items-center text-green-600">
                        <Check className="w-4 h-4" />
                        <span className="text-xs ml-1">Wallet</span>
                      </div>
                    ) : (
                      <div className="flex items-center text-red-500">
                        <AlertCircle className="w-4 h-4" />
                        <span className="text-xs ml-1">No Wallet</span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      {/* Selected User Display */}
      {selectedUser && (
        <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              {selectedUser.avatar ? (
                <img
                  src={selectedUser.avatar}
                  alt={selectedUser.name}
                  className="w-8 h-8 rounded-full object-cover"
                />
              ) : (
                <div className="w-8 h-8 rounded-full bg-blue-200 flex items-center justify-center">
                  <User className="w-4 h-4 text-blue-600" />
                </div>
              )}
            </div>
            
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-blue-900">
                {selectedUser.name}
              </p>
              <p className="text-xs text-blue-600">
                {selectedUser.email}
              </p>
            </div>
            
            <div className="flex-shrink-0">
              <Check className="w-5 h-5 text-blue-600" />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserSelector;
