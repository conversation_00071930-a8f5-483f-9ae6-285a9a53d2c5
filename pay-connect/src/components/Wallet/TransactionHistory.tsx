import React, { useState, useEffect } from 'react';
import { X, <PERSON>UpR<PERSON>, ArrowDownLeft, CreditCard, Filter, Download, Search, ChevronRight, Clock, CheckCircle, AlertCircle, Building2 } from 'lucide-react';
import { getWalletTransactions } from '../../services/wallet';
import { getWithdrawalProgress } from '../../services/paymentMethods';
import PaymentLoader from '../common/PaymentLoader';
import { Link } from 'react-router-dom';
interface Transaction {
  id: string;
  type: 'credit' | 'debit';
  category: 'add_money' | 'withdraw' | 'payment_sent' | 'payment_received' | 'refund';
  amount: number;
  description: string;
  date: string;
  status: 'completed' | 'pending' | 'failed';
  reference?: string;
}

interface TransactionHistoryProps {
  isOpen: boolean;
  onClose: () => void;
}

const TransactionHistory: React.FC<TransactionHistoryProps> = ({ isOpen, onClose }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'credit' | 'debit'>('all');
  const [filterStatus, setFilterStatus] = useState<'all' | 'completed' | 'pending' | 'failed'>('all');
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedTransaction, setExpandedTransaction] = useState<string | null>(null);
  const [withdrawalSteps, setWithdrawalSteps] = useState<{ [transactionId: string]: any[] }>({});

  // Fetch real transaction data
  useEffect(() => {
    const fetchTransactions = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const data = await getWalletTransactions(100, 0); // Get last 100 transactions

        // Transform API data to component format
        const transformedTransactions: Transaction[] = data.map((tx: any) => ({
          id: tx.id.toString(),
          type: tx.type as 'credit' | 'debit',
          category: tx.type === 'credit' ? 'add_money' : 'withdraw',
          amount: tx.amount,
          description: tx.description,
          date: tx.date,
          status: tx.status as 'completed' | 'pending' | 'failed',
          reference: tx.reference
        }));

        setTransactions(transformedTransactions);
      } catch (err) {
        console.error('Error fetching transactions:', err);
        setError('Failed to load transaction history');
        setTransactions([]); // Use empty array instead of mock data
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen) {
      fetchTransactions();
    }
  }, [isOpen]);

  // Filter real transactions
  const filteredTransactions = transactions.filter(transaction => {
    const matchesSearch = transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.reference?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'all' || transaction.type === filterType;
    const matchesStatus = filterStatus === 'all' || transaction.status === filterStatus;

    return matchesSearch && matchesType && matchesStatus;
  });

  const getTransactionIcon = (category: string, type: string) => {
    switch (category) {
      case 'add_money':
        return <ArrowDownLeft size={16} className="text-green-600" />;
      case 'withdraw':
        return <ArrowUpRight size={16} className="text-red-600" />;
      case 'payment_sent':
      case 'payment_received':
        return <CreditCard size={16} className={type === 'credit' ? 'text-green-600' : 'text-blue-600'} />;
      default:
        return <CreditCard size={16} className="text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-100';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'failed':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const isWithdrawal = (transaction: Transaction) => {
    return transaction.type === 'debit' && transaction.description.toLowerCase().includes('withdrawal');
  };

  const handleTransactionClick = async (transaction: Transaction) => {
    if (isWithdrawal(transaction)) {
      const transactionId = transaction.id;

      if (expandedTransaction === transactionId) {
        // Collapse if already expanded
        setExpandedTransaction(null);
      } else {
        // Expand and fetch withdrawal progress if not already cached
        setExpandedTransaction(transactionId);

        if (!withdrawalSteps[transactionId]) {
          try {
            const progressData = await getWithdrawalProgress(parseInt(transactionId));
            if (progressData) {
              setWithdrawalSteps(prev => ({
                ...prev,
                [transactionId]: progressData.steps
              }));
            }
          } catch (error) {
            console.error('Error fetching withdrawal progress:', error);
          }
        }
      }
    }
  };

  const getWithdrawalSteps = (transaction: Transaction) => {
    const cachedSteps = withdrawalSteps[transaction.id];
    if (cachedSteps) {
      return cachedSteps;
    }

    // Fallback to basic initial step if not loaded from database yet
    // With the new progressive approach, we only show steps that have actually occurred
    const status = transaction.status;
    const steps = [
      {
        title: 'Withdrawal Initiated',
        description: 'Your withdrawal request has been received and is being processed',
        status: 'completed',
        completed_at: transaction.date,
        step_key: 'initiated'
      }
    ];

    // If the transaction is failed, show the failure
    if (status === 'failed') {
      steps.push({
        title: 'Withdrawal Failed',
        description: 'Your withdrawal could not be completed',
        status: 'failed',
        completed_at: transaction.date,
        step_key: 'failed'
      });
    }

    return steps;
  };

  const getStepIcon = (stepStatus: string, stepKey?: string) => {
    switch (stepStatus) {
      case 'completed':
        // Different icons for different types of completion
        if (stepKey === 'reversed' || stepKey === 'refunded') {
          return <AlertCircle size={20} className="text-orange-600" />;
        }
        if (stepKey === 'cancelled') {
          return <X size={20} className="text-gray-600" />;
        }
        return <CheckCircle size={20} className="text-green-600" />;
      case 'failed':
        return <AlertCircle size={20} className="text-red-600" />;
      case 'skipped':
        return <div className="w-5 h-5 rounded-full border-2 border-gray-300" />;
      case 'pending':
        return <Clock size={20} className="text-blue-600" />;
      default:
        return <Clock size={20} className="text-gray-400" />;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">Transaction History</h2>
          <div className="flex items-center space-x-2">
            <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <Download size={20} className="text-gray-600" />
            </button>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X size={20} />
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="p-6 border-b bg-gray-50">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search transactions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Type Filter */}
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Types</option>
              <option value="credit">Money In</option>
              <option value="debit">Money Out</option>
            </select>

            {/* Status Filter */}
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="completed">Completed</option>
              <option value="pending">Pending</option>
              <option value="failed">Failed</option>
            </select>
          </div>
        </div>

        {/* Transaction List */}
        <div className="flex-1 overflow-y-auto">
          {isLoading ? (
            <div className="text-center py-12">
              <PaymentLoader
                type="transaction"
                message="Loading transactions..."
                size="large"
                showQuotes={true}
              />
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-red-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                <CreditCard size={24} className="text-red-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Error loading transactions</h3>
              <p className="text-gray-500 mb-4">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Retry
              </button>
            </div>
          ) : filteredTransactions.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                <CreditCard size={24} className="text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No transactions found</h3>
              <p className="text-gray-500">Try adjusting your search or filter criteria.</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {filteredTransactions.map((transaction) => {
                const isExpanded = expandedTransaction === transaction.id;
                const isWithdrawalTx = isWithdrawal(transaction);

                return (
                  <div key={transaction.id} className="overflow-hidden">
                    {/* Transaction Row */}
                    <div
                      className={`p-6 transition-all duration-200 ${
                        isWithdrawalTx
                          ? 'hover:bg-blue-50 cursor-pointer'
                          : 'hover:bg-gray-50'
                      } ${isExpanded ? 'bg-blue-50' : ''}`}
                      onClick={() => handleTransactionClick(transaction)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                            transaction.type === 'credit' ? 'bg-green-100' : 'bg-red-100'
                          }`}>
                            {getTransactionIcon(transaction.category, transaction.type)}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <p className="font-medium text-gray-900">{transaction.description}</p>
                              {isWithdrawalTx && (
                                <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full font-medium">
                                  Withdrawal
                                </span>
                              )}
                            </div>
                            <div className="flex items-center space-x-3 mt-1">
                              <p className="text-sm text-gray-500">{formatDate(transaction.date)}</p>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(transaction.status)}`}>
                                {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                              </span>
                            </div>
                            {transaction.reference && (
                              <p className="text-xs text-gray-400 mt-1">Ref: {transaction.reference}</p>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <div className="text-right">
                            <p className={`text-lg font-semibold ${
                              transaction.type === 'credit' ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {transaction.type === 'credit' ? '+' : '-'}${transaction.amount.toFixed(2)}
                            </p>
                          </div>
                          {isWithdrawalTx && (
                            <div className={`transform transition-transform duration-200 ${
                              isExpanded ? 'rotate-90' : ''
                            }`}>
                              <ChevronRight size={20} className="text-gray-400" />
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Expanded Withdrawal Details */}
                    {isWithdrawalTx && (
                      <div className={`transition-all duration-300 ease-in-out ${
                        isExpanded
                          ? 'max-h-96 opacity-100'
                          : 'max-h-0 opacity-0'
                      } overflow-hidden`}>
                        <div className="px-6 pb-6 bg-gradient-to-b from-blue-50 to-white">
                          <div className="border-l-4 border-blue-200 pl-6 ml-6">
                            {/* Withdrawal Progress */}
                            <div className="space-y-4">
                              <h4 className="font-medium text-gray-900 text-sm mb-3">Withdrawal Progress</h4>
                              {getWithdrawalSteps(transaction).map((step, index) => (
                                <div key={index} className="flex items-start space-x-3">
                                  <div className="flex-shrink-0 mt-0.5">
                                    {getStepIcon(step.status, step.step_key)}
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <div className="flex items-center justify-between">
                                      <p className={`text-sm font-medium ${
                                        step.status === 'completed' ?
                                          (step.step_key === 'reversed' || step.step_key === 'refunded' ? 'text-orange-700' :
                                           step.step_key === 'cancelled' ? 'text-gray-700' : 'text-green-700') :
                                        step.status === 'failed' ? 'text-red-700' :
                                        step.status === 'skipped' ? 'text-gray-500' : 'text-blue-700'
                                      }`}>
                                        {step.title}
                                      </p>
                                      {step.completed_at && (
                                        <span className="text-xs text-gray-500 ml-2">
                                          {formatDate(step.completed_at)}
                                        </span>
                                      )}
                                    </div>
                                    <p className={`text-xs mt-1 ${
                                      step.status === 'completed' ?
                                        (step.step_key === 'reversed' || step.step_key === 'refunded' ? 'text-orange-600' :
                                         step.step_key === 'cancelled' ? 'text-gray-600' : 'text-green-600') :
                                      step.status === 'failed' ? 'text-red-600' :
                                      step.status === 'skipped' ? 'text-gray-400' : 'text-blue-600'
                                    }`}>
                                      {step.description}
                                    </p>
                                  </div>
                                </div>
                              ))}
                            </div>

                            {/* Processing Info */}
                            <div className="mt-4 p-3 bg-white rounded-lg border border-blue-200">
                              <div className="flex items-start space-x-2">
                                <Building2 size={14} className="text-blue-600 mt-0.5 flex-shrink-0" />
                                <div>
                                  <p className="text-xs font-medium text-blue-900">Processing Information</p>
                                  <p className="text-xs text-blue-700 mt-1">
                                    {(() => {
                                      const steps = getWithdrawalSteps(transaction);
                                      const hasReversed = steps.some(s => s.step_key === 'reversed' && s.status === 'completed');
                                      const hasRefunded = steps.some(s => s.step_key === 'refunded' && s.status === 'completed');
                                      const hasCancelled = steps.some(s => s.step_key === 'cancelled' && s.status === 'completed');
                                      const hasRejected = steps.some(s => s.step_key === 'review_rejected' && s.status === 'failed');
                                      const hasFailed = steps.some(s => s.status === 'failed' && !['review_rejected'].includes(s.step_key || ''));

                                      if (hasReversed) {
                                        return 'Your withdrawal has been reversed. The funds have been returned to your wallet balance.';
                                      } else if (hasRefunded) {
                                        return 'Your withdrawal has been refunded due to a bank processing issue. The funds have been returned to your wallet.';
                                      } else if (hasCancelled) {
                                        return 'Your withdrawal request has been cancelled. No funds were transferred.';
                                      } else if (hasRejected) {
                                        return 'Your withdrawal was rejected during manual review. Please contact support for more information.';
                                      } else if (hasFailed) {
                                        return 'Your withdrawal failed during processing. Please contact support for assistance.';
                                      } else if (transaction.status === 'completed') {
                                        return 'Withdrawal completed successfully. Funds are now available in your bank account.';
                                      } else {
                                        return 'Your withdrawal is being processed. Funds typically arrive within 1-3 business days.';
                                      }
                                    })()}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t bg-gray-50">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <p>Showing {filteredTransactions.length} of {transactions.length} transactions</p>
            <button className="text-blue-600 hover:text-blue-700 font-medium">
              Load More
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TransactionHistory;
