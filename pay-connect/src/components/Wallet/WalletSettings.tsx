import React, { useState } from 'react';
import { X, Lock, Bell, Shield, CreditCard, Eye, EyeOff, Mail } from 'lucide-react';
import { Wallet, changeWalletPin, sendPinChangeOTP, sendPinResetOTP, resetWalletPin } from '../../services/wallet';

interface WalletSettingsProps {
  isOpen: boolean;
  onClose: () => void;
  wallet: Wallet;
  onWalletUpdate: (wallet: Wallet) => void;
}

const WalletSettings: React.FC<WalletSettingsProps> = ({
  isOpen,
  onClose,
  wallet,
  onWalletUpdate
}) => {
  const [activeTab, setActiveTab] = useState<'security' | 'notifications' | 'limits'>('security');
  const [showCurrentPin, setShowCurrentPin] = useState(false);
  const [showNewPin, setShowNewPin] = useState(false);
  const [currentPin, setCurrentPin] = useState('');
  const [newPin, setNewPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [otpCode, setOtpCode] = useState('');
  const [isChangingPin, setIsChangingPin] = useState(false);
  const [isSendingOTP, setIsSendingOTP] = useState(false);
  const [otpSent, setOtpSent] = useState(false);
  const [userEmail, setUserEmail] = useState('');
  const [otpExpiresIn, setOtpExpiresIn] = useState(5);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // PIN Reset states
  const [isResettingPin, setIsResettingPin] = useState(false);
  const [resetNewPin, setResetNewPin] = useState('');
  const [resetConfirmPin, setResetConfirmPin] = useState('');
  const [resetOtpCode, setResetOtpCode] = useState('');
  const [isResetMode, setIsResetMode] = useState(false);
  const [isSendingResetOTP, setIsSendingResetOTP] = useState(false);
  const [resetOtpSent, setResetOtpSent] = useState(false);
  const [resetUserEmail, setResetUserEmail] = useState('');
  const [resetOtpExpiresIn, setResetOtpExpiresIn] = useState(5);
  const [showResetNewPin, setShowResetNewPin] = useState(false);

  // Mock settings state
  const [settings, setSettings] = useState({
    notifications: {
      transactionAlerts: true,
      lowBalanceAlert: true,
      securityAlerts: true,
      emailNotifications: true,
      smsNotifications: false
    },
    limits: {
      dailySpendLimit: 1000,
      monthlySpendLimit: 5000,
      singleTransactionLimit: 500
    }
  });

  const handleSendOTP = async () => {
    if (currentPin.length < 4 || currentPin.length > 6) {
      setError('Please enter your current PIN first');
      return;
    }

    if (newPin !== confirmPin) {
      setError('New PIN and confirmation do not match');
      return;
    }

    if (newPin.length < 4 || newPin.length > 6) {
      setError('PIN must be 4-6 digits');
      return;
    }

    if (!/^\d+$/.test(newPin)) {
      setError('PIN must contain only numbers');
      return;
    }

    try {
      setIsSendingOTP(true);
      setError(null);

      const result = await sendPinChangeOTP();

      if (result.success) {
        setOtpSent(true);
        setUserEmail(result.email || '');
        setOtpExpiresIn(result.expiresInMinutes || 5);
        setSuccess('OTP sent to your email. Please check and enter the code below.');

        // Auto-fill OTP in development mode
        if (result.otp) {
          setOtpCode(result.otp);
        }

        setTimeout(() => {
          setSuccess(null);
        }, 3000);
      } else {
        setError(result.message || 'Failed to send OTP');
      }

    } catch (err) {
      setError('Failed to send OTP. Please try again.');
    } finally {
      setIsSendingOTP(false);
    }
  };

  const handlePinChange = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!otpSent) {
      setError('Please send OTP first');
      return;
    }

    if (!otpCode || otpCode.length !== 6) {
      setError('Please enter the 6-digit OTP code');
      return;
    }

    try {
      setIsChangingPin(true);
      setError(null);

      // Call the real API to change PIN with OTP
      const result = await changeWalletPin(currentPin, newPin, otpCode);

      if (result.success) {
        setSuccess(result.message || 'PIN changed successfully');
        setCurrentPin('');
        setNewPin('');
        setConfirmPin('');
        setOtpCode('');
        setOtpSent(false);
        setUserEmail('');

        setTimeout(() => {
          setSuccess(null);
        }, 3000);
      } else {
        setError(result.message || 'Failed to change PIN');
      }

    } catch (err) {
      setError('Failed to change PIN. Please try again.');
    } finally {
      setIsChangingPin(false);
    }
  };

  const handleSendResetOTP = async () => {
    if (resetNewPin !== resetConfirmPin) {
      setError('New PIN and confirmation do not match');
      return;
    }

    if (resetNewPin.length < 4 || resetNewPin.length > 6) {
      setError('PIN must be 4-6 digits');
      return;
    }

    if (!/^\d+$/.test(resetNewPin)) {
      setError('PIN must contain only numbers');
      return;
    }

    try {
      setIsSendingResetOTP(true);
      setError(null);

      const result = await sendPinResetOTP();

      if (result.success) {
        setResetOtpSent(true);
        setResetUserEmail(result.email || '');
        setResetOtpExpiresIn(result.expiresInMinutes || 5);
        setSuccess('PIN reset code sent to your email. Please check and enter the code below.');

        // Auto-fill OTP in development mode
        if (result.otp) {
          setResetOtpCode(result.otp);
        }

        setTimeout(() => {
          setSuccess(null);
        }, 3000);
      } else {
        setError(result.message || 'Failed to send PIN reset code');
      }

    } catch (err) {
      setError('Failed to send PIN reset code. Please try again.');
    } finally {
      setIsSendingResetOTP(false);
    }
  };

  const handlePinReset = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!resetOtpSent) {
      setError('Please send PIN reset code first');
      return;
    }

    if (!resetOtpCode || resetOtpCode.length !== 6) {
      setError('Please enter the 6-digit reset code');
      return;
    }

    try {
      setIsResettingPin(true);
      setError(null);

      // Call the real API to reset PIN with OTP
      const result = await resetWalletPin(resetNewPin, resetOtpCode);

      if (result.success) {
        setSuccess(result.message || 'PIN reset successfully');
        setResetNewPin('');
        setResetConfirmPin('');
        setResetOtpCode('');
        setResetOtpSent(false);
        setResetUserEmail('');
        setIsResetMode(false);

        setTimeout(() => {
          setSuccess(null);
        }, 3000);
      } else {
        setError(result.message || 'Failed to reset PIN');
      }

    } catch (err) {
      setError('Failed to reset PIN. Please try again.');
    } finally {
      setIsResettingPin(false);
    }
  };

  const handleStartPinReset = () => {
    setIsResetMode(true);
    setError(null);
    setSuccess(null);
    // Clear change PIN form
    setCurrentPin('');
    setNewPin('');
    setConfirmPin('');
    setOtpCode('');
    setOtpSent(false);
    setUserEmail('');
  };

  const handleCancelPinReset = () => {
    setIsResetMode(false);
    setError(null);
    setSuccess(null);
    // Clear reset PIN form
    setResetNewPin('');
    setResetConfirmPin('');
    setResetOtpCode('');
    setResetOtpSent(false);
    setResetUserEmail('');
  };

  const handleNotificationChange = (key: string, value: boolean) => {
    setSettings(prev => ({
      ...prev,
      notifications: {
        ...prev.notifications,
        [key]: value
      }
    }));
  };

  const handleLimitChange = (key: string, value: number) => {
    setSettings(prev => ({
      ...prev,
      limits: {
        ...prev.limits,
        [key]: value
      }
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">Wallet Settings</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'security', label: 'Security', icon: Lock },
              { id: 'notifications', label: 'Notifications', icon: Bell },
              { id: 'limits', label: 'Limits', icon: Shield }
            ].map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                onClick={() => setActiveTab(id as any)}
                className={`py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <Icon size={16} />
                  <span>{label}</span>
                </div>
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {activeTab === 'security' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Security Settings</h3>
                
                {/* Change PIN or Reset PIN */}
                <div className="bg-gray-50 rounded-lg p-4 mb-6">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium text-gray-900">
                      {isResetMode ? 'Reset Wallet PIN' : 'Change Wallet PIN'}
                    </h4>
                    {!isResetMode && !otpSent && (
                      <button
                        type="button"
                        onClick={handleStartPinReset}
                        className="text-sm text-blue-600 hover:text-blue-800 underline"
                      >
                        Forgot PIN?
                      </button>
                    )}
                  </div>

                  {!isResetMode ? (
                    <form onSubmit={handlePinChange} className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Current PIN
                      </label>
                      <div className="relative">
                        <input
                          type={showCurrentPin ? 'text' : 'password'}
                          value={currentPin}
                          onChange={(e) => setCurrentPin(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          maxLength={6}
                          required
                          disabled={otpSent}
                        />
                        <button
                          type="button"
                          onClick={() => setShowCurrentPin(!showCurrentPin)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2"
                        >
                          {showCurrentPin ? <EyeOff size={16} /> : <Eye size={16} />}
                        </button>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        New PIN
                      </label>
                      <div className="relative">
                        <input
                          type={showNewPin ? 'text' : 'password'}
                          value={newPin}
                          onChange={(e) => setNewPin(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          maxLength={6}
                          required
                          disabled={otpSent}
                        />
                        <button
                          type="button"
                          onClick={() => setShowNewPin(!showNewPin)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2"
                        >
                          {showNewPin ? <EyeOff size={16} /> : <Eye size={16} />}
                        </button>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Confirm New PIN
                      </label>
                      <input
                        type="password"
                        value={confirmPin}
                        onChange={(e) => setConfirmPin(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        maxLength={6}
                        required
                        disabled={otpSent}
                      />
                    </div>

                    {!otpSent && (
                      <button
                        type="button"
                        onClick={handleSendOTP}
                        disabled={isSendingOTP}
                        className="w-full bg-orange-600 hover:bg-orange-700 text-white py-2 px-4 rounded-lg font-medium transition-colors disabled:opacity-50 flex items-center justify-center space-x-2"
                      >
                        <Mail size={16} />
                        <span>{isSendingOTP ? 'Sending OTP...' : 'Send OTP to Email'}</span>
                      </button>
                    )}

                    {otpSent && (
                      <div className="space-y-3">
                        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                          <p className="text-sm text-blue-700">
                            OTP sent to {userEmail}. Please check your email and enter the 6-digit code below.
                            <br />
                            <span className="text-xs">Code expires in {otpExpiresIn} minutes.</span>
                          </p>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Enter OTP Code
                          </label>
                          <input
                            type="text"
                            value={otpCode}
                            onChange={(e) => setOtpCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-center text-lg font-mono"
                            maxLength={6}
                            placeholder="000000"
                            required
                          />
                        </div>
                      </div>
                    )}

                    {error && (
                      <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                        <p className="text-sm text-red-600">{error}</p>
                      </div>
                    )}

                    {success && (
                      <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                        <p className="text-sm text-green-600">{success}</p>
                      </div>
                    )}

                    {otpSent && (
                      <button
                        type="submit"
                        disabled={isChangingPin || !otpCode || otpCode.length !== 6}
                        className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium transition-colors disabled:opacity-50"
                      >
                        {isChangingPin ? 'Changing PIN...' : 'Change PIN'}
                      </button>
                    )}

                    {otpSent && (
                      <button
                        type="button"
                        onClick={() => {
                          setOtpSent(false);
                          setOtpCode('');
                          setUserEmail('');
                          setError(null);
                        }}
                        className="w-full bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg font-medium transition-colors"
                      >
                        Cancel & Start Over
                      </button>
                    )}
                  </form>
                  ) : (
                    /* Reset PIN Form */
                    <form onSubmit={handlePinReset} className="space-y-4">
                      <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg mb-4">
                        <p className="text-sm text-yellow-700">
                          <strong>Forgot your PIN?</strong> No worries! Enter a new PIN below and we'll send a verification code to your email.
                        </p>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          New PIN
                        </label>
                        <div className="relative">
                          <input
                            type={showResetNewPin ? 'text' : 'password'}
                            value={resetNewPin}
                            onChange={(e) => setResetNewPin(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            maxLength={6}
                            required
                            disabled={resetOtpSent}
                          />
                          <button
                            type="button"
                            onClick={() => setShowResetNewPin(!showResetNewPin)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2"
                          >
                            {showResetNewPin ? <EyeOff size={16} /> : <Eye size={16} />}
                          </button>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Confirm New PIN
                        </label>
                        <input
                          type="password"
                          value={resetConfirmPin}
                          onChange={(e) => setResetConfirmPin(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          maxLength={6}
                          required
                          disabled={resetOtpSent}
                        />
                      </div>

                      {!resetOtpSent && (
                        <button
                          type="button"
                          onClick={handleSendResetOTP}
                          disabled={isSendingResetOTP}
                          className="w-full bg-orange-600 hover:bg-orange-700 text-white py-2 px-4 rounded-lg font-medium transition-colors disabled:opacity-50 flex items-center justify-center space-x-2"
                        >
                          <Mail size={16} />
                          <span>{isSendingResetOTP ? 'Sending Reset Code...' : 'Send Reset Code to Email'}</span>
                        </button>
                      )}

                      {resetOtpSent && (
                        <div className="space-y-3">
                          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                            <p className="text-sm text-blue-700">
                              Reset code sent to {resetUserEmail}. Please check your email and enter the 6-digit code below.
                              <br />
                              <span className="text-xs">Code expires in {resetOtpExpiresIn} minutes.</span>
                            </p>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Enter Reset Code
                            </label>
                            <input
                              type="text"
                              value={resetOtpCode}
                              onChange={(e) => setResetOtpCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-center text-lg font-mono"
                              maxLength={6}
                              placeholder="000000"
                              required
                            />
                          </div>
                        </div>
                      )}

                      {error && (
                        <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                          <p className="text-sm text-red-600">{error}</p>
                        </div>
                      )}

                      {success && (
                        <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                          <p className="text-sm text-green-600">{success}</p>
                        </div>
                      )}

                      {resetOtpSent && (
                        <button
                          type="submit"
                          disabled={isResettingPin || !resetOtpCode || resetOtpCode.length !== 6}
                          className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium transition-colors disabled:opacity-50"
                        >
                          {isResettingPin ? 'Resetting PIN...' : 'Reset PIN'}
                        </button>
                      )}

                      <button
                        type="button"
                        onClick={handleCancelPinReset}
                        className="w-full bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg font-medium transition-colors"
                      >
                        Cancel & Go Back
                      </button>
                    </form>
                  )}
                </div>

                {/* Two-Factor Authentication */}
                {/* <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-gray-900">Two-Factor Authentication</h4>
                      <p className="text-sm text-gray-600">Add an extra layer of security to your wallet</p>
                    </div>
                    <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                      Enable
                    </button>
                  </div>
                </div> */}
              </div>
            </div>
          )}

          {activeTab === 'notifications' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Notification Preferences</h3>
              
              <div className="space-y-4">
                {Object.entries(settings.notifications).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between py-3 border-b border-gray-200">
                    <div>
                      <p className="font-medium text-gray-900">
                        {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                      </p>
                      <p className="text-sm text-gray-600">
                        {key === 'transactionAlerts' && 'Get notified for all transactions'}
                        {key === 'lowBalanceAlert' && 'Alert when balance is low'}
                        {key === 'securityAlerts' && 'Important security notifications'}
                        {key === 'emailNotifications' && 'Receive notifications via email'}
                        {key === 'smsNotifications' && 'Receive notifications via SMS'}
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={value}
                        onChange={(e) => handleNotificationChange(key, e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'limits' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Spending Limits</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Daily Spending Limit
                  </label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                    <input
                      type="number"
                      value={settings.limits.dailySpendLimit}
                      onChange={(e) => handleLimitChange('dailySpendLimit', parseInt(e.target.value))}
                      className="w-full pl-8 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Monthly Spending Limit
                  </label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                    <input
                      type="number"
                      value={settings.limits.monthlySpendLimit}
                      onChange={(e) => handleLimitChange('monthlySpendLimit', parseInt(e.target.value))}
                      className="w-full pl-8 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Single Transaction Limit
                  </label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                    <input
                      type="number"
                      value={settings.limits.singleTransactionLimit}
                      onChange={(e) => handleLimitChange('singleTransactionLimit', parseInt(e.target.value))}
                      className="w-full pl-8 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium transition-colors">
                  Save Limits
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default WalletSettings;
