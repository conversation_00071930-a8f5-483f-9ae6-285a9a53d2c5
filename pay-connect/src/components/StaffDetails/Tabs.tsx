import React from 'react';

interface TabProps {
  label: string;
  count: number;
  isActive: boolean;
  onClick: () => void;
}

const Tab: React.FC<TabProps> = ({ label, count, isActive, onClick }) => {
  return (
    <button
      className={`px-4 py-2 rounded-lg font-medium text-sm transition-colors duration-200
        ${
          isActive
            ? 'bg-orange-500 text-white shadow-md'
            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
        }`}
      onClick={onClick}
    >
      {label} ({count})
    </button>
  );
};

interface TabsProps {
  activeTab: 'gameActivity' | 'transactions' | 'bankAccounts';
  gameActivityCount: number;
  transactionsCount: number;
  bankAccountsCount: number;
  onTabChange: (tab: 'gameActivity' | 'transactions' | 'bankAccounts') => void;
}

const Tabs: React.FC<TabsProps> = ({
  activeTab,
  gameActivityCount,
  transactionsCount,
  bankAccountsCount,
  onTabChange,
}) => {
  const tabs = [
    { id: 'transactions', label: 'Transactions', count: transactionsCount },
    { id: 'bankAccounts', label: 'Bank Accounts', count: bankAccountsCount },
    { id: 'gameActivity', label: 'Game Activity', count: gameActivityCount },
  ];

  return (
    <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
      {tabs.map((tab) => (
        <button
          key={tab.id}
          onClick={() => onTabChange(tab.id as any)}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === tab.id
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          {tab.label} ({tab.count})
        </button>
      ))}
    </div>
  );
};

export default Tabs; 
