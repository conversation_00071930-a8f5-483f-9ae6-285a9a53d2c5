import React from 'react';
import { Phone, Mail, MapPin } from 'lucide-react';
import { StaffDetail } from '../../services/staffDetails';

interface ProfileCardProps {
  staff: StaffDetail;
}

const ProfileCard: React.FC<ProfileCardProps> = ({ staff }) => {
  return (
    <div className="bg-blue-white border-1 border-blue-600 rounded-[30px] shadow-sm border p-6 flex flex-col md:flex-row items-center md:items-start space-y-4 md:space-y-0 md:space-x-6">
      <div className="flex-shrink-0">
        <img
          className="h-24 w-24 rounded-full object-cover"
          src={staff.profile_pic  || `https://ui-avatars.com/api/?name=${encodeURIComponent(staff.full_name)}`}
          alt={staff.full_name}
        />
      </div>
      <div className="text-center md:text-left">
        <h3 className="text-xl font-bold text-blue-700">{staff.full_name}</h3>
        <p className="text-blue-600 font-medium text-sm mb-2">{staff.role}</p>
        <div className="flex items-center justify-center md:justify-start text-blue-600 text-sm mb-1">
          <Mail size={16} className="mr-2" />
          <span>{staff.email}</span>
        </div>
        {/* <div className="flex items-center justify-center md:justify-start text-gray-600 text-sm mb-1">
          <Phone size={16} className="mr-2" />
          <span>{staff.phone}</span>
        </div>
        <div className="flex items-center justify-center md:justify-start text-gray-600 text-sm">
          <MapPin size={16} className="mr-2" />
          <span>{staff.location}</span>
        </div> */}
      </div>
    </div>
  );
};

export default ProfileCard; 