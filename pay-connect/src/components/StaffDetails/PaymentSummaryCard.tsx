import { AlertCircle, Calendar, Wallet } from 'lucide-react';
import React from 'react';
// import { Wallet, Calendar, AlertCircle } from 'lucide-react'; 

interface PaymentSummaryCardProps {
  title: string;
  value: number;
  dateLabel?: string;
  date?: string;
  showButton?: boolean;
}

const PaymentSummaryCard: React.FC<PaymentSummaryCardProps> = ({
  title,
  value,
  dateLabel,
  date,
  showButton = false,
}) => {
  const handlePayNow = () => {
    alert('Pay Now functionality will be implemented here.');
  };

  // ✅ Match icon based on title
  const getIcon = () => {
    switch (title) {
      case 'Total Payment Paid':
        return <Wallet className="w-8 h-8 text-blue-600 mb-2" />;
      case 'Upcoming Payment':
        return <Calendar className="w-8 h-8  text-green-600 mb-2" />;
      case 'Overdue Payment':
        return <AlertCircle className="w-8 h-8  text-red-600 mb-2" />;
      default:
        return null;
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border p-4 flex flex-col items-center justify-center text-center">
      {getIcon()} {/* ✅ Render the icon */}
      <p className="text-sm text-gray-500 mb-1">{title}</p>
      <p className="text-2xl font-bold text-gray-900 mb-2">${value.toFixed(2)}</p>
      {date && (
        <p className="text-xs text-gray-500 mb-2">
          {dateLabel}: {new Date(date).toLocaleDateString()}
        </p>
      )}
      {showButton && (
        <button
          onClick={handlePayNow}
          className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm transition-colors"
        >
          Pay Now
        </button>
      )}
    </div>
  );
};

export default PaymentSummaryCard;
