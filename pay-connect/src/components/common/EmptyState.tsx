import React from 'react';
import { FileX, Users, CreditCard, Receipt, Search } from 'lucide-react';

interface EmptyStateProps {
  type: 'staff' | 'payments' | 'dues' | 'transactions' | 'search';
  title?: string;
  description?: string;
  actionText?: string;
  onAction?: () => void;
  className?: string;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  type,
  title,
  description,
  actionText,
  onAction,
  className = ''
}) => {
  const getIcon = () => {
    switch (type) {
      case 'staff':
        return <Users className="h-12 w-12 text-gray-400" />;
      case 'payments':
        return <CreditCard className="h-12 w-12 text-gray-400" />;
      case 'dues':
        return <Receipt className="h-12 w-12 text-gray-400" />;
      case 'transactions':
        return <FileX className="h-12 w-12 text-gray-400" />;
      case 'search':
        return <Search className="h-12 w-12 text-gray-400" />;
      default:
        return <FileX className="h-12 w-12 text-gray-400" />;
    }
  };

  const getDefaultContent = () => {
    switch (type) {
      case 'staff':
        return {
          title: 'No staff members found',
          description: 'Get started by Importing your first staff member to the system.',
          actionText: 'Add Staff Member'
        };
      case 'payments':
        return {
          title: 'No awaiting payments',
          description: 'All payments are up to date. New payments will appear here when they are due.',
          actionText: 'Refresh'
        };
      case 'dues':
        return {
          title: 'No dues found',
          description: 'All dues have been paid or no dues have been created yet.',
          actionText: 'Create Due'
        };
      case 'transactions':
        return {
          title: 'No transactions found',
          description: 'Transaction history will appear here once payments are processed.',
          actionText: 'Refresh'
        };
      case 'search':
        return {
          title: 'No results found',
          description: 'Try adjusting your search criteria or filters to find what you\'re looking for.',
          actionText: 'Clear Filters'
        };
      default:
        return {
          title: 'No data found',
          description: 'There is no data to display at the moment.',
          actionText: 'Refresh'
        };
    }
  };

  const defaultContent = getDefaultContent();
  const finalTitle = title || defaultContent.title;
  const finalDescription = description || defaultContent.description;
  const finalActionText = actionText || defaultContent.actionText;

  return (
    <div className={`text-center py-12 px-4 ${className}`}>
      <div className="flex justify-center mb-4">
        {getIcon()}
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        {finalTitle}
      </h3>
      <p className="text-sm text-gray-500 mb-6 max-w-sm mx-auto">
        {finalDescription}
      </p>
      {onAction && (
        <button
          onClick={onAction}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
        >
          {finalActionText}
        </button>
      )}
    </div>
  );
};

export default EmptyState;
