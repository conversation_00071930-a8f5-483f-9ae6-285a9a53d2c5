import React, { useRef, useEffect } from 'react';
import { X } from 'lucide-react';

interface OffcanvasProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  title?: string;
  width?: string; // e.g., "w-1/3", "w-96", "w-screen"
}

const Offcanvas: React.FC<OffcanvasProps> = ({
  isOpen,
  onClose,
  children,
  title = 'Details',
  width = 'w-98',
}) => {
  const offcanvasRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    const handleClickOutside = (event: MouseEvent) => {
      if (offcanvasRef.current && !offcanvasRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('keydown', handleEscape);
      document.removeEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  return (
    <div
      className={`fixed inset-0 z-50 overflow-hidden ${isOpen ? 'pointer-events-auto' : 'pointer-events-none'}`}
      aria-labelledby="slide-over-title"
      role="dialog"
      aria-modal="true"
    >
      {/* Overlay */}
      <div
        className={`absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity duration-300 ease-in-out
          ${isOpen ? 'opacity-100' : 'opacity-0'}`}
      ></div>

      {/* Offcanvas Panel */}
      <div
        ref={offcanvasRef}
        className={`fixed inset-y-0 right-0 max-w-full flex transition-transform duration-300 ease-in-out
          ${isOpen ? 'translate-x-0' : 'translate-x-full'}`}
      >
        <div className={`relative ${width} bg-white shadow-xl flex flex-col`}>
          <div className="px-4 py-5 sm:px-6 border-b flex justify-between items-center">
            <h2 className="text-lg font-medium text-gray-900" id="slide-over-title">
              {title}
            </h2>
            <button
              type="button"
              className="ml-3 h-7 w-7 rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              onClick={onClose}
            >
              <span className="sr-only">Close panel</span>
              <X size={24} aria-hidden="true" />
            </button>
          </div>
          <div className="relative flex-1 py-6 px-6 overflow-y-auto scrollbar-hide">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Offcanvas; 