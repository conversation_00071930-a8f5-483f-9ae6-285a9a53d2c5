import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import PaymentLoader from '../PaymentLoader';

// Mock the logo import
jest.mock('../../../assets/images/giu_pay_connect.png', () => 'mocked-logo.png');

describe('PaymentLoader', () => {
  it('renders with default props', () => {
    render(<PaymentLoader />);
    
    // Check if the logo is rendered
    const logo = screen.getByAltText('Pay Connect Logo');
    expect(logo).toBeInTheDocument();
    expect(logo).toHaveAttribute('src', 'mocked-logo.png');
  });

  it('renders with custom message', () => {
    const customMessage = 'Processing your secure payment...';
    render(<PaymentLoader message={customMessage} />);
    
    expect(screen.getByText(customMessage)).toBeInTheDocument();
  });

  it('renders different types correctly', () => {
    const { rerender } = render(<PaymentLoader type="authentication" showQuotes={false} />);
    
    // Should render without quotes when showQuotes is false
    expect(screen.queryByText(/Verifying your identity/)).not.toBeInTheDocument();
    
    // Rerender with quotes enabled
    rerender(<PaymentLoader type="authentication" showQuotes={true} />);
    
    // Should show authentication-specific quotes
    expect(screen.getByText(/🔐|🛡️|🔑|✨/)).toBeInTheDocument();
  });

  it('renders different sizes correctly', () => {
    const { rerender } = render(<PaymentLoader size="small" />);
    
    // Check if small size classes are applied
    const container = screen.getByAltText('Pay Connect Logo').closest('div');
    expect(container).toHaveClass('w-12', 'h-12');
    
    // Test medium size
    rerender(<PaymentLoader size="medium" />);
    const mediumContainer = screen.getByAltText('Pay Connect Logo').closest('div');
    expect(mediumContainer).toHaveClass('w-20', 'h-20');
    
    // Test large size
    rerender(<PaymentLoader size="large" />);
    const largeContainer = screen.getByAltText('Pay Connect Logo').closest('div');
    expect(largeContainer).toHaveClass('w-28', 'h-28');
  });

  it('shows progress dots when multiple quotes are available', () => {
    render(<PaymentLoader type="processing" showQuotes={true} />);
    
    // Should show progress dots for quote rotation
    const progressDots = screen.getAllByRole('generic').filter(el => 
      el.className.includes('w-2 h-2 rounded-full')
    );
    expect(progressDots.length).toBeGreaterThan(1);
  });

  it('hides quotes when showQuotes is false', () => {
    render(<PaymentLoader showQuotes={false} />);
    
    // Should not show any quotes
    expect(screen.queryByText(/💳|🚀|🔒|⚡|💰/)).not.toBeInTheDocument();
  });

  it('renders animated elements', () => {
    render(<PaymentLoader />);
    
    // Check for animated spinner
    const spinners = screen.getAllByRole('generic').filter(el => 
      el.className.includes('animate-spin')
    );
    expect(spinners.length).toBeGreaterThan(0);
    
    // Check for animated border
    const animatedBorder = screen.getByRole('generic', { hidden: true });
    expect(animatedBorder.className).toContain('animate-pulse');
  });
});
