import React, { useState, useEffect } from 'react';
import {
  Star, // ← add this
  CreditCard, Wallet, Shield, Zap, TrendingUp, DollarSign,
  ShieldCheck, Lock, KeyRound, Settings, MailCheck, SearchCheck,
  CheckCircle2, Link2, RefreshCw, BarChart3, Banknote, Briefcase,
  FileText, PieChart, ClipboardList, Wrench, LayoutDashboard, Rocket,
  Users
} from 'lucide-react';
import logoImage from '../../assets/images/giu_pay_connect.png';

interface PaymentLoaderProps {
  type?: 'authentication' | 'verification' | 'processing' | 'wallet' | 'transaction' | 'setup' | 'team';
  message?: string;
  size?: 'small' | 'medium' | 'large';
  showQuotes?: boolean;
}

type QuoteItem = {
  icon: React.ReactNode;
  text: string;
};

const fallbackQuotes: QuoteItem[] = [
  { icon: <CreditCard size={16} />, text: "Securing your financial future, one transaction at a time" },
  { icon: <Rocket size={16} />, text: "Making payments as easy as a single tap" },
  { icon: <Shield size={16} />, text: "Your money, your security, our priority" },
  { icon: <Zap size={16} />, text: "Lightning-fast payments for the modern world" },
  { icon: <DollarSign size={16} />, text: "Building wealth through smart financial choices" },
  { icon: <CheckCircle2 size={16} />, text: "Every payment brings you closer to your goals" },
  { icon: <Link2 size={16} />, text: "Precision in payments, excellence in service" },
  { icon: <Briefcase size={16} />, text: "Your financial success is our mission" },
  { icon: <Wallet size={16} />, text: "Revolutionizing the way you handle money" },
  { icon: <Star size={16} />, text: "Where convenience meets security" },
];

const typeQuotes: Record<string, QuoteItem[]> = {
  authentication: [
    { icon: <ShieldCheck size={16} />, text: "Verifying your identity for secure access" },
    { icon: <Lock size={16} />, text: "Protecting your account with advanced security" },
    { icon: <KeyRound size={16} />, text: "Unlocking your financial dashboard" },
    { icon: <Settings size={16} />, text: "Preparing your personalized experience" }
  ],
  verification: [
    { icon: <MailCheck size={16} />, text: "Confirming your email for account security" },
    { icon: <SearchCheck size={16} />, text: "Validating your verification code" },
    { icon: <CheckCircle2 size={16} />, text: "Almost there! Securing your account" },
    { icon: <Link2 size={16} />, text: "Finalizing your secure connection" }
  ],
  processing: [
    { icon: <CreditCard size={16} />, text: "Processing your payment securely" },
    { icon: <Zap size={16} />, text: "Executing transaction at lightning speed" },
    { icon: <RefreshCw size={16} />, text: "Updating your account balance" },
    { icon: <CheckCircle2 size={16} />, text: "Completing your financial operation" }
  ],
  wallet: [
    { icon: <Wallet size={16} />, text: "Loading your wallet information" },
    { icon: <BarChart3 size={16} />, text: "Calculating your current balance" },
    { icon: <Banknote size={16} />, text: "Syncing with your financial accounts" },
    { icon: <Briefcase size={16} />, text: "Preparing your payment options" }
  ],
  transaction: [
    { icon: <PieChart size={16} />, text: "Analyzing your transaction history" },
    { icon: <ClipboardList size={16} />, text: "Organizing your payment records" },
    { icon: <FileText size={16} />, text: "Compiling your financial summary" },
    { icon: <BarChart3 size={16} />, text: "Tracking your spending patterns" }
  ],
  setup: [
    { icon: <Wrench size={16} />, text: "Setting up your secure payment profile" },
    { icon: <LayoutDashboard size={16} />, text: "Customizing your financial dashboard" },
    { icon: <Shield size={16} />, text: "Configuring your security preferences" },
    { icon: <Rocket size={16} />, text: "Preparing your payment experience" }
  ],
  team: [
    { icon: <ShieldCheck size={16} />, text: "Loading team details ..." },
    { icon: <Users size={16} />, text: "Fetching team members and info..." },
    { icon: <BarChart3 size={16} />, text: "Analyzing team payment data..." },
    { icon: <Briefcase size={16} />, text: "Preparing your team dashboard..." }
  ],
};

const PaymentLoader: React.FC<PaymentLoaderProps> = ({
  type = 'processing',
  message,
  size = 'medium',
  showQuotes = true
}) => {
  const [currentQuoteIndex, setCurrentQuoteIndex] = useState(0);
  const [currentIconIndex, setCurrentIconIndex] = useState(0);

  const paymentIcons = [CreditCard, Wallet, Shield, Zap, TrendingUp, DollarSign];
  const CurrentIcon = paymentIcons[currentIconIndex];
  const quotes = showQuotes ? (typeQuotes[type] || fallbackQuotes) : [];
  const currentQuote = quotes[currentQuoteIndex];

  useEffect(() => {
    if (quotes.length > 1) {
      const interval = setInterval(() => {
        setCurrentQuoteIndex((prev) => (prev + 1) % quotes.length);
      }, 3000);
      return () => clearInterval(interval);
    }
  }, [quotes.length]);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIconIndex((prev) => (prev + 1) % paymentIcons.length);
    }, 2000);
    return () => clearInterval(interval);
  }, []);

  const sizeClasses = {
    small: {
      container: 'w-20 h-20',
      logo: 'w-12 h-12',
      spinner: 'w-20 h-20',
      text: 'text-sm'
    },
  medium: {
container: 'w-40 h-40',
      logo: 'w-28 h-28',
      spinner: 'w-40 h-40',
      text: 'text-lg'
},
    large: {
      container: 'w-40 h-40',
      logo: 'w-28 h-28',
      spinner: 'w-40 h-40',
      text: 'text-lg'
    }
  };

  // const CurrentIcon = paymentIcons[currentIconIndex];

  return (
    <div className="flex flex-col items-center justify-center space-y-6">
      {/* Logo with animated border */}
      <div className="relative">
        <div className={`relative ${sizeClasses[size].container} flex items-center justify-center`}>

          {/* Spinner background
          <div className="absolute inset-0 border-4 border-orange-200 border-t-orange-600 rounded-full animate-spin z-0"></div>
          <div className="absolute inset-0 border-4 border-transparent border-r-purple-400 rounded-full animate-spin z-0" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>

          Animated gradient pulse background
          <div className="absolute inset-0 bg-gradient-to-r from-orange-400 via-red-500 to-purple-600 rounded-full opacity-75 animate-pulse z-10"></div>
          <div className="absolute inset-[6px] bg-white rounded-full z-20"></div> */}


          <div className="relative">
            <div className={`${sizeClasses[size].spinner} border-4 border-orange-200 border-t-orange-600 rounded-full animate-spin`}></div>
            <div className="absolute inset-0 border-4 border-transparent border-r-purple-400 rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>
          </div>


          {/* Logo centered inside spinner */}
          <img
            src={logoImage}
            alt="Pay Connect Logo"
            className={`${sizeClasses[size].logo} object-contain  z-30 rounded-full absolute`}
          />

          {/* Floating payment icon */}
          <div className="absolute -top-0 -right-5 w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center shadow-lg animate-bounce z-40">
            <CurrentIcon className="w-4 h-4 text-white" />
          </div>
        </div>
      </div>


      {/* Animated spinner */}
      {/* <div className="relative">
        <div className={`${sizeClasses[size].spinner} border-4 border-orange-200 border-t-orange-600 rounded-full animate-spin`}></div>
        <div className="absolute inset-0 border-4 border-transparent border-r-purple-400 rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>
      </div> */}

      {/* Message */}
      {message && (
        <p className={`${sizeClasses[size].text} font-semibold text-gray-800 text-center max-w-s`}>
          {message}
        </p>
      )}

      {/* Rotating quotes */}
       {showQuotes && currentQuote && (
        <div className="flex items-center justify-center space-x-2 text-sm text-gray-600 italic transition-all duration-500 ease-in-out transform">
          <span className="text-gray-500">{currentQuote.icon}</span>
          <span>{currentQuote.text}</span>
        </div>
      )}

      {/* Progress dots */}
      {showQuotes && quotes.length > 1 && (
        <div className="flex space-x-2">
          {quotes.map((_, index) => (
            <div
              key={index}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${index === currentQuoteIndex ? 'bg-orange-500 scale-125' : 'bg-gray-300'
                }`}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default PaymentLoader;
