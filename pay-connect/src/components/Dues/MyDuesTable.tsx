import React, { useState, useEffect } from 'react';
import MyDuesTableRow from './MyDuesTableRow';
import { getDues, Due } from '../../services/dues';
import { ChevronLeft, ChevronRight, CreditCard } from 'lucide-react';
import EmptyState from '../common/EmptyState';
import PaymentLoader from '../common/PaymentLoader';
import AmountRefillModal from './AmountRefillModal';

interface MyDuesTableProps {
  className?: string;
  search: string;
  statusFilter: string;
  onDataChange: () => void; // Callback to re-fetch summary when data changes
  onPayNow: (due: Due) => void; // Callback to handle pay now action
  onBulkPay: (dues: Due[]) => void; // Callback to handle bulk pay action
}

const MyDuesTable: React.FC<MyDuesTableProps> = ({
  className,
  search,
  statusFilter,
  onDataChange,
  onPayNow,
  onBulkPay,
}) => {
  const [dues, setDues] = useState<Due[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedDues, setSelectedDues] = useState<Set<string>>(new Set());
  const [selectAll, setSelectAll] = useState(false);
  const [showRefillModal, setShowRefillModal] = useState(false);
  const [selectedDue, setSelectedDue] = useState<Due | null>(null);

  useEffect(() => {
    fetchDues();
  }, [search, statusFilter, currentPage]);

  useEffect(() => {
    // Reset selection when data changes
    setSelectedDues(new Set());
    setSelectAll(false);
  }, [dues]);

  const fetchDues = async () => {
    try {
      setLoading(true);
      const { data, totalPages: newTotalPages } = await getDues({
        search,
        status: statusFilter !== 'All' ? (statusFilter as Due['status']) : undefined,
        page: currentPage,
        limit: 10
      });
      setDues(data);
      setTotalPages(newTotalPages);
      setError(null);
    } catch (err) {
      setError('Failed to fetch dues');
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (newPage: number) => {
    if (newPage > 0 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  };

  const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    const checked = e.target.checked;
    setSelectAll(checked);
    
    if (checked) {
      // Only select dues that can be paid (Pending or Overdue)
      const payableDues = dues
        .filter(due => due.status === 'Pending' || due.status === 'Overdue')
        .map(due => due.id);
      setSelectedDues(new Set(payableDues));
    } else {
      setSelectedDues(new Set());
    }
  };

  const handleSelectDue = (id: string, checked: boolean) => {
    const newSelectedDues = new Set(selectedDues);
    
    if (checked) {
      newSelectedDues.add(id);
    } else {
      newSelectedDues.delete(id);
    }
    
    setSelectedDues(newSelectedDues);
    
    // Update selectAll state based on whether all payable dues are selected
    const payableDues = dues.filter(due => due.status === 'Pending' || due.status === 'Overdue');
    setSelectAll(payableDues.length > 0 && payableDues.every(due => newSelectedDues.has(due.id)));
  };

  const handleBulkPay = () => {
    const selectedDuesList = dues.filter(due => selectedDues.has(due.id));
    if (selectedDuesList.length > 0) {
      onBulkPay(selectedDuesList);
    }
  };

  const handleRefill = (due: Due) => {
    setSelectedDue(due);
    setShowRefillModal(true);
  };

  const handleRefillSuccess = () => {
    fetchDues();
    onDataChange();
  };

  return (
    <div className={className}>
      {loading && dues.length === 0 ? (
        <div className="flex justify-center items-center py-20">
          <PaymentLoader size="large" />
        </div>
      ) : error ? (
        <div className="text-center py-10">
          <p className="text-red-500">{error}</p>
          <button 
            onClick={fetchDues}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Try Again
          </button>
        </div>
      ) : dues.length === 0 ? (
        <EmptyState 
          title="No dues found"
          description="There are no dues matching your search criteria."
          icon={<CreditCard size={48} className="text-gray-400" />}
        />
      ) : (
        <>
          {selectedDues.size > 0 && (
            <div className="bg-blue-50 p-4 flex justify-between items-center mb-4">
              <p className="text-blue-700">
                {selectedDues.size} {selectedDues.size === 1 ? 'due' : 'dues'} selected
              </p>
              <button
                onClick={handleBulkPay}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Pay Selected
              </button>
            </div>
          )}
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <input 
                      type="checkbox" 
                      className="form-checkbox h-4 w-4 text-indigo-600 transition duration-150 ease-in-out"
                      checked={selectAll}
                      onChange={handleSelectAll}
                    />
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Email
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Contact
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Game
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Season
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Due Date
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {dues.map((due) => (
                  <MyDuesTableRow
                    key={due.id}
                    due={due}
                    onPayNow={() => onPayNow(due)}
                    onRefill={() => handleRefill(due)}
                    isSelected={selectedDues.has(due.id)}
                    onSelect={(checked) => handleSelectDue(due.id, checked)}
                  />
                ))}
              </tbody>
            </table>
          </div>
          
          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-between items-center px-4 py-3 bg-white border-t border-gray-200 sm:px-6">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                    currentPage === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  Previous
                </button>
                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className={`ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                    currentPage === totalPages ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  Next
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Showing page <span className="font-medium">{currentPage}</span> of{' '}
                    <span className="font-medium">{totalPages}</span>
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${
                        currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      <span className="sr-only">Previous</span>
                      <ChevronLeft className="h-5 w-5" aria-hidden="true" />
                    </button>
                    {/* Page numbers */}
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNum;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        pageNum = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = currentPage - 2 + i;
                      }
                      
                      return (
                        <button
                          key={pageNum}
                          onClick={() => handlePageChange(pageNum)}
                          className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                            currentPage === pageNum
                              ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                              : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                          }`}
                        >
                          {pageNum}
                        </button>
                      );
                    })}
                    <button
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${
                        currentPage === totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      <span className="sr-only">Next</span>
                      <ChevronRight className="h-5 w-5" aria-hidden="true" />
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </>
      )}
      
      {/* Amount Refill Modal */}
      {showRefillModal && selectedDue && (
        <AmountRefillModal
          isOpen={showRefillModal}
          onClose={() => setShowRefillModal(false)}
          due={selectedDue}
          onSuccess={handleRefillSuccess}
        />
      )}
    </div>
  );
};

export default MyDuesTable;
