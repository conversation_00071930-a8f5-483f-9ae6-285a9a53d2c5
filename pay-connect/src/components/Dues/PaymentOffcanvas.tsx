import React, { useState, useEffect, useRef } from 'react';
import { CheckCircle, XCircle, ArrowLeft, Wallet as WalletIcon, ArrowRight, Eye, EyeOff, RefreshCw, Building2 } from 'lucide-react';
import {
  Due,
  getDueById,
  payDueWithWallet,
  payDueEnhanced,
  checkPaymentStatus,
  createDueNotification,
  DueNotification
} from '../../services/dues';
import { getWallet, getUserProfile, Wallet, UserProfile } from '../../services/wallet';
import PaymentMethodSelector, { PaymentMethod } from '../Staff/PaymentMethodSelector';
import PaymentLoader from '../common/PaymentLoader';

interface PaymentOffcanvasProps {
  isOpen: boolean;
  onClose: () => void;
  due: Due | null;
  onPaymentSuccess: () => void;
}

type PaymentStep = 'details' | 'pin' | 'processing' | 'success' | 'failed';

// Helper function to get member details from due (using backend data)
const getMemberDetails = (due: Due | null) => {
  if (!due) return null;

  // Use backend data if available, otherwise fallback to individual fields
  if (due.member_details) {
    return {
      participationType: due.member_details.participation_type,
      email: due.member_details.email,
      contact: due.member_details.contact,
      avatarUrl: due.member_details.avatar_url,
      name: due.member_details.name,
    };
  }

  // Fallback to individual fields
  return {
    participationType: due.participation_type || 'Staff Member',
    email: due.email || 'N/A',
    contact: due.contact || 'N/A',
    avatarUrl: due.avatar_url || 'https://randomuser.me/api/portraits/men/32.jpg',
    name: due.full_name || `${due.firstname || ''} ${due.lastname || ''}`.trim() || 'N/A',
  };
};

// Helper function to get league info from due (using backend data)
const getLeagueInfo = (due: Due | null) => {
  if (!due) return null;

  // Use backend data if available, otherwise fallback to individual fields
  if (due.league_info) {
    return {
      logo: due.league_info.logo,
      name: due.league_info.name,
      matchDates: due.league_info.due_date ? new Date(due.league_info.due_date).toLocaleDateString() : 'N/A',
      organizer: due.league_info.organizer,
    };
  }

  // Fallback to individual fields
  return {
    logo: due.game_logo || 'https://cdn-icons-png.flaticon.com/512/3135/3135715.png',
    name: due.game_title || 'N/A',
    matchDates: due.due_date ? new Date(due.due_date).toLocaleDateString() : (due.end_date ? new Date(due.end_date).toLocaleDateString() : 'N/A'),
    organizer: due.organizer_name || 'Team Connect',
  };
};

// Helper function to safely parse numeric values
const safeParseNumber = (value: unknown): number => {
  if (typeof value === 'number') return value;
  if (typeof value === 'string') {
    const parsed = parseFloat(value);
    return isNaN(parsed) ? 0 : parsed;
  }
  return 0;
};

// Helper function to get payment breakdown from due (using backend data)
const getPaymentBreakdown = (due: Due | null) => {
  if (!due) return [];

  // Use backend data if available
  if (due.payment_breakdown && due.payment_breakdown.length > 0) {
    return due.payment_breakdown.map(item => ({
      label: item.label || 'Unknown',
      value: safeParseNumber(item.value)
    }));
  }

  // Fallback to calculated values if backend data not available
  const baseAmount = safeParseNumber(due.base_amount || due.amount || 0);
  const payconnectFee = safeParseNumber(due.payconnect_fee) || Math.round(baseAmount * 0.01 * 100) / 100;
  const tax = safeParseNumber(due.tax_amount) || Math.round(baseAmount * 0.0125 * 100) / 100;

  return [
    { label: `${due.game_title || 'Game'} - ${due.season_name || 'Season'}`, value: baseAmount },
    { label: 'Payconnect Charges', value: payconnectFee },
    { label: 'Tax', value: tax },
  ];
};

const PaymentOffcanvas: React.FC<PaymentOffcanvasProps> = ({
  isOpen,
  onClose,
  due,
  onPaymentSuccess,
}) => {
  const [currentStep, setCurrentStep] = useState<PaymentStep>('details');
  const [isLoading, setIsLoading] = useState(false);
  const [pin, setPin] = useState(['', '', '', '']);
  const [pinError, setPinError] = useState('');
  const pinInputs = [useRef(null), useRef(null), useRef(null), useRef(null)];
  const [wallet, setWallet] = useState<Wallet | null>(null);
  const [user, setUser] = useState<UserProfile | null>(null);
  const [errorMsg, setErrorMsg] = useState<string>('');
  const [transactionId, setTransactionId] = useState<string>('');
  const [showPin, setShowPin] = useState(false);
  const [statusCheckInterval, setStatusCheckInterval] = useState<number | null>(null);
  const [paymentStatus, setPaymentStatus] = useState<'pending' | 'completed' | 'failed' | null>(null);
  const [notification, setNotification] = useState<DueNotification | null>(null);
  const [isRefreshingWallet, setIsRefreshingWallet] = useState(false);
  const [freshDueData, setFreshDueData] = useState<Due | null>(null);
  const [isLoadingDueData, setIsLoadingDueData] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(false);

  // Payment method state (similar to staff payment)
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null);
  const [paymentSource, setPaymentSource] = useState<'wallet' | 'bank'>('wallet');
  const [selectedBankAccount, setSelectedBankAccount] = useState<any>(null); // For compatibility with PaymentMethodSelector

  // Use fresh due data if available, otherwise fallback to prop data
  const currentDue = freshDueData || due;

  // Check if all required data is loaded
  const isDataLoaded = !isInitialLoading && !isLoadingDueData && wallet && currentDue;

  // Get real data from due
  const memberDetails = getMemberDetails(currentDue);
  const leagueInfo = getLeagueInfo(currentDue);
  const paymentBreakdown = getPaymentBreakdown(currentDue);

  // Calculate total - prefer backend total_amount, fallback to breakdown calculation
  const breakdownTotal = safeParseNumber(currentDue?.total_amount) || paymentBreakdown.reduce((sum, item) => sum + safeParseNumber(item.value), 0);

  useEffect(() => {
    if (isOpen && due) {
      setCurrentStep('details');
      setPin(['', '', '', '']);
      setPinError('');
      setErrorMsg('');
      setTransactionId('');
      setIsRefreshingWallet(false);
      setFreshDueData(null);
      setIsInitialLoading(true);

      // Load all required data
      Promise.all([
        fetchWalletAndUser(),
        fetchFreshDueData()
      ]).finally(() => {
        setIsInitialLoading(false);
      });
    }
  }, [isOpen, due]);

  // Auto-refresh wallet balance every 30 seconds when offcanvas is open
  useEffect(() => {
    if (!isOpen) return;

    const interval = setInterval(() => {
      if (currentStep === 'details') {
        refreshWalletBalance();
      }
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [isOpen, currentStep]);

  const fetchWalletAndUser = async () => {
    try {
      const [w, u] = await Promise.all([getWallet(), getUserProfile()]);
      setWallet(w);
      setUser(u);
      console.log('✅ Wallet and user data loaded successfully');
    } catch (error) {
      console.error('❌ Failed to load wallet and user data:', error);
      setErrorMsg('Failed to load wallet information');
    }
  };

  const refreshWalletBalance = async () => {
    setIsRefreshingWallet(true);
    try {
      const updatedWallet = await getWallet();
      setWallet(updatedWallet);
      console.log('💰 Wallet balance refreshed:', updatedWallet.balance);
    } catch (error) {
      console.error('❌ Failed to refresh wallet balance:', error);
    } finally {
      setIsRefreshingWallet(false);
    }
  };

  const fetchFreshDueData = async () => {
    if (!due?.id) return;

    setIsLoadingDueData(true);
    try {
      console.log('🔄 Fetching fresh due data for ID:', due.id);
      const freshDue = await getDueById(due.id);
      setFreshDueData(freshDue);
      console.log('✅ Fresh due data loaded:', freshDue);
    } catch (error) {
      console.error('❌ Failed to fetch fresh due data:', error);
      // Keep using the original due data if fetch fails
    } finally {
      setIsLoadingDueData(false);
    }
  };

  // PIN handlers
  const handlePinChange = (idx: number, value: string) => {
    if (!/^[0-9]?$/.test(value)) return;
    const newPin = [...pin];
    newPin[idx] = value;
    setPin(newPin);
    setPinError('');
    if (value && idx < 3) {
      (pinInputs[idx + 1].current as any)?.focus();
    }
    if (!value && idx > 0) {
      (pinInputs[idx - 1].current as any)?.focus();
    }
  };
  const handlePinKeyDown = (idx: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Backspace' && !pin[idx] && idx > 0) {
      (pinInputs[idx - 1].current as any)?.focus();
    }
  };
  const handleContinueWithPin = async () => {
    if (pin.some(p => p === '')) {
      setPinError('Please enter your 4-digit PIN');
      return;
    }

    // PIN is now verified directly in the wallet payment process
    // No need for separate verification
    handleWalletPayment();
  };

  const handleWalletPayment = async () => {
    if (!currentDue || !wallet) return;

    // Validate PIN
    if (pin.some(digit => digit === '')) {
      setPinError('Please enter your complete PIN');
      return;
    }

    // Validate payment amount
    if (breakdownTotal <= 0) {
      setErrorMsg('Invalid payment amount');
      return;
    }

    setIsLoading(true);
    setErrorMsg('');
    setCurrentStep('processing');

    try {
      // Get the recipient user ID (organization or team admin)
      const recipientUserId = currentDue.user_id || '1'; // Default to admin user if not specified

      console.log('💰 Processing payment:', {
        dueId: currentDue.id,
        baseAmount: currentDue.amount,
        totalAmount: breakdownTotal,
        recipientUserId
      });

      // Use the enhanced payment system (similar to staff payments)
      const result = await payDueEnhanced({
        dueId: currentDue.id,
        amount: breakdownTotal, // Use total amount including fees and taxes
        description: `Payment for ${currentDue.game_title} - ${currentDue.season_name}`,
        pin: pin.join(''),
        paymentSource: paymentSource,
        bankAccountId: undefined, // Will be set if bank payment is selected
        paymentMethodId: selectedPaymentMethod?.id
      });

      if (result.success) {
        const txId = result.transactionId?.toString() || `txn_${Math.random().toString(36).substring(2, 11)}`;
        setTransactionId(txId);
        setCurrentStep('success');

        console.log('✅ Payment successful:', {
          transactionId: txId,
          amount: breakdownTotal,
          dueId: currentDue.id,
          newBalance: result.newBalance
        });

        // Update wallet balance if provided, otherwise refresh from server
        if (result.newBalance !== undefined && wallet) {
          console.log('💰 Updating wallet balance from API response:', result.newBalance);
          setWallet({
            ...wallet,
            balance: result.newBalance
          });
        } else {
          console.log('🔄 Refreshing wallet balance from server...');
          // Refresh wallet balance from server to get the latest balance
          refreshWalletBalance();
        }

        // Start real-time status monitoring
        startPaymentStatusMonitoring(txId);

        // Create success notification
        if (currentDue) {
          const successNotification = createDueNotification('payment_success', currentDue);
          setNotification(successNotification);
        }

        // Notify parent component after showing success state
        setTimeout(() => {
          console.log('🎉 Payment flow completed, notifying parent component');
          onPaymentSuccess();
          handleClose();
        }, 3000);
      } else {
        console.error('❌ Payment failed:', result.message);
        setErrorMsg(result.message || 'Payment failed. Please try again.');
        setCurrentStep('failed');
      }
    } catch (error) {
      console.error('❌ Payment error:', error);
      setErrorMsg('Payment failed. Please try again.');
      setCurrentStep('failed');
    } finally {
      setIsLoading(false);
    }
  };

  // Start payment status monitoring
  const startPaymentStatusMonitoring = (txId: string) => {
    if (!currentDue) return;

    console.log('🔄 Starting payment status monitoring for transaction:', txId);

    const interval = setInterval(async () => {
      try {
        const statusResult = await checkPaymentStatus(currentDue.id, txId);
        setPaymentStatus(statusResult.status);

        if (statusResult.status === 'completed') {
          console.log('✅ Payment confirmed as completed');
          const successNotification = createDueNotification('payment_success', currentDue);
          setNotification(successNotification);

          // Clear interval
          if (statusCheckInterval) {
            clearInterval(statusCheckInterval);
            setStatusCheckInterval(null);
          }
        } else if (statusResult.status === 'failed') {
          console.log('❌ Payment confirmed as failed');
          const failedNotification = createDueNotification('payment_failed', currentDue, statusResult.message);
          setNotification(failedNotification);
          setCurrentStep('failed');
          setErrorMsg(statusResult.message || 'Payment failed');

          // Clear interval
          if (statusCheckInterval) {
            clearInterval(statusCheckInterval);
            setStatusCheckInterval(null);
          }
        }
      } catch (error) {
        console.error('❌ Error checking payment status:', error);
      }
    }, 5000); // Check every 5 seconds

    setStatusCheckInterval(interval);

    // Auto-clear after 2 minutes
    setTimeout(() => {
      if (statusCheckInterval) {
        clearInterval(statusCheckInterval);
        setStatusCheckInterval(null);
      }
    }, 120000);
  };

  const handleClose = () => {
    // Clear any running intervals
    if (statusCheckInterval) {
      clearInterval(statusCheckInterval);
      setStatusCheckInterval(null);
    }

    setCurrentStep('details');
    setPin(['', '', '', '']);
    setPinError('');
    setErrorMsg('');
    setTransactionId('');
    setPaymentStatus(null);
    setNotification(null);
    onClose();
  };

  const getStepTitle = () => {
    switch (currentStep) {
      case 'details':
        return 'Pay from Wallet';
      case 'pin':
        return 'PIN Verification';
      case 'processing':
        return 'Processing Payment';
      case 'success':
        return 'Payment Successful';
      case 'failed':
        return 'Payment Failed';
      default:
        return 'Payment';
    }
  };

  if (!isOpen || !due) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      {/* Overlay */}
      <div className="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity duration-300 ease-in-out"></div>
      {/* Offcanvas Panel */}
      <div className="fixed inset-y-0 right-0 max-w-full flex transition-transform duration-300 ease-in-out translate-x-0">
        <div className="relative w-98 bg-white shadow-xl flex flex-col">
          {/* Header */}
          <div className="px-6 py-5 border-b flex items-center">
            {currentStep !== 'details' && (
              <button
                onClick={() => setCurrentStep('details')}
                className="mr-3 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <ArrowLeft size={20} />
              </button>
            )}
            <h2 className="text-lg font-medium text-gray-900 flex-1">
              {getStepTitle()}
            </h2>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <span className="sr-only">Close panel</span>
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          {/* Content */}
          <div className="flex-1 py-6 px-6 overflow-y-auto">
            {/* Initial Loading State */}
            {isInitialLoading && (
              <div className="flex flex-col items-center justify-center h-full space-y-4">
                <div className="text-center">
                  <PaymentLoader
                    type="processing"
                    message="Loading payment details..."
                    size="large"
                    showQuotes={true}
                  />
                </div>
                <div className="text-sm text-gray-600 text-center">
                  <p>Fetching wallet balance and payment information</p>
                  <p className="text-xs text-gray-500 mt-1">Please wait while we prepare your payment details</p>
                </div>
              </div>
            )}

            {/* Step 1: Wallet & Due Details */}
            {currentStep === 'details' && !isInitialLoading && (
              <div className="space-y-6">
                {/* Loading indicator for fresh due data */}
                {isLoadingDueData && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 flex items-center space-x-2">
                    <RefreshCw size={16} className="text-blue-600 animate-spin" />
                    <span className="text-sm text-blue-700">Loading latest payment details...</span>
                  </div>
                )}

                {/* Wallet Balance Card */}
                <div className="rounded-xl p-4 flex items-center justify-between mb-2 bg-gradient-to-r from-orange-400 to-orange-600 relative shadow-sm">
                  <div>
                    <div className="text-xs text-white font-medium mb-1 flex items-center space-x-2">
                      <span>Wallet Balance</span>
                      <button
                        onClick={refreshWalletBalance}
                        disabled={isRefreshingWallet}
                        className="text-white hover:text-orange-100 transition-colors disabled:opacity-50"
                        title="Refresh wallet balance"
                      >
                        <RefreshCw
                          size={14}
                          className={`${isRefreshingWallet ? 'animate-spin' : ''}`}
                        />
                      </button>
                    </div>
                    <div className="text-3xl font-bold text-white">${wallet?.balance?.toFixed(2) ?? '--'}</div>
                    <div className="text-xs text-orange-100 mt-1">
                      Total Wallet Balance
                      {isRefreshingWallet && <span className="ml-2">• Refreshing...</span>}
                    </div>
                  </div>
                  <div className="flex flex-col items-end">
                    <WalletIcon size={32} className="text-white mb-2" />
                    <div className="text-xs text-white opacity-80">Primary Bank<br /><span className="font-semibold">{wallet?.primaryBank ?? '--'}</span></div>
                  </div>
                </div>
                {/* Member Details Card */}
                <div className="bg-white border border-orange-200 rounded-xl p-4 flex items-center space-x-3">
                  <img src={memberDetails?.avatarUrl || 'https://randomuser.me/api/portraits/men/32.jpg'} alt={memberDetails?.name || 'User'} className="w-12 h-12 rounded-full object-cover border-2 border-orange-400" />
                  <div>
                    <div className="font-semibold text-gray-900">{memberDetails?.name || 'N/A'}</div>
                    <div className="text-xs text-gray-500">{memberDetails?.email || 'N/A'}</div>
                    <div className="text-xs text-gray-500">{memberDetails?.contact || 'N/A'}</div>
                    <div className="text-xs text-orange-700 font-semibold mt-1">{memberDetails?.participationType || 'Staff Member'}</div>
                  </div>
                </div>
                {/* League/Tournament Info Card */}
                <div className="bg-white border border-green-200 rounded-xl p-4 flex items-center space-x-3">
                  <img src={leagueInfo?.logo || 'https://cdn-icons-png.flaticon.com/512/3135/3135715.png'} alt={leagueInfo?.name || 'Game'} className="w-12 h-12 rounded-full object-cover border-2 border-green-400" />
                  <div>
                    <div className="font-semibold text-gray-900">{leagueInfo?.name || 'N/A'}</div>
                    <div className="text-xs text-gray-500">Due Date: {leagueInfo?.matchDates || 'N/A'}</div>
                    <div className="text-xs text-green-700 font-semibold">Organized by: {leagueInfo?.organizer || 'Team Connect'}</div>
                  </div>
                </div>
                {/* Payment Breakdown Card */}
                <div className="bg-blue-50 rounded-xl p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="font-semibold text-gray-900">Payment Breakdown:</div>
                    <button
                      onClick={fetchFreshDueData}
                      disabled={isLoadingDueData}
                      className="text-blue-600 hover:text-blue-700 transition-colors disabled:opacity-50"
                      title="Refresh payment details"
                    >
                      <RefreshCw
                        size={16}
                        className={`${isLoadingDueData ? 'animate-spin' : ''}`}
                      />
                    </button>
                  </div>
                  <div className="space-y-1 text-sm">
                    {paymentBreakdown.map((item, idx) => (
                      <div key={idx} className="flex justify-between">
                        <span>{item.label}</span>
                        <span>${item.value.toFixed(2)}</span>
                      </div>
                    ))}
                  </div>
                  <div className="border-t pt-2 mt-2 flex justify-between text-lg font-bold text-gray-900">
                    <span>Total:</span>
                    <span>${breakdownTotal.toFixed(2)}</span>
                  </div>
                </div>

                {/* Payment Method Selection */}
                <div className="bg-white border border-gray-200 rounded-xl p-4">
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Payment Source
                    </label>
                    <div className="space-y-2">
                      <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input
                          type="radio"
                          name="paymentSource"
                          value="wallet"
                          checked={paymentSource === 'wallet'}
                          onChange={(e) => setPaymentSource(e.target.value as 'wallet' | 'bank')}
                          className="mr-3"
                        />
                        <WalletIcon className="mr-3 text-blue-500" size={20} />
                        <div className="flex-1">
                          <div className="font-medium">Wallet</div>
                          <div className="text-sm text-gray-500">Balance: ${wallet?.balance?.toFixed(2) || '0.00'}</div>
                        </div>
                      </label>

                      <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input
                          type="radio"
                          name="paymentSource"
                          value="bank"
                          checked={paymentSource === 'bank'}
                          onChange={(e) => setPaymentSource(e.target.value as 'wallet' | 'bank')}
                          className="mr-3"
                        />
                        <Building2 className="mr-3 text-green-500" size={20} />
                        <div className="flex-1">
                          <div className="font-medium">Bank Transfer</div>
                          <div className="text-sm text-gray-500">Direct from bank account</div>
                        </div>
                      </label>
                    </div>
                  </div>

                  {/* Payment Method Selector (only show for bank transfers) */}
                  {paymentSource === 'bank' && (
                    <PaymentMethodSelector
                      selectedBankAccount={selectedBankAccount}
                      selectedPaymentMethod={selectedPaymentMethod}
                      onPaymentMethodChange={setSelectedPaymentMethod}
                      disabled={isLoading || isLoadingDueData}
                    />
                  )}
                </div>

                {/* Error */}
                {errorMsg && <div className="text-red-600 text-sm text-center">{errorMsg}</div>}
                {/* Pay Now Button */}
                <button
                  onClick={() => setCurrentStep('pin')}
                  disabled={
                    isLoading ||
                    isLoadingDueData ||
                    !isDataLoaded ||
                    (!!wallet && wallet.balance < breakdownTotal)
                  }
                  className="w-full bg-red-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-red-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2 text-base mt-2"
                >
                  {isLoading ? (
                    <div className="flex items-center space-x-2">
                      <PaymentLoader
                        type="processing"
                        size="small"
                        showQuotes={false}
                      />
                      <span>Processing...</span>
                    </div>
                  ) : isLoadingDueData ? (
                    <div className="flex items-center space-x-2">
                      <RefreshCw size={16} className="animate-spin" />
                      <span>Loading...</span>
                    </div>
                  ) : !isDataLoaded ? (
                    <span>Loading Payment Details...</span>
                  ) : (!!wallet && wallet.balance < breakdownTotal) ? (
                    <span>Insufficient Balance</span>
                  ) : (
                    <>
                      <span>Pay Now</span>
                      <ArrowRight size={20} />
                    </>
                  )}
                </button>
              </div>
            )}
            {/* Step 2: PIN Verification */}
            {currentStep === 'pin' && (
              <div className="space-y-8 max-w-xs mx-auto">
                <div>
                  <div className="font-semibold text-gray-900 text-base mb-1">Enter 4 Digit PIN</div>
                  <div className="text-xs text-gray-500 mb-6">For your security, please enter your transaction PIN to proceed. This step ensures your transaction is safe and secure.</div>
                  <div className="flex space-x-3 justify-center mb-2 relative">
                    {[0, 1, 2, 3].map((idx) => (
                      <input
                        key={idx}
                        ref={pinInputs[idx]}
                        type={showPin ? 'text' : 'password'}
                        inputMode="numeric"
                        maxLength={1}
                        className={`w-12 h-12 rounded-lg border text-center text-2xl font-mono bg-gray-50 focus:ring-2 focus:ring-red-400 focus:border-red-400 transition ${pinError ? 'border-red-400' : 'border-gray-300'}`}
                        value={pin[idx]}
                        onChange={e => handlePinChange(idx, e.target.value)}
                        onKeyDown={e => handlePinKeyDown(idx, e)}
                        autoFocus={idx === 0}
                      />
                    ))}
                    <button
                      type="button"
                      aria-label={showPin ? 'Hide PIN' : 'Show PIN'}
                      onClick={() => setShowPin((v) => !v)}
                      className="absolute right-[-40px] top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none"
                      tabIndex={-1}
                    >
                      {showPin ? <EyeOff size={22} /> : <Eye size={22} />}
                    </button>
                  </div>
                  {pinError && <div className="text-xs text-red-600 text-center mb-2">{pinError}</div>}
                  <div className="text-xs text-gray-700 text-center mt-2">
                    Forgot your PIN? <button className="font-bold text-black underline underline-offset-2 hover:text-red-600" type="button">Reset PIN</button>
                  </div>
                </div>
                <button
                  onClick={handleContinueWithPin}
                  disabled={isLoading || pin.some(p => p === '')}
                  className="w-full bg-red-500 text-white py-3 px-4 rounded-lg font-semibold hover:bg-red-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2 text-base"
                >
                  {isLoading ? (
                    <div className="flex items-center space-x-2">
                      <PaymentLoader
                        type="verification"
                        size="small"
                        showQuotes={false}
                      />
                      <span>Processing...</span>
                    </div>
                  ) : (
                    <>
                      <span>Continue</span>
                      <ArrowRight size={20} />
                    </>
                  )}
                </button>
              </div>
            )}
            {/* Step 3: Processing */}
            {currentStep === 'processing' && (
              <div className="space-y-6 text-center">
                <PaymentLoader
                  type="processing"
                  message="Please wait while we process your payment..."
                  size="large"
                  showQuotes={true}
                />
              </div>
            )}
            {/* Step 4: Success */}
            {currentStep === 'success' && (
              <div className="space-y-6 text-center">
                <div className="flex items-center justify-center">
                  <CheckCircle size={64} className="text-green-600" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">Payment Successful!</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Your payment of <span className="font-semibold text-green-600">${breakdownTotal.toFixed(2)}</span> has been processed successfully.
                  </p>

                  {/* Payment Details */}
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                    <div className="text-sm space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Game:</span>
                        <span className="font-medium">{currentDue?.game_title}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Season:</span>
                        <span className="font-medium">{currentDue?.season_name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Amount Paid:</span>
                        <span className="font-bold text-green-600">${breakdownTotal.toFixed(2)}</span>
                      </div>
                      {wallet && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Remaining Balance:</span>
                          <span className="font-medium">${wallet.balance.toFixed(2)}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {transactionId && (
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                      <p className="text-xs text-gray-500 mb-1">Transaction ID:</p>
                      <p className="text-sm font-mono text-gray-700">{transactionId}</p>
                    </div>
                  )}

                  <p className="text-xs text-gray-500 mt-4">
                    This window will close automatically in a few seconds...
                  </p>
                </div>
              </div>
            )}
            {/* Step 5: Failed */}
            {currentStep === 'failed' && (
              <div className="space-y-6 text-center">
                <div className="flex items-center justify-center">
                  <XCircle size={64} className="text-red-600" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">Payment Failed</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    {errorMsg || 'An error occurred while processing your payment. Please try again.'}
                  </p>

                  {/* Error Details */}
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                    <div className="text-sm space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Attempted Amount:</span>
                        <span className="font-medium">${breakdownTotal.toFixed(2)}</span>
                      </div>
                      {wallet && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Wallet Balance:</span>
                          <span className="font-medium">${wallet.balance.toFixed(2)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <button
                    onClick={() => {
                      setCurrentStep('details');
                      setErrorMsg('');
                      setPinError('');
                      setPin(['', '', '', '']);
                    }}
                    className="w-full bg-red-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-red-700 transition-colors"
                  >
                    Try Again
                  </button>

                  <button
                    onClick={handleClose}
                    className="w-full bg-gray-300 text-gray-700 py-3 px-4 rounded-lg font-semibold hover:bg-gray-400 transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentOffcanvas; 