import React from 'react';
import { DollarSign, CheckCircle, Clock, XCircle } from 'lucide-react';

interface SummaryCardProps {
  title: string;
  value: number;
  className?: string;
  onClick?: () => void;
  selected?: boolean;
}

const SummaryCard: React.FC<SummaryCardProps> = ({ title, value, className, onClick, selected }) => {
 const getIcon = () => {
    const iconProps = {
      size: 24,
      className: `transition-colors ${selected ? 'text-blue-600' : 'text-gray-500'}`
    };
    switch (title) {
      case 'Total Dues':
        return <DollarSign {...iconProps} />;
      case 'Paid Dues':
        return <CheckCircle {...iconProps}  />;
      case 'Pending Dues':
        return <Clock {...iconProps}  />;
      case 'Overdue Dues':
        return <XCircle {...iconProps} />;
      default:
        return <DollarSign {...iconProps}  />;
    }
  };

  return (
    <div
      className={`
      p-6 rounded-[20px] shadow-sm border transition-all duration-200 cursor-pointer flex justify-between items-center h[120px] 
    ${className}
    ${selected ? 'border-blue-300 bg-blue-50 shadow-md transform scale-105' : 'border-gray-200 bg-white hover:shadow-md hover:border-gray-300'}
  `}
      onClick={onClick}
    >
      <div>
         <h3 className={`${selected ? 'text-blue-700' : 'text-gray-600'}`}>
          {title}
        </h3>
        <p className={`text-2xl font-bold ${selected ? 'text-blue-900' : 'text-gray-900'}`}>
          {value}
        </p>
       
      </div>

      <div className={`w-12 h-12 rounded-full flex items-center justify-center border   ${selected ? 'bg-white border-blue-500' : 'bg-gray-100 border-white'}`}>
        {getIcon()}
      </div>
    </div>



  );
};

export default SummaryCard; 