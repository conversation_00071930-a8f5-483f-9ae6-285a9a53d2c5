import React, { useState, useEffect } from 'react';
import { X, CreditCard, Building, Wallet, CheckCircle, XCircle, Plus } from 'lucide-react';
import { Due } from '../../services/dues';
import { 
  PaymentMethod, 
  PaymentRequest, 
  PaymentResponse, 
  getPaymentMethods, 
  processPayment,
  addPaymentMethod 
} from '../../services/paymentProcessing';
import PaymentLoader from '../common/PaymentLoader';

interface BulkPaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedDues: Due[];
  onPaymentSuccess: () => void;
}

const BulkPaymentModal: React.FC<BulkPaymentModalProps> = ({
  isOpen,
  onClose,
  selectedDues,
  onPaymentSuccess,
}) => {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState<'idle' | 'processing' | 'success' | 'failed'>('idle');
  const [paymentResults, setPaymentResults] = useState<Array<{ due: Due; result: PaymentResponse }>>([]);
  const [showAddPaymentMethod, setShowAddPaymentMethod] = useState(false);
  const [newPaymentMethod, setNewPaymentMethod] = useState({
    type: 'card' as 'card' | 'bank' | 'wallet',
    name: '',
    last4: '',
    brand: '',
  });

  const totalAmount = selectedDues.reduce((sum, due) => sum + due.amount, 0);

  useEffect(() => {
    if (isOpen && selectedDues.length > 0) {
      loadPaymentMethods();
    }
  }, [isOpen, selectedDues]);

  useEffect(() => {
    if (paymentMethods.length > 0 && !selectedPaymentMethod) {
      const defaultMethod = paymentMethods.find(method => method.isDefault);
      setSelectedPaymentMethod(defaultMethod?.id || paymentMethods[0].id);
    }
  }, [paymentMethods, selectedPaymentMethod]);

  const loadPaymentMethods = async () => {
    try {
      const methods = await getPaymentMethods();
      setPaymentMethods(methods);
    } catch (error) {
      console.error('Failed to load payment methods:', error);
    }
  };

  const handleBulkPayment = async () => {
    if (!selectedPaymentMethod || selectedDues.length === 0) return;

    setIsLoading(true);
    setPaymentStatus('processing');
    setPaymentResults([]);

    const results: Array<{ due: Due; result: PaymentResponse }> = [];

    try {
      // Process payments sequentially to avoid overwhelming the system
      for (const due of selectedDues) {
        const paymentRequest: PaymentRequest = {
          dueId: due.id,
          amount: due.amount,
          paymentMethodId: selectedPaymentMethod,
          description: `Payment for ${due.league} - ${due.season}`,
        };

        const result = await processPayment(paymentRequest);
        results.push({ due, result });
        setPaymentResults([...results]); // Update results in real-time
      }

      const allSuccessful = results.every(r => r.result.status === 'completed');
      
      if (allSuccessful) {
        setPaymentStatus('success');
        setTimeout(() => {
          onPaymentSuccess();
          handleClose();
        }, 2000);
      } else {
        setPaymentStatus('failed');
      }
    } catch (error) {
      console.error('Bulk payment failed:', error);
      setPaymentStatus('failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddPaymentMethod = async () => {
    if (!newPaymentMethod.name || !newPaymentMethod.last4) return;

    try {
      const method = await addPaymentMethod({
        ...newPaymentMethod,
        isDefault: false,
      });
      
      setPaymentMethods(prev => [...prev, method]);
      setSelectedPaymentMethod(method.id);
      setShowAddPaymentMethod(false);
      setNewPaymentMethod({ type: 'card', name: '', last4: '', brand: '' });
    } catch (error) {
      console.error('Failed to add payment method:', error);
    }
  };

  const handleClose = () => {
    setPaymentStatus('idle');
    setPaymentResults([]);
    setShowAddPaymentMethod(false);
    setNewPaymentMethod({ type: 'card', name: '', last4: '', brand: '' });
    onClose();
  };

  const getPaymentMethodIcon = (type: PaymentMethod['type']) => {
    switch (type) {
      case 'card':
        return <CreditCard size={20} className="text-blue-600" />;
      case 'bank':
        return <Building size={20} className="text-green-600" />;
      case 'wallet':
        return <Wallet size={20} className="text-purple-600" />;
      default:
        return <CreditCard size={20} className="text-gray-600" />;
    }
  };

  if (!isOpen || selectedDues.length === 0) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-bold text-gray-900">Bulk Payment</h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Selected Dues Summary */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 mb-3">Selected Dues ({selectedDues.length})</h3>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {selectedDues.map((due) => (
                <div key={due.id} className="flex justify-between items-center text-sm">
                  <div>
                    <p className="font-medium text-gray-900">{due.league} - {due.season}</p>
                    <p className="text-gray-600">{due.payerName}</p>
                  </div>
                  <p className="font-bold text-gray-900">${due.amount.toFixed(2)}</p>
                </div>
              ))}
            </div>
            <div className="border-t pt-3 mt-3">
              <p className="text-lg font-bold text-gray-900">
                Total Amount: <span className="text-red-600">${totalAmount.toFixed(2)}</span>
              </p>
            </div>
          </div>

          {/* Payment Status */}
          {paymentStatus === 'processing' && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <div className="text-center">
                <PaymentLoader
                  type="processing"
                  message={`Processing ${paymentResults.length} of ${selectedDues.length} payments...`}
                  size="medium"
                  showQuotes={true}
                />
              </div>
              {/* Show individual payment results */}
              {paymentResults.length > 0 && (
                <div className="mt-3 space-y-2">
                  {paymentResults.map(({ due, result }) => (
                    <div key={due.id} className="flex items-center justify-between text-sm">
                      <span className="text-gray-700">{due.league}</span>
                      <span className={result.status === 'completed' ? 'text-green-600' : 'text-red-600'}>
                        {result.status === 'completed' ? '✓' : '✗'}
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {paymentStatus === 'success' && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <CheckCircle size={20} className="text-green-600" />
                <div>
                  <p className="font-medium text-green-900">All Payments Successful!</p>
                  <p className="text-sm text-green-700">
                    Successfully processed {selectedDues.length} payments.
                  </p>
                </div>
              </div>
            </div>
          )}

          {paymentStatus === 'failed' && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <XCircle size={20} className="text-red-600" />
                <div>
                  <p className="font-medium text-red-900">Some Payments Failed</p>
                  <p className="text-sm text-red-700">
                    {paymentResults.filter(r => r.result.status === 'failed').length} of {selectedDues.length} payments failed.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Payment Methods */}
          {paymentStatus === 'idle' && (
            <>
              <div>
                <h3 className="font-semibold text-gray-900 mb-3">Select Payment Method</h3>
                <div className="space-y-2">
                  {paymentMethods.map((method) => (
                    <label
                      key={method.id}
                      className={`flex items-center space-x-3 p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedPaymentMethod === method.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <input
                        type="radio"
                        name="paymentMethod"
                        value={method.id}
                        checked={selectedPaymentMethod === method.id}
                        onChange={(e) => setSelectedPaymentMethod(e.target.value)}
                        className="text-blue-600"
                      />
                      {getPaymentMethodIcon(method.type)}
                      <div className="flex-1">
                        <p className="font-medium text-gray-900">{method.name}</p>
                        {method.brand && (
                          <p className="text-sm text-gray-500 capitalize">{method.brand}</p>
                        )}
                      </div>
                    </label>
                  ))}
                </div>

                {/* Add New Payment Method */}
                {!showAddPaymentMethod ? (
                  <button
                    onClick={() => setShowAddPaymentMethod(true)}
                    className="flex items-center space-x-2 text-blue-600 hover:text-blue-700 mt-3"
                  >
                    <Plus size={16} />
                    <span>Add New Payment Method</span>
                  </button>
                ) : (
                  <div className="mt-4 p-4 border border-gray-200 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-3">Add Payment Method</h4>
                    <div className="space-y-3">
                      <select
                        value={newPaymentMethod.type}
                        onChange={(e) => setNewPaymentMethod(prev => ({ ...prev, type: e.target.value as any }))}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      >
                        <option value="card">Credit/Debit Card</option>
                        <option value="bank">Bank Account</option>
                        <option value="wallet">Digital Wallet</option>
                      </select>
                      <input
                        type="text"
                        placeholder="Card/Bank Name"
                        value={newPaymentMethod.name}
                        onChange={(e) => setNewPaymentMethod(prev => ({ ...prev, name: e.target.value }))}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      />
                      <input
                        type="text"
                        placeholder="Last 4 digits"
                        value={newPaymentMethod.last4}
                        onChange={(e) => setNewPaymentMethod(prev => ({ ...prev, last4: e.target.value }))}
                        className="w-full p-2 border border-gray-300 rounded-md"
                        maxLength={4}
                      />
                      {newPaymentMethod.type === 'card' && (
                        <input
                          type="text"
                          placeholder="Card Brand (Visa, Mastercard, etc.)"
                          value={newPaymentMethod.brand}
                          onChange={(e) => setNewPaymentMethod(prev => ({ ...prev, brand: e.target.value }))}
                          className="w-full p-2 border border-gray-300 rounded-md"
                        />
                      )}
                      <div className="flex space-x-2">
                        <button
                          onClick={handleAddPaymentMethod}
                          className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
                        >
                          Add
                        </button>
                        <button
                          onClick={() => setShowAddPaymentMethod(false)}
                          className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Payment Button */}
              <button
                onClick={handleBulkPayment}
                disabled={!selectedPaymentMethod || isLoading}
                className="w-full bg-red-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-red-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              >
                {isLoading ? (
                  <div className="flex items-center justify-center space-x-2">
                    <PaymentLoader type="processing" size="small" showQuotes={false} />
                    <span>Processing...</span>
                  </div>
                ) : (
                  `Pay All (${selectedDues.length} dues) - $${totalAmount.toFixed(2)}`
                )}
              </button>
            </>
          )}

          {/* Close Button for Success/Failed States */}
          {(paymentStatus === 'success' || paymentStatus === 'failed') && (
            <button
              onClick={handleClose}
              className="w-full bg-gray-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-gray-700 transition-colors"
            >
              Close
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default BulkPaymentModal; 