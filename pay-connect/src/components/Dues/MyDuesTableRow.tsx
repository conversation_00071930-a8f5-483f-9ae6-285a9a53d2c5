import React from 'react';
import { Due } from '../../services/dues';
import { Eye, CreditCard, Plus, History, ExternalLink } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface MyDuesTableRowProps {
  due: Due;
  onPayNow: () => void;
  onRefill: () => void;
  isSelected?: boolean;
  onSelect?: (checked: boolean) => void;
}

const MyDuesTableRow: React.FC<MyDuesTableRowProps> = ({ 
  due, 
  onPayNow, 
  onRefill,
  isSelected = false, 
  onSelect 
}) => {
  const navigate = useNavigate();

  const getStatusColor = (status: Due['status']) => {
    switch (status) {
      case 'Paid':
        return 'bg-green-100 text-green-800';
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'Overdue':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const isOverdue = due.status === 'Overdue';
  const canPay = due.status === 'Pending' || due.status === 'Overdue';
  const canSelect = due.status === 'Pending' || due.status === 'Overdue';
  const canRefill = due.status === 'Pending' || due.status === 'Overdue';

  const handleViewDetails = () => {
    navigate(`/dues/${due.id}`);
  };

  const handleSelectChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onSelect) {
      onSelect(e.target.checked);
    }
  };

  // Format the display name
  const displayName = due.full_name || `${due.firstname} ${due.lastname || ''}`.trim();
  
  // Format the avatar URL
  const avatarUrl = due.avatar_url || due.avatarUrl || `https://ui-avatars.com/api/?name=${encodeURIComponent(displayName)}`;

  // Format the amount - ensure it's a number
  const displayAmount = parseFloat(due.total_amount?.toString() || due.amount?.toString() || '0') || 0;

  // Format the due date
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  const dueDate = due.due_date || due.end_date;

  return (
    <tr className="hover:bg-gray-50">
      <td className="px-6 py-4 whitespace-nowrap">
        {canSelect ? (
          <input 
            type="checkbox" 
            className="form-checkbox h-4 w-4 text-indigo-600 transition duration-150 ease-in-out"
            checked={isSelected}
            onChange={handleSelectChange}
          />
        ) : (
          <div className="w-4 h-4"></div>
        )}
      </td>
      
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            <img 
              className="h-10 w-10 rounded-full" 
              src={avatarUrl} 
              alt={displayName}
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(displayName)}`;
              }}
            />
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">
              <button 
                onClick={handleViewDetails}
                className="hover:text-blue-600 flex items-center"
              >
                {displayName}
                <ExternalLink size={14} className="ml-1 text-gray-400" />
              </button>
            </div>
          </div>
        </div>
      </td>
      
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {due.email}
      </td>
      
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {due.contact || '-'}
      </td>
      
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {due.game_title}
      </td>
      
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {due.season_name}
      </td>
      
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
        ${displayAmount.toFixed(2)}
      </td>
      
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {dueDate ? formatDate(dueDate) : '-'}
      </td>
      
      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(due.status)}`}>
          {due.status}
          {isOverdue && ' ⚠️'}
        </span>
      </td>
      
      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <div className="flex items-center justify-end space-x-2">
          {canRefill && (
            <button
              onClick={onRefill}
              className="text-blue-500 hover:text-blue-700 p-2 rounded-full bg-blue-50 hover:bg-blue-100 transition"
              title="Add Money to Due"
            >
              <Plus size={18} />
            </button>
          )}
          
          {canPay && (
            <button
              onClick={onPayNow}
              className="text-green-500 hover:text-green-700 p-2 rounded-full bg-green-50 hover:bg-green-100 transition"
              title="Pay Now"
            >
              <CreditCard size={18} />
            </button>
          )}
          
          <button
            onClick={handleViewDetails}
            className="text-purple-500 hover:text-purple-700 p-2 rounded-full bg-purple-50 hover:bg-purple-100 transition"
            title="View Details"
          >
            <Eye size={18} />
          </button>
          
          <button
            onClick={handleViewDetails}
            className="text-gray-500 hover:text-gray-700 p-2 rounded-full bg-gray-50 hover:bg-gray-100 transition"
            title="Transaction History"
          >
            <History size={18} />
          </button>
        </div>
      </td>
    </tr>
  );
};

export default MyDuesTableRow;
