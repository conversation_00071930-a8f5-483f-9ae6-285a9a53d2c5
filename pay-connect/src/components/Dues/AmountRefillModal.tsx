import React, { useState, useEffect } from 'react';
import { X, DollarSign, Lock, Eye, EyeOff, ArrowRight, CheckCircle, AlertCircle, Wallet, Building2 } from 'lucide-react';
import { Due, refillDueAmount } from '../../services/dues';
import { getWallet } from '../../services/wallet';
import PaymentLoader from '../common/PaymentLoader';

interface AmountRefillModalProps {
  isOpen: boolean;
  onClose: () => void;
  due: Due;
  onSuccess: () => void;
}

type RefillStep = 'details' | 'confirmation' | 'processing' | 'success' | 'error';
type PaymentMethod = 'wallet' | 'bank';

const AmountRefillModal: React.FC<AmountRefillModalProps> = ({
  isOpen,
  onClose,
  due,
  onSuccess
}) => {
  const [step, setStep] = useState<RefillStep>('details');
  const [amount, setAmount] = useState('');
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('wallet');
  const [pin, setPin] = useState('');
  const [showPin, setShowPin] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [walletBalance, setWalletBalance] = useState(0);
  const [transactionId, setTransactionId] = useState<string | null>(null);

  const quickAmounts = [10, 25, 50, 100, 200];

  useEffect(() => {
    if (isOpen) {
      fetchWalletBalance();
      resetForm();
    }
  }, [isOpen]);

  const fetchWalletBalance = async () => {
    try {
      const wallet = await getWallet();
      setWalletBalance(wallet.balance);
    } catch (error) {
      console.error('Error fetching wallet balance:', error);
      setWalletBalance(0);
    }
  };

  const resetForm = () => {
    setStep('details');
    setAmount('');
    setPaymentMethod('wallet');
    setPin('');
    setShowPin(false);
    setError(null);
    setLoading(false);
    setTransactionId(null);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Allow only numbers and decimal point
    if (/^\d*\.?\d*$/.test(value)) {
      setAmount(value);
      setError(null);
    }
  };

  const handleQuickAmount = (quickAmount: number) => {
    setAmount(quickAmount.toString());
    setError(null);
  };

  const validateForm = () => {
    const amountNum = parseFloat(amount);
    
    if (!amount || amountNum <= 0) {
      setError('Please enter a valid amount');
      return false;
    }

    if (amountNum < 1) {
      setError('Minimum refill amount is $1.00');
      return false;
    }

    if (amountNum > 10000) {
      setError('Maximum refill amount is $10,000.00');
      return false;
    }

    if (paymentMethod === 'wallet' && amountNum > walletBalance) {
      setError('Insufficient wallet balance');
      return false;
    }

    if (!pin) {
      setError('Please enter your PIN');
      return false;
    }

    if (pin.length !== 4) {
      setError('PIN must be 4 digits');
      return false;
    }

    return true;
  };

  const handleContinue = () => {
    if (validateForm()) {
      setStep('confirmation');
    }
  };

  const handleConfirmRefill = async () => {
    setStep('processing');
    setLoading(true);
    setError(null);

    try {
      const result = await refillDueAmount({
        dueId: due.id,
        amount: parseFloat(amount),
        paymentMethod,
        pin
      });

      if (result.success) {
        setTransactionId(result.transactionId || `refill_${Math.random().toString(36).substring(2, 11)}`);
        setStep('success');

        // Call onSuccess to refresh data
        onSuccess();
      } else {
        setError(result.message || 'Refill failed. Please try again.');
        setStep('error');
      }
    } catch (err) {
      setError('Refill failed. Please try again.');
      setStep('error');
    } finally {
      setLoading(false);
    }
  };

  const handleBackToDetails = () => {
    setStep('details');
    setError(null);
  };

  const handleTryAgain = () => {
    setStep('details');
    setError(null);
    setPin(''); // Clear PIN for security
  };

  if (!isOpen) return null;

  const displayName = due.full_name || `${due.firstname} ${due.lastname || ''}`.trim();
  const currentAmount = due.total_amount || due.amount || 0;
  const refillAmount = parseFloat(amount) || 0;
  const newAmount = currentAmount + refillAmount;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h3 className="text-lg font-semibold text-gray-900">
            Add Money to Due
          </h3>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {step === 'details' && (
            <div className="space-y-6">
              {/* Due Information */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">Due Details</h4>
                <div className="space-y-1 text-sm text-gray-600">
                  <p><span className="font-medium">Member:</span> {displayName}</p>
                  <p><span className="font-medium">Game:</span> {due.game_title}</p>
                  <p><span className="font-medium">Season:</span> {due.season_name}</p>
                  <p><span className="font-medium">Current Amount:</span> ${currentAmount.toFixed(2)}</p>
                </div>
              </div>

              {/* Amount Input */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Refill Amount
                </label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="text"
                    value={amount}
                    onChange={handleAmountChange}
                    placeholder="0.00"
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                {/* Quick Amount Buttons */}
                <div className="flex flex-wrap gap-2 mt-3">
                  {quickAmounts.map((quickAmount) => (
                    <button
                      key={quickAmount}
                      onClick={() => handleQuickAmount(quickAmount)}
                      className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
                    >
                      ${quickAmount}
                    </button>
                  ))}
                </div>
              </div>

              {/* Payment Method */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Payment Method
                </label>
                <div className="space-y-2">
                  <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="wallet"
                      checked={paymentMethod === 'wallet'}
                      onChange={(e) => setPaymentMethod(e.target.value as PaymentMethod)}
                      className="mr-3"
                    />
                    <Wallet className="mr-3 text-blue-500" size={20} />
                    <div className="flex-1">
                      <div className="font-medium">Wallet</div>
                      <div className="text-sm text-gray-500">Balance: ${walletBalance.toFixed(2)}</div>
                    </div>
                  </label>
                  
                  <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="bank"
                      checked={paymentMethod === 'bank'}
                      onChange={(e) => setPaymentMethod(e.target.value as PaymentMethod)}
                      className="mr-3"
                    />
                    <Building2 className="mr-3 text-green-500" size={20} />
                    <div className="flex-1">
                      <div className="font-medium">Bank Transfer</div>
                      <div className="text-sm text-gray-500">Direct from bank account</div>
                    </div>
                  </label>
                </div>
              </div>

              {/* PIN Input */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Enter PIN
                </label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type={showPin ? 'text' : 'password'}
                    value={pin}
                    onChange={(e) => setPin(e.target.value.replace(/\D/g, '').slice(0, 4))}
                    placeholder="Enter 4-digit PIN"
                    className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    maxLength={4}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPin(!showPin)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPin ? <EyeOff size={20} /> : <Eye size={20} />}
                  </button>
                </div>
              </div>

              {error && (
                <div className="flex items-center space-x-2 text-red-600 bg-red-50 p-3 rounded-lg">
                  <AlertCircle size={20} />
                  <span className="text-sm">{error}</span>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex space-x-3">
                <button
                  onClick={handleClose}
                  className="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleContinue}
                  className="flex-1 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center justify-center"
                >
                  Continue
                  <ArrowRight className="ml-2" size={16} />
                </button>
              </div>
            </div>
          )}

          {step === 'confirmation' && (
            <div className="space-y-6">
              <div className="text-center">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">
                  Confirm Refill
                </h4>
              </div>

              {/* Refill Summary */}
              <div className="bg-gray-50 p-4 rounded-lg space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Member:</span>
                  <span className="font-medium">{displayName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Current Amount:</span>
                  <span className="font-medium">${currentAmount.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Refill Amount:</span>
                  <span className="font-medium text-blue-600">+${refillAmount.toFixed(2)}</span>
                </div>
                <div className="border-t pt-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">New Amount:</span>
                    <span className="font-semibold text-lg">${newAmount.toFixed(2)}</span>
                  </div>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Payment Method:</span>
                  <span className="font-medium capitalize">{paymentMethod}</span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-3">
                <button
                  onClick={handleBackToDetails}
                  className="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                >
                  Back
                </button>
                <button
                  onClick={handleConfirmRefill}
                  className="flex-1 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  Confirm Refill
                </button>
              </div>
            </div>
          )}

          {step === 'processing' && (
            <div className="text-center py-8">
              <PaymentLoader size="large" />
              <p className="mt-4 text-gray-600">Processing refill...</p>
            </div>
          )}

          {step === 'success' && (
            <div className="text-center space-y-6">
              <div className="flex justify-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
              </div>
              
              <div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">
                  Refill Successful!
                </h4>
                <p className="text-gray-600">
                  ${refillAmount.toFixed(2)} has been added to the due amount.
                </p>
                {transactionId && (
                  <p className="text-sm text-gray-500 mt-2">
                    Transaction ID: {transactionId}
                  </p>
                )}
              </div>

              <button
                onClick={handleClose}
                className="w-full px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700"
              >
                Done
              </button>
            </div>
          )}

          {step === 'error' && (
            <div className="text-center space-y-6">
              <div className="flex justify-center">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                  <AlertCircle className="w-8 h-8 text-red-600" />
                </div>
              </div>
              
              <div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">
                  Refill Failed
                </h4>
                <p className="text-gray-600">
                  {error || 'Something went wrong. Please try again.'}
                </p>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={handleClose}
                  className="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleTryAgain}
                  className="flex-1 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  Try Again
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AmountRefillModal;
