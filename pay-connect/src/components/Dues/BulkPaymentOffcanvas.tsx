import React, { useState, useEffect, useRef } from 'react';
import { CreditCard, Building, Wallet, CheckCircle, XCircle, Loader2, Plus, ArrowLeft, Eye, EyeOff, RefreshCw } from 'lucide-react';
import { Due, payMultipleDuesWithWallet } from '../../services/dues';
import { getWallet, Wallet as WalletType } from '../../services/wallet';
import PaymentLoader from '../common/PaymentLoader';

interface BulkPaymentOffcanvasProps {
  isOpen: boolean;
  onClose: () => void;
  selectedDues: Due[];
  onPaymentSuccess: () => void;
}

type PaymentStep = 'details' | 'pin' | 'processing' | 'success' | 'failed';

interface BulkPaymentResult {
  dueId: string;
  success: boolean;
  message: string;
  transactionId?: string;
}

const BulkPaymentOffcanvas: React.FC<BulkPaymentOffcanvasProps> = ({
  isOpen,
  onClose,
  selectedDues,
  onPaymentSuccess,
}) => {
  const [currentStep, setCurrentStep] = useState<PaymentStep>('details');
  const [isLoading, setIsLoading] = useState(false);
  const [paymentResults, setPaymentResults] = useState<BulkPaymentResult[]>([]);
  const [wallet, setWallet] = useState<WalletType | null>(null);
  const [pin, setPin] = useState(['', '', '', '']);
  const [pinError, setPinError] = useState('');
  const [showPin, setShowPin] = useState(false);
  const [errorMsg, setErrorMsg] = useState('');
  const [successfulPayments, setSuccessfulPayments] = useState(0);
  const [failedPayments, setFailedPayments] = useState(0);
  const [isRefreshingWallet, setIsRefreshingWallet] = useState(false);

  const pinInputs = [useRef(null), useRef(null), useRef(null), useRef(null)];

  const totalAmount = selectedDues.reduce((sum, due) => sum + due.amount, 0);

  useEffect(() => {
    if (isOpen && selectedDues.length > 0) {
      loadWalletInfo();
      setCurrentStep('details');
      resetState();
    }
  }, [isOpen, selectedDues]);

  // Auto-refresh wallet balance every 30 seconds when offcanvas is open
  useEffect(() => {
    if (!isOpen) return;

    const interval = setInterval(() => {
      if (currentStep === 'details') {
        refreshWalletBalance();
      }
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [isOpen, currentStep]);

  const loadWalletInfo = async () => {
    try {
      const walletInfo = await getWallet();
      setWallet(walletInfo);
    } catch (error) {
      console.error('Failed to load wallet info:', error);
      setErrorMsg('Failed to load wallet information');
    }
  };

  const refreshWalletBalance = async () => {
    setIsRefreshingWallet(true);
    try {
      const updatedWallet = await getWallet();
      setWallet(updatedWallet);
      console.log('💰 Bulk payment wallet balance refreshed:', updatedWallet.balance);
    } catch (error) {
      console.error('❌ Failed to refresh wallet balance:', error);
    } finally {
      setIsRefreshingWallet(false);
    }
  };

  const resetState = () => {
    setPaymentResults([]);
    setPin(['', '', '', '']);
    setPinError('');
    setErrorMsg('');
    setSuccessfulPayments(0);
    setFailedPayments(0);
  };

  const handleNextStep = () => {
    if (currentStep === 'details') {
      // Check if wallet has sufficient balance
      if (wallet && wallet.balance < totalAmount) {
        setErrorMsg(`Insufficient wallet balance. You need $${totalAmount.toFixed(2)} but have $${wallet.balance.toFixed(2)}`);
        return;
      }

      setCurrentStep('pin');
      setPinError('');
    }
  };

  const handleBackStep = () => {
    if (currentStep === 'pin') {
      setCurrentStep('details');
      setPinError('');
    }
  };

  const handleSendOTP = async () => {
    if (!selectedPaymentMethod || selectedDues.length === 0) return;

    setIsLoading(true);
    try {
      // Use the first due's contact info for OTP
      const firstDue = selectedDues[0];
      const response = await sendOTP({
        paymentId: `pay_${Math.random().toString(36).substr(2, 9)}`,
        phoneNumber: firstDue.contact,
        email: firstDue.email,
      });

      if (response.success && response.otpId) {
        setOtpId(response.otpId);
        setCurrentStep('otp');
        setOtpError('');
      } else {
        setOtpError('Failed to send OTP. Please try again.');
      }
    } catch (error) {
      console.error('Failed to send OTP:', error);
      setOtpError('Failed to send OTP. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // PIN input handling
  const handlePinChange = (value: string, index: number) => {
    if (value.length > 1) return;

    const newPin = [...pin];
    newPin[index] = value;
    setPin(newPin);

    // Auto-focus next input
    if (value && index < 3) {
      (pinInputs[index + 1].current as any)?.focus();
    }

    setPinError('');
  };

  const handlePinKeyDown = (e: React.KeyboardEvent, idx: number) => {
    if (e.key === 'Backspace' && !pin[idx] && idx > 0) {
      (pinInputs[idx - 1].current as any)?.focus();
    }
  };

  const handleContinueWithPin = async () => {
    if (pin.some(p => p === '')) {
      setPinError('Please enter your 4-digit PIN');
      return;
    }

    setIsLoading(true);
    setPinError('');
    setCurrentStep('processing');

    try {
      await handleBulkPayment();
    } catch (error) {
      console.error('Payment failed:', error);
      setErrorMsg('Payment failed. Please try again.');
      setCurrentStep('failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBulkPayment = async () => {
    if (selectedDues.length === 0 || !wallet) return;

    setPaymentResults([]);

    try {
      console.log('💰 Processing bulk payment for', selectedDues.length, 'dues');

      // Prepare payment data
      const payments = selectedDues.map(due => ({
        dueId: due.id,
        recipientUserId: due.user_id || '1', // Default to admin user if not specified
        amount: due.amount
      }));

      // Process bulk payment with wallet
      const result = await payMultipleDuesWithWallet(
        payments,
        pin.join('')
      );

      console.log('✅ Bulk payment result:', result);

      // Update wallet balance if provided, otherwise refresh from server
      if (wallet && result.success) {
        if (result.newBalance !== undefined) {
          setWallet({
            ...wallet,
            balance: result.newBalance
          });
        } else {
          // Refresh wallet balance from server to get the latest balance
          refreshWalletBalance();
        }
      }

      // Set payment results
      const formattedResults = result.results.map(r => ({
        dueId: r.dueId,
        success: r.success,
        message: r.message,
        transactionId: r.transactionId
      }));

      setPaymentResults(formattedResults);
      setSuccessfulPayments(result.successfulPayments);
      setFailedPayments(result.failedPayments);

      if (result.success) {
        setCurrentStep('success');
        setTimeout(() => {
          onPaymentSuccess();
          handleClose();
        }, 3000);
      } else {
        setCurrentStep('failed');
        setErrorMsg('Some payments failed. Please check the details below.');
      }
    } catch (error) {
      console.error('❌ Bulk payment failed:', error);
      setCurrentStep('failed');
      setErrorMsg('Payment failed. Please try again.');
    }
  };

  const handleAddPaymentMethod = async () => {
    if (!newPaymentMethod.name || !newPaymentMethod.last4) return;

    try {
      const method = await addPaymentMethod({
        ...newPaymentMethod,
        isDefault: false,
      });
      
      setPaymentMethods(prev => [...prev, method]);
      setSelectedPaymentMethod(method.id);
      setShowAddPaymentMethod(false);
      setNewPaymentMethod({ type: 'card', name: '', last4: '', brand: '' });
    } catch (error) {
      console.error('Failed to add payment method:', error);
    }
  };

  const handleClose = () => {
    setCurrentStep('details');
    setPaymentResults([]);
    setPin(['', '', '', '']);
    setPinError('');
    setErrorMsg('');
    setSuccessfulPayments(0);
    setFailedPayments(0);
    onClose();
  };

  const getPaymentMethodIcon = (type: PaymentMethod['type']) => {
    switch (type) {
      case 'card':
        return <CreditCard size={20} className="text-blue-600" />;
      case 'bank':
        return <Building size={20} className="text-green-600" />;
      case 'wallet':
        return <Wallet size={20} className="text-purple-600" />;
      default:
        return <CreditCard size={20} className="text-gray-600" />;
    }
  };

  const getStepTitle = () => {
    switch (currentStep) {
      case 'details':
        return 'Bulk Payment Details';
      case 'pin':
        return 'Enter Wallet PIN';
      case 'processing':
        return 'Processing Payments';
      case 'success':
        return 'Payment Successful';
      case 'failed':
        return 'Payment Failed';
      default:
        return 'Bulk Payment';
    }
  };

  if (!isOpen || selectedDues.length === 0) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      {/* Overlay */}
      <div className="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity duration-300 ease-in-out"></div>

      {/* Offcanvas Panel */}
      <div className="fixed inset-y-0 right-0 max-w-full flex transition-transform duration-300 ease-in-out translate-x-0">
        <div className="relative w-full bg-white shadow-xl flex flex-col">
          {/* Header */}
          <div className="px-6 py-5 border-b flex items-center">
            {currentStep !== 'details' && (
              <button
                onClick={handleBackStep}
                className="mr-3 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <ArrowLeft size={20} />
              </button>
            )}
            <h2 className="text-lg font-medium text-gray-900 flex-1">
              {getStepTitle()}
            </h2>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <span className="sr-only">Close panel</span>
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Content */}
          <div className="flex-1 py-6 px-6 overflow-y-auto">
            {/* Step 1: Payment Details */}
            {currentStep === 'details' && (
              <div className="space-y-6">
                {/* Wallet Balance Card */}
                {wallet && (
                  <div className="rounded-xl p-4 flex items-center justify-between mb-4 bg-gradient-to-r from-orange-400 to-orange-600 relative shadow-sm">
                    <div>
                      <div className="text-xs text-white font-medium mb-1 flex items-center space-x-2">
                        <span>Wallet Balance</span>
                        <button
                          onClick={refreshWalletBalance}
                          disabled={isRefreshingWallet}
                          className="text-white hover:text-orange-100 transition-colors disabled:opacity-50"
                          title="Refresh wallet balance"
                        >
                          <RefreshCw
                            size={14}
                            className={`${isRefreshingWallet ? 'animate-spin' : ''}`}
                          />
                        </button>
                      </div>
                      <div className="text-3xl font-bold text-white">${wallet.balance?.toFixed(2) ?? '--'}</div>
                      <div className="text-xs text-orange-100 mt-1">
                        Available for payments
                        {isRefreshingWallet && <span className="ml-2">• Refreshing...</span>}
                      </div>
                    </div>
                    <div className="flex flex-col items-end">
                      <Wallet size={32} className="text-white mb-2" />
                      <div className="text-xs text-white opacity-80">Primary Bank<br /><span className="font-semibold">{wallet.primaryBank ?? '--'}</span></div>
                    </div>
                  </div>
                )}

                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-semibold text-gray-900 mb-3">Selected Dues ({selectedDues.length})</h3>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {selectedDues.map((due) => (
                      <div key={due.id} className="flex justify-between items-center text-sm">
                        <div>
                          <p className="font-medium text-gray-900">{due.league} - {due.season}</p>
                          <p className="text-gray-600">{due.payerName}</p>
                        </div>
                        <p className="font-bold text-gray-900">${due.amount.toFixed(2)}</p>
                      </div>
                    ))}
                  </div>
                  <div className="border-t pt-3 mt-3">
                    <div className="flex justify-between text-lg font-bold text-gray-900">
                      <span>Total Amount:</span>
                      <span className="text-red-600">${totalAmount.toFixed(2)}</span>
                    </div>
                    {wallet && wallet.balance < totalAmount && (
                      <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                        ⚠️ Insufficient wallet balance. You need ${totalAmount.toFixed(2)} but have ${wallet.balance.toFixed(2)}
                      </div>
                    )}
                  </div>
                </div>

                <button
                  onClick={handleNextStep}
                  className="w-full bg-red-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-red-700 transition-colors"
                >
                  Continue to Payment
                </button>
              </div>
            )}

            {/* Step 2: Payment Method Selection */}
            {currentStep === 'payment-method' && (
              <div className="space-y-6">
                <div>
                  <h3 className="font-semibold text-gray-900 mb-3">Select Payment Method</h3>
                  <div className="space-y-2">
                    {paymentMethods.map((method) => (
                      <label
                        key={method.id}
                        className={`flex items-center space-x-3 p-3 border rounded-lg cursor-pointer transition-colors ${
                          selectedPaymentMethod === method.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <input
                          type="radio"
                          name="paymentMethod"
                          value={method.id}
                          checked={selectedPaymentMethod === method.id}
                          onChange={(e) => setSelectedPaymentMethod(e.target.value)}
                          className="text-blue-600"
                        />
                        {getPaymentMethodIcon(method.type)}
                        <div className="flex-1">
                          <p className="font-medium text-gray-900">{method.name}</p>
                          {method.brand && (
                            <p className="text-sm text-gray-500 capitalize">{method.brand}</p>
                          )}
                        </div>
                      </label>
                    ))}
                  </div>

                  {/* Add New Payment Method */}
                  {!showAddPaymentMethod ? (
                    <button
                      onClick={() => setShowAddPaymentMethod(true)}
                      className="flex items-center space-x-2 text-blue-600 hover:text-blue-700 mt-3"
                    >
                      <Plus size={16} />
                      <span>Add New Payment Method</span>
                    </button>
                  ) : (
                    <div className="mt-4 p-4 border border-gray-200 rounded-lg">
                      <h4 className="font-medium text-gray-900 mb-3">Add Payment Method</h4>
                      <div className="space-y-3">
                        <select
                          value={newPaymentMethod.type}
                          onChange={(e) => setNewPaymentMethod(prev => ({ ...prev, type: e.target.value as any }))}
                          className="w-full p-2 border border-gray-300 rounded-md"
                        >
                          <option value="card">Credit/Debit Card</option>
                          <option value="bank">Bank Account</option>
                          <option value="wallet">Digital Wallet</option>
                        </select>
                        <input
                          type="text"
                          placeholder="Card/Bank Name"
                          value={newPaymentMethod.name}
                          onChange={(e) => setNewPaymentMethod(prev => ({ ...prev, name: e.target.value }))}
                          className="w-full p-2 border border-gray-300 rounded-md"
                        />
                        <input
                          type="text"
                          placeholder="Last 4 digits"
                          value={newPaymentMethod.last4}
                          onChange={(e) => setNewPaymentMethod(prev => ({ ...prev, last4: e.target.value }))}
                          className="w-full p-2 border border-gray-300 rounded-md"
                          maxLength={4}
                        />
                        {newPaymentMethod.type === 'card' && (
                          <input
                            type="text"
                            placeholder="Card Brand (Visa, Mastercard, etc.)"
                            value={newPaymentMethod.brand}
                            onChange={(e) => setNewPaymentMethod(prev => ({ ...prev, brand: e.target.value }))}
                            className="w-full p-2 border border-gray-300 rounded-md"
                          />
                        )}
                        <div className="flex space-x-2">
                          <button
                            onClick={handleAddPaymentMethod}
                            className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
                          >
                            Add
                          </button>
                          <button
                            onClick={() => setShowAddPaymentMethod(false)}
                            className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors"
                          >
                            Cancel
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <button
                  onClick={handleNextStep}
                  disabled={!selectedPaymentMethod || isLoading}
                  className="w-full bg-red-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-red-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center space-x-2">
                      <PaymentLoader
                        type="verification"
                        size="small"
                        showQuotes={false}
                      />
                      <span>Sending OTP...</span>
                    </div>
                  ) : (
                    `Pay All (${selectedDues.length} dues) - $${totalAmount.toFixed(2)}`
                  )}
                </button>
              </div>
            )}

            {/* Step 3: OTP Verification */}
            {currentStep === 'otp' && (
              <div className="space-y-6">
                <div className="text-center">
                  <h3 className="font-semibold text-gray-900 mb-2">OTP Verification</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    We've sent a 6-digit OTP to your registered phone number
                  </p>
                  <p className="text-sm font-medium text-gray-900">
                    {selectedDues[0]?.contact}
                  </p>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Enter OTP
                    </label>
                    <input
                      type="text"
                      value={otp}
                      onChange={(e) => {
                        const value = e.target.value.replace(/\D/g, '').slice(0, 6);
                        setOtp(value);
                        if (otpError) setOtpError('');
                      }}
                      placeholder="Enter 6-digit OTP"
                      className="w-full p-3 border border-gray-300 rounded-lg text-center text-lg font-mono tracking-widest focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      maxLength={6}
                    />
                    {otpError && (
                      <p className="mt-2 text-sm text-red-600">{otpError}</p>
                    )}
                  </div>

                  <div className="flex space-x-3">
                    <button
                      onClick={handleVerifyOTP}
                      disabled={otp.length !== 6 || isLoading}
                      className="flex-1 bg-red-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-red-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                    >
                      {isLoading ? (
                        <div className="flex items-center justify-center space-x-3">
                          <PaymentLoader
                            type="verification"
                            size="small"
                            showQuotes={false}
                          />
                          <span>Verifying & Processing...</span>
                        </div>
                      ) : (
                        'Verify & Pay All'
                      )}
                    </button>
                  </div>

                  <div className="text-center">
                    <p className="text-sm text-gray-600 mb-2">Didn't receive the OTP?</p>
                    <button
                      onClick={handleResendOTP}
                      disabled={isResendingOTP}
                      className="text-blue-600 hover:text-blue-700 text-sm font-medium disabled:text-gray-400"
                    >
                      {isResendingOTP ? 'Resending...' : 'Resend OTP'}
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Step 4: Processing */}
            {currentStep === 'processing' && (
              <div className="space-y-6">
                <div className="text-center">
                  <PaymentLoader
                    type="processing"
                    message={`Processing ${paymentResults.length} of ${selectedDues.length} payments...`}
                    size="large"
                    showQuotes={true}
                  />
                </div>
                
                {/* Show individual payment results */}
                {paymentResults.length > 0 && (
                  <div className="space-y-2">
                    {paymentResults.map(({ due, result }) => (
                      <div key={due.id} className="flex items-center justify-between text-sm p-2 bg-gray-50 rounded">
                        <span className="text-gray-700">{due.league}</span>
                        <span className={result.status === 'completed' ? 'text-green-600' : 'text-red-600'}>
                          {result.status === 'completed' ? '✓' : '✗'}
                        </span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Step 5: Success */}
            {currentStep === 'success' && (
              <div className="space-y-6 text-center">
                <div className="flex items-center justify-center">
                  <CheckCircle size={48} className="text-green-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">All Payments Successful!</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Successfully processed {selectedDues.length} payments.
                  </p>
                </div>
              </div>
            )}

            {/* Step 6: Failed */}
            {currentStep === 'failed' && (
              <div className="space-y-6 text-center">
                <div className="flex items-center justify-center">
                  <XCircle size={48} className="text-red-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Some Payments Failed</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    {paymentResults.filter(r => r.result.status === 'failed').length} of {selectedDues.length} payments failed.
                  </p>
                </div>
                <button
                  onClick={() => setCurrentStep('payment-method')}
                  className="w-full bg-gray-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-gray-700 transition-colors"
                >
                  Try Again
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BulkPaymentOffcanvas; 