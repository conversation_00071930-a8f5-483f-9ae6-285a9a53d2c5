import React, { useState, useEffect } from 'react';
import { X, Users, DollarSign, Building, Clock, CheckCircle, AlertCircle, XCircle, ArrowLeft, Download, Mail, Trophy } from 'lucide-react';
import { PlatformDue, getPlatformDueTeams, getPlatformDuePayments, clearPlatformDueDetailsCache } from '../../services/dues';
import EmptyState from '../common/EmptyState';

interface PlatformDueDetailProps {
  isOpen: boolean;
  onClose: () => void;
  due: PlatformDue | null;
}

const PlatformDueDetail: React.FC<PlatformDueDetailProps> = ({
  isOpen,
  onClose,
  due,
}) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'teams' | 'payments'>('overview');
  const [teams, setTeams] = useState<Array<{
    id: string;
    name: string;
    captain: string;
    players: number;
    amountDue: number;
    amountPaid: number;
    status: 'Paid' | 'Pending';
  }>>([]);
  const [payments, setPayments] = useState<Array<{
    id: string;
    date: string;
    amount: number;
    method: string;
    reference: string;
    teams: string[];
    card_details?: string; // Added card_details field
  }>>([]);
  const [detailsLoaded, setDetailsLoaded] = useState(false);

  useEffect(() => {
    if (!due) return;
    if (!detailsLoaded) {
      fetchPlatformDueDetails();
    }
    // eslint-disable-next-line
  }, [due]);

  const fetchPlatformDueDetails = async () => {
    if (!due) return;
    try {
      const [teamsData, paymentsData] = await Promise.all([
        getPlatformDueTeams(due.id),
        getPlatformDuePayments(due.id),
      ]);
      setTeams(teamsData);
      setPayments(paymentsData);
      setDetailsLoaded(true);
    } catch (error) {
      console.error('Failed to fetch platform due details:', error);
      setTeams([]);
      setPayments([]);
      setDetailsLoaded(false);
    }
  };

  // Optionally, clear cache when closing or due changes
  useEffect(() => {
    return () => {
      if (due) clearPlatformDueDetailsCache(due.id);
      setDetailsLoaded(false);
    };
  }, [due]);

  useEffect(() => {
    if (!due) return;
    if (!detailsLoaded) return;
    // If switching tabs and data is already loaded, do nothing
    // If not loaded, fetch
    if ((activeTab === 'teams' && teams.length === 0) || (activeTab === 'payments' && payments.length === 0)) {
      fetchPlatformDueDetails();
    }
    // eslint-disable-next-line
  }, [activeTab]);

  if (!isOpen || !due) return null;

  const getStatusIcon = (status: PlatformDue['status']) => {
    switch (status) {
      case 'Paid':
        return <CheckCircle className="text-green-600" size={24} />;
      case 'Pending':
        return <Clock className="text-yellow-600" size={24} />;
      case 'Overdue':
        return <XCircle className="text-red-600" size={24} />;
      default:
        return <AlertCircle className="text-gray-600" size={24} />;
    }
  };

  const getStatusColor = (status: PlatformDue['status']) => {
    switch (status) {
      case 'Paid':
        return 'text-green-600 bg-green-100 border-green-200';
      case 'Pending':
        return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'Overdue':
        return 'text-red-600 bg-red-100 border-red-200';
      default:
        return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const paymentPercentage = (due.amount_paid / due.total_amount) * 100;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
      
      <div className="absolute right-0 top-0 h-full w-full max-w-4xl bg-white shadow-xl">
        <div className="flex h-full flex-col">
          {/* Header */}
          <div className="flex items-center justify-between border-b border-gray-200 px-6 py-4">
            <div className="flex items-center space-x-4">
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <h2 className="text-xl font-bold text-gray-900">{due.league_name}</h2>
                <p className="text-sm text-gray-600">{due.season_name} • {due.division_name}</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X size={24} />
            </button>
          </div>

          {/* Status Banner */}
          <div className={`px-6 py-4 border-b ${getStatusColor(due.status)}`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {getStatusIcon(due.status)}
                <div>
                  <p className="font-semibold">Status: {due.status}</p>
                  <p className="text-sm opacity-80">
                    Due Date: {new Date(due.due_date).toLocaleDateString()}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold">${due.amount_pending}</p>
                <p className="text-sm opacity-80">Amount Pending</p>
              </div>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {[
                { id: 'overview', label: 'Overview', icon: Building },
                { id: 'teams', label: 'Teams', icon: Users },
                { id: 'payments', label: 'Payment History', icon: DollarSign },
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as 'overview' | 'teams' | 'payments')}
                  className={`flex items-center space-x-2 py-4 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <tab.icon size={16} />
                  <span>{tab.label}</span>
                </button>
              ))}
            </nav>
          </div>
          <div className="flex-1 overflow-y-auto p-6">
            {activeTab === 'overview' && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="bg-blue-50 rounded-lg p-4">
                    <div className="flex items-center space-x-2">
                      <Users className="text-blue-600" size={20} />
                      <span className="text-sm font-medium text-blue-600">Teams</span>
                    </div>
                    <p className="text-2xl font-bold text-blue-900 mt-1">{due.teams_count}</p>
                  </div>
                  
                  <div className="bg-green-50 rounded-lg p-4">
                    <div className="flex items-center space-x-2">
                      <DollarSign className="text-green-600" size={20} />
                      <span className="text-sm font-medium text-green-600">Total Amount</span>
                    </div>
                    <p className="text-2xl font-bold text-green-900 mt-1">${due.total_amount.toLocaleString()}</p>
                  </div>
                  
                  <div className="bg-yellow-50 rounded-lg p-4">
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="text-yellow-600" size={20} />
                      <span className="text-sm font-medium text-yellow-600">Amount Paid</span>
                    </div>
                    <p className="text-2xl font-bold text-yellow-900 mt-1">${due.amount_paid.toLocaleString()}</p>
                  </div>
                  
                  <div className="bg-red-50 rounded-lg p-4">
                    <div className="flex items-center space-x-2">
                      <Clock className="text-red-600" size={20} />
                      <span className="text-sm font-medium text-red-600">Amount Pending</span>
                    </div>
                    <p className="text-2xl font-bold text-red-900 mt-1">${due.amount_pending.toLocaleString()}</p>
                  </div>
                </div>

                {/* Payment Progress */}
                <div className="bg-white border rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Progress</h3>
                  <div className="space-y-4">
                    <div className="flex justify-between text-sm">
                      <span>Progress</span>
                      <span>{paymentPercentage.toFixed(1)}% Complete</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div
                        className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                        style={{ width: `${paymentPercentage}%` }}
                      />
                    </div>
                    <div className="flex justify-between text-sm text-gray-600">
                      <span>${due.amount_paid.toLocaleString()} paid</span>
                      <span>${due.amount_pending.toLocaleString()} remaining</span>
                    </div>
                  </div>
                </div>

                {/* League Information */}
                <div className="bg-white border rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">League Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Season</label>
                      <p className="text-gray-900">{due.season_name}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Division</label>
                      <p className="text-gray-900">{due.division_name || 'N/A'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Created Date</label>
                      <p className="text-gray-900">{new Date(due.created_at).toLocaleDateString()}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Due Date</label>
                      <p className="text-gray-900">{new Date(due.due_date).toLocaleDateString()}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'teams' && (
             <> 
             {
              teams.length > 0 ? (
                 <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold text-gray-900">Teams ({due.teams_count})</h3>
                  <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                    <Download size={16} />
                    <span>Export List</span>
                  </button>
                </div>
                
                <div className="bg-white border rounded-lg overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Team</th>
                        {/* <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Captain</th> */}
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Players</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {teams.map((team) => (
                        <tr key={team.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="font-medium text-gray-900">{team.name}</div>
                          </td>
                          {/* <td className="px-6 py-4 whitespace-nowrap text-gray-600">
                            {team.captain}
                          </td> */}
                          <td className="px-6 py-4 whitespace-nowrap text-gray-600">
                            {team.players}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap font-medium">
                            ${team.amountDue.toLocaleString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              team.status === 'Paid' ? 'text-green-600 bg-green-100' : 'text-yellow-600 bg-yellow-100'
                            }`}>
                              {team.status}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
              ) : (
                <EmptyState
                  type="staff"
                  title="No Teams Found"
                  description="No teams have been added to this platform due yet."
                />
              ) }
             
              </>
            )}

            {activeTab === 'payments' && (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold text-gray-900">Payment History</h3>
                  <button className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
                    <Mail size={16} />
                    <span>Send Reminder</span>
                  </button>
                </div>
                
                {payments.length > 0 ? (
                  <div className="space-y-4">
                    {payments.map((payment) => {
                      const cardDetailsString = payment.card_details;
                      let method = 'Unknown';

                      if (cardDetailsString) {
                        try {
                          const cardDetails = JSON.parse(cardDetailsString);
                          method = `${cardDetails.brand?.toUpperCase() || 'CARD'} •••• ${cardDetails.last4 || ''} (${cardDetails.funding || ''})`;
                        } catch (e) {
                          method = 'Card';
                        }
                      }

                      return (
                        <div key={payment.id} className="bg-white border rounded-lg p-6">
                          <div className="flex justify-between items-start">
                            <div className="space-y-2">
                              <div className="flex items-center space-x-2">
                                <CheckCircle className="text-green-600" size={20} />
                                <span className="font-semibold text-gray-900">
                                  ${payment.amount.toLocaleString()}
                                </span>
                                <span className="text-gray-500">•</span>
                                <span className="text-gray-600">{payment.method}</span>
                              </div>
                              <p className="text-sm text-gray-600">
                                Reference: {payment.reference}
                              </p>
                              <p className="text-sm text-gray-600">
                                Teams: {payment.teams.join(', ')}
                              </p>
                            </div>
                            <div className="text-right">
                              <p className="text-sm text-gray-600">
                                {new Date(payment.date).toLocaleDateString()}
                              </p>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <DollarSign className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No payments yet</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Payment history will appear here once teams start paying.
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PlatformDueDetail;
