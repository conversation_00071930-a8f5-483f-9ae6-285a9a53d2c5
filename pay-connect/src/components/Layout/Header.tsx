import React from 'react';
import { Search, Bell, Menu, HelpCircle } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import logoImage from '../../assets/images/giu_pay_connect.png';

interface HeaderProps {
  onMenuToggle: () => void;
}

const Header: React.FC<HeaderProps> = ({ onMenuToggle }) => {
  const { user } = useAuth();

  return (
<header className="sticky top-0 bg-white shadow-sm border-b px-4 py-3 z-30 h-16 flex items-center">


      <div className="flex items-center justify-between w-full">
        {/* Left side */}
        <div className="flex items-center space-x-4">
          <button
            onClick={onMenuToggle}
            className="p-2 rounded-md hover:bg-gray-100 mr-4"
          >
            <Menu className="w-5 h-5" />
          </button>

          {/* Logo for mobile when sidebar is collapsed */}
          <div className="flex items-center space-x-3 lg:hidden">
            <div className="w-10 h-10 flex items-center justify-center">
              <img
                src={logoImage}
                alt="Pay Connect Logo"
                className="w-full h-full object-contain"
              />
            </div>
            <span className="font-bold text-gray-800">Pay Connect</span>
          </div>

          <h1 className="text-xl font-semibold text-gray-800 hidden lg:block">Dashboard</h1>
        </div>

        {/* Right side */}
        <div className="flex items-center space-x-4">
          {/* Search */}
          <div className="relative hidden md:block">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search..."
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Notifications */}
          <div className="relative">
            <button className="p-2 rounded-full hover:bg-gray-100 relative">
              <Bell className="w-5 h-5 text-gray-600" />
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                3
              </span>
            </button>
          </div>

          {/* Help */}
          <div className="relative">
            <button className="p-2 rounded-full hover:bg-gray-100 relative">
              <HelpCircle className="w-5 h-5 text-gray-600" />
              <span className="absolute -top-1 -right-1 bg-orange-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                ?
              </span>
            </button>
          </div>

          {/* User Profile */}
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-medium">
                {user?.name?.charAt(0) || 'U'}
              </span>
            </div>
            <div className="hidden md:block">
              <p className="text-sm font-medium text-gray-800">{user?.name}</p>
              <p className="text-xs text-gray-500">Welcome</p>
            </div>
          </div>

          {/* Need Help */}
          <div className="hidden lg:flex items-center space-x-2 text-orange-500">
            <HelpCircle className="w-4 h-4" />
            <span className="text-sm font-medium">Need Help ?</span>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
