import React from 'react';
import { NavLink } from 'react-router-dom';
import {
  LayoutDashboard,
  Wallet,
  ArrowUpDown,
  CreditCard,
  BarChart3,
  FileText,
  Banknote,
  Settings,
  LogOut,
  X,
  Users
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import logoImage from '../../assets/images/giu_pay_connect.png';

interface SidebarProps {
  isOpen: boolean;
  onToggle: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onToggle }) => {
  const { logout } = useAuth();

  const menuItems = [
    { icon: LayoutDashboard, label: 'Dashboard', path: '/dashboard' },
    { icon: Wallet, label: 'My Wallet', path: '/wallet' },
    { icon: ArrowUpDown, label: 'Transaction', path: '/transactions' },
    { icon: CreditCard, label: 'Awaiting Payments', path: '/awaiting-payments' },
    { icon: BarChart3, label: 'My Dues', path: '/dues' },
    { icon: Users, label: 'Staff', path: '/staff' },
    { icon: FileText, label: 'Invoices', path: '/invoices' },
    { icon: Banknote, label: 'Bank Account', path: '/bank-account' },
    { icon: Settings, label: 'Settings', path: '/settings' },
  ];

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onToggle}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed top-0 left-0 h-screen min-h-screen bg-white shadow-lg  z-50 transition-transform duration-300 ease-in-out flex flex-col
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
        lg:translate-x-0 lg:sticky lg:z-auto
        ${isOpen ? 'lg:w-64' : 'lg:w-16'}
      `}>
        {/* Header */}
        <div className={`sticky top-0 flex items-center p-4 border-b bg-white z-10 ${isOpen ? 'justify-between' : 'justify-center'}`}>
          <div className={`flex items-center space-x-3 ${isOpen ? '' : 'hidden lg:flex'}`}>
            <div className="w-12 h-12 flex items-center justify-center">
              <img
                src={logoImage}
                alt="Pay Connect Logo"
                className="w-full h-full object-contain"
              />
            </div>
            {isOpen && (
              <div className="flex flex-col">
                <span className="font-bold text-gray-800 text-lg leading-tight">Pay Connect</span>
                <span className="text-xs text-gray-500 leading-tight">by GIU Team</span>
              </div>
            )}
          </div>
          <button
            onClick={onToggle}
            className="lg:hidden p-1 rounded-md hover:bg-gray-100"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Navigation */}
        <nav className={`flex-1 p-4 overflow-y-auto ${!isOpen && 'px-2'}`}>
          <ul className="space-y-2">
            {menuItems.map((item) => (
              <li key={item.path} className='relative ' >
                <NavLink
                  to={item.path}
                  className={({ isActive }) =>
                    `flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors group   ${isActive
                      ? 'bg-orange-50 text-orange-600 border-r-2 border-orange-600'
                      : 'text-gray-600 hover:bg-gray-50'
                    } ${isOpen ? '' : 'justify-center space-x-0'}`
                  }
                  onClick={() => window.innerWidth < 1024 && onToggle()}
                >
                  <item.icon className="w-5 h-5" />
                  {isOpen ? (
                    <span className="font-medium">{item.label}</span>
                  ) : (
                    <span className="hidden group-hover:block absolute left-16 top-1/2 transform -translate-y-1/2 bg-gray-800 text-white text-xs rounded py-1 px-2 z-50 whitespace-nowrap">
                      {item.label}
                    </span>
                  )}
                </NavLink>
              </li>
            ))}
          </ul>
        </nav>

        {/* Logout */}
        <div className={`p-4 border-t ${!isOpen && 'px-2'}`}>
          <button
            onClick={logout}
            className={`flex items-center px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-50 w-full transition-colors group relative ${isOpen ? 'space-x-3 justify-start' : 'space-x-0 justify-center'}`}
          >
            <LogOut className="w-5 h-5" />
            {isOpen ? (
              <span className="font-medium">Logout</span>
            ) : (
              <span className="hidden group-hover:block absolute left-16 top-1/2 transform -translate-y-1/2 bg-gray-800 text-white text-xs rounded py-1 px-2 z-10 whitespace-nowrap">
                Logout
              </span>
            )}
          </button>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
