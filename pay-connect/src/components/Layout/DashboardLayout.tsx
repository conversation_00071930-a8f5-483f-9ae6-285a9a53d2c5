import React, { useState } from 'react';
import { Outlet } from 'react-router-dom';
import Sidebar from './Sidebar';
import Header from './Header';

const DashboardLayout: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <div className="flex min-h-screen bg-gray-50">
      <Sidebar isOpen={sidebarOpen} onToggle={toggleSidebar} />
      <div className={`flex-1 flex flex-col transition-all duration-300 ease-in-out  lg:mt-0`}>
        <Header onMenuToggle={toggleSidebar} />
        <main className="flex-1 p-4 lg:p-6 overflow-y-auto pt-20 bg-[#EFEDEC] p-5">
      <div className="bg-white rounded-xl p-8 min-h-[837px] shadow-sm border">
            <Outlet />
          </div>

        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;
