import React from 'react';
import { Download, Filter, Plus } from 'lucide-react';

interface FilterAndExportControlsProps {
  className?: string;
  onFilterChange?: (status: string) => void;
  onExport?: () => void;
  onCreateInvoice?: () => void;
  currentFilter?: string;
}

const FilterAndExportControls: React.FC<FilterAndExportControlsProps> = ({
  className = '',
  onFilterChange,
  onExport,
  onCreateInvoice,
  currentFilter = 'All'
}) => {
  const statusOptions = [
    { value: 'All', label: 'All Invoices' },
    { value: 'Draft', label: 'Draft' },
    { value: 'Sent', label: 'Sent' },
    { value: 'Paid', label: 'Paid' },
    { value: 'Overdue', label: 'Overdue' },
    { value: 'Cancelled', label: 'Cancelled' }
  ];

  return (
    <div className={`flex flex-col sm:flex-row justify-between items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-3 ${className}`}>
      {/* Status Filter */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Filter className="h-4 w-4 text-gray-400" />
        </div>
        <select
          value={currentFilter}
          onChange={(e) => onFilterChange?.(e.target.value)}
          className="block w-full pl-10 pr-8 py-2 text-sm border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 appearance-none cursor-pointer"
        >
          {statusOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
          <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex space-x-2">
        {/* Create Invoice Button */}
        {onCreateInvoice && (
          <button
            onClick={onCreateInvoice}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          >
           
            <span className="hidden sm:inline">Create Invoice</span>
            <span className="sm:hidden">Create</span>
             <Plus className="h-4 w-4" />
          </button>
        )}

        {/* Export Button */}
        {onExport && (
          <button
            onClick={onExport}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors"
          >
           
            <span className="hidden sm:inline">Export</span>
             <Download className="h-4 w-4" />
          </button>
        )}
      </div>
    </div>
  );
};

export default FilterAndExportControls;
