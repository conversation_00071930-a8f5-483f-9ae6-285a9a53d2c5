import React, { useState, useEffect } from 'react';
import { getInvoiceById, type Invoice } from '../../services/invoices';
import { Mail, Phone, Calendar, FileText, Download, Send, Edit, CheckCircle } from 'lucide-react';
import PaymentLoader from '../common/PaymentLoader';

interface InvoiceDetailOffcanvasProps {
  invoiceId: string | null;
  onClose: () => void;
}

const InvoiceDetailOffcanvas: React.FC<InvoiceDetailOffcanvasProps> = ({
  invoiceId,
  onClose,
}) => {
  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (invoiceId) {
      fetchInvoiceDetails(invoiceId);
    } else {
      setInvoice(null);
      setLoading(false);
      setError(null);
    }
  }, [invoiceId]);

  const fetchInvoiceDetails = async (id: string) => {
    try {
      setLoading(true);
      // Add artificial delay to show the engaging loader
      await new Promise(resolve => setTimeout(resolve, 1000));
      const data = await getInvoiceById(id);
      setInvoice(data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch invoice details');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: Invoice['status']) => {
    switch (status) {
      case 'Paid':
        return 'bg-green-100 text-green-800';
      case 'Sent':
        return 'bg-blue-100 text-blue-800';
      case 'Draft':
        return 'bg-gray-100 text-gray-800';
      case 'Overdue':
        return 'bg-red-100 text-red-800';
      case 'Cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const calculateSubtotal = () => {
    if (!invoice) return 0;
    return invoice.items.reduce((sum, item) => sum + item.total, 0);
  };

  const calculateDiscount = () => {
    if (!invoice) return 0;
    return calculateSubtotal() * (invoice.discountPercent / 100);
  };

  const calculateTax = () => {
    if (!invoice) return 0;
    const afterDiscount = calculateSubtotal() - calculateDiscount();
    return afterDiscount * (invoice.taxPercent / 100);
  };

  const calculateTotal = () => {
    return calculateSubtotal() - calculateDiscount() + calculateTax();
  };

  const handleSendInvoice = () => {
    if (invoice) {
      alert(`Invoice ${invoice.invoiceNumber} sent to ${invoice.clientEmail}`);
    }
  };

  const handleDownloadPDF = () => {
    if (invoice) {
      alert(`Downloading PDF for invoice ${invoice.invoiceNumber}`);
    }
  };

  const handleMarkAsPaid = () => {
    if (invoice) {
      alert(`Invoice ${invoice.invoiceNumber} marked as paid`);
    }
  };

  const handleEditInvoice = () => {
    if (invoice) {
      alert(`Edit functionality for invoice ${invoice.invoiceNumber} will be implemented`);
    }
  };

  if (!invoiceId) {
    return null; // Don't render if no invoiceId is provided (offcanvas is closed)
  }

  if (loading) {
    return (
      <div className="p-6 text-center">
        <PaymentLoader
          type="processing"
          message="Loading invoice details..."
          size="medium"
          showQuotes={true}
        />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      </div>
    );
  }

  if (!invoice) {
    return (
      <div className="p-6 text-center">
        <div className="text-gray-500">Invoice not found</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Invoice Header */}
      <div className="bg-white rounded-xl shadow-sm border p-6 space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-bold text-gray-900">{invoice.invoiceNumber}</h3>
          <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(invoice.status)}`}>
            {invoice.status}
          </span>
        </div>
        
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-500">Issue Date:</span>
            <p className="font-medium">{formatDate(invoice.issueDate)}</p>
          </div>
          <div>
            <span className="text-gray-500">Due Date:</span>
            <p className="font-medium">{formatDate(invoice.dueDate)}</p>
          </div>
        </div>
      </div>

      {/* Client Details */}
      <div className="bg-white rounded-xl shadow-sm border p-6 space-y-4">
        <h3 className="text-lg font-bold text-gray-900">Client Details</h3>
        <div className="flex items-center space-x-4">
          <img src={invoice.avatar} alt={invoice.clientName} className="h-16 w-16 rounded-full object-cover" />
          <div className="space-y-2">
            <p className="font-semibold text-gray-900">{invoice.clientName}</p>
            <div className="flex items-center text-sm text-gray-600">
              <Mail size={14} className="mr-2" />
              <span>{invoice.clientEmail}</span>
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <Phone size={14} className="mr-2" />
              <span>{invoice.clientPhone}</span>
            </div>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-500">League:</span>
            <p className="font-medium">{invoice.league}</p>
          </div>
          <div>
            <span className="text-gray-500">Season:</span>
            <p className="font-medium">{invoice.season}</p>
          </div>
        </div>
      </div>

      {/* Invoice Items */}
      <div className="bg-white rounded-xl shadow-sm border p-6 space-y-4">
        <h3 className="text-lg font-bold text-gray-900">Invoice Items</h3>
        <div className="space-y-3">
          {invoice.items.map((item) => (
            <div key={item.id} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
              <div className="flex-1">
                <p className="font-medium text-gray-900">{item.description}</p>
                <p className="text-sm text-gray-500">Qty: {item.quantity} × ${item.unitPrice.toLocaleString()}</p>
              </div>
              <div className="text-right">
                <p className="font-medium">${item.total.toLocaleString()}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Payment Summary */}
      <div className="bg-white rounded-xl shadow-sm border p-6 space-y-4">
        <h3 className="text-lg font-bold text-gray-900">Payment Summary</h3>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">Subtotal:</span>
            <span className="font-medium">${calculateSubtotal().toLocaleString()}</span>
          </div>
          {invoice.discountPercent > 0 && (
            <div className="flex justify-between text-green-600">
              <span>Discount ({invoice.discountPercent}%):</span>
              <span>-${calculateDiscount().toLocaleString()}</span>
            </div>
          )}
          {invoice.taxPercent > 0 && (
            <div className="flex justify-between">
              <span className="text-gray-600">Tax ({invoice.taxPercent}%):</span>
              <span className="font-medium">${calculateTax().toLocaleString()}</span>
            </div>
          )}
          <div className="border-t pt-2 flex justify-between text-lg font-bold">
            <span>Total:</span>
            <span>${calculateTotal().toLocaleString()}</span>
          </div>
        </div>
      </div>

      {/* Notes */}
      {invoice.notes && (
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <h3 className="text-lg font-bold text-gray-900 mb-2">Notes</h3>
          <p className="text-gray-700">{invoice.notes}</p>
        </div>
      )}

      {/* Action Buttons */}
      <div className="bg-white rounded-xl shadow-sm border p-6">
        <h3 className="text-lg font-bold text-gray-900 mb-4">Actions</h3>
        <div className="grid grid-cols-2 gap-3">
          {invoice.status === 'Draft' && (
            <>
              <button
                onClick={handleSendInvoice}
                className="flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors"
              >
                <Send size={18} />
                <span>Send Invoice</span>
              </button>
              <button
                onClick={handleEditInvoice}
                className="flex items-center justify-center space-x-2 bg-orange-600 hover:bg-orange-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors"
              >
                <Edit size={18} />
                <span>Edit Invoice</span>
              </button>
            </>
          )}
          
          {invoice.status === 'Sent' && (
            <button
              onClick={handleMarkAsPaid}
              className="flex items-center justify-center space-x-2 bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors"
            >
              <CheckCircle size={18} />
              <span>Mark as Paid</span>
            </button>
          )}
          
          {['Sent', 'Paid', 'Overdue'].includes(invoice.status) && (
            <button
              onClick={handleDownloadPDF}
              className="flex items-center justify-center space-x-2 bg-purple-600 hover:bg-purple-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors"
            >
              <Download size={18} />
              <span>Download PDF</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default InvoiceDetailOffcanvas;
