import React, { useState, useEffect } from 'react';
import TableRow from './TableRow';
import { Invoice, getInvoices, updateInvoiceStatus } from '../../services/invoices';
import EmptyState from '../common/EmptyState';
import PaymentLoader from '../common/PaymentLoader';
import DeleteConfirmModal from './DeleteConfirmModal';

interface InvoicesTableProps {
  className?: string;
  search: string;
  statusFilter: string;
  onViewDetails: (id: string) => void;
  onDataChange?: () => void;
}

const InvoicesTable: React.FC<InvoicesTableProps> = ({ 
  className, 
  search, 
  statusFilter,
  onViewDetails,
  onDataChange
}) => {
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchInvoices();
  }, [search, statusFilter]);

  const fetchInvoices = async () => {
    try {
      setLoading(true);
      // Add artificial delay to show the engaging loader
      await new Promise(resolve => setTimeout(resolve, 1500));
      const data = await getInvoices({
        search: search || undefined,
        status: statusFilter !== 'All' ? statusFilter : undefined
      });
      setInvoices(data.data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch invoices');
    } finally {
      setLoading(false);
    }
  };

  const handleStatusUpdate = async (id: string, newStatus: Invoice['status']) => {
    try {
      const updatedInvoice = await updateInvoiceStatus(id, newStatus);
      setInvoices(invoices.map(invoice => 
        invoice.id === id ? updatedInvoice : invoice
      ));
      onDataChange?.(); // Refresh summary data
    } catch (err) {
      setError('Failed to update invoice status');
    }
  };

  const handleSendInvoice = (id: string) => {
    handleStatusUpdate(id, 'Sent');
  };

  const handleDownloadInvoice = (id: string) => {
    // Simulate PDF download
    alert(`Downloading invoice PDF for invoice ID: ${id}`);
  };

  const handleEditInvoice = (id: string) => {
    // Navigate to edit page or open edit modal
    alert(`Edit functionality for invoice ID: ${id} will be implemented`);
  };


  const [showModal, setShowModal] = useState(false);
  const [selectedInvoiceId, setSelectedInvoiceId] = useState<string | null>(null);

  const handleDeleteClick = (id: string) => {
    setSelectedInvoiceId(id);
    setShowModal(true);
  };

  const handleDeleteInvoice = () => {
    if (selectedInvoiceId) {
      setInvoices(invoices.filter(invoice => invoice.id !== selectedInvoiceId));
      alert(`Invoice ${selectedInvoiceId} deleted successfully`);
      setSelectedInvoiceId(null);
      setShowModal(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <PaymentLoader
          type="processing"
          message="Loading invoices..."
          size="medium"
          showQuotes={true}
        />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        {error}
      </div>
    );
  }

  // Check if we should show empty state
  const hasSearch = search.trim() !== '';
  const hasFilter = statusFilter !== 'All';
  const showEmptyState = invoices.length === 0;

  if (showEmptyState) {
    return (
      <div className={`rounded-md border border-gray-200 ${className}`}>
        <EmptyState
          type={hasSearch || hasFilter ? 'search' : 'invoices'}
          title={hasSearch || hasFilter ? 'No invoices found' : 'No invoices yet'}
          description={
            hasSearch || hasFilter 
              ? 'Try adjusting your search criteria or filters.' 
              : 'Create your first invoice to get started.'
          }
          onAction={() => window.location.reload()}
        />
      </div>
    );
  }

  return (
    <div className={`overflow-x-auto rounded-md ${className}`}>
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-[#404040]">
          <tr>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              Invoice Details
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              Client
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              Phone
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              League/Tournament
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              Amount
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              Due Date
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              Status
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {invoices.map((invoice) => (
            <TableRow
          key={invoice.id}
  {...invoice}
  onStatusUpdate={handleStatusUpdate}
  onViewDetails={onViewDetails}
  onSendInvoice={handleSendInvoice}
  onDownloadInvoice={handleDownloadInvoice}
  onEditInvoice={handleEditInvoice}
  onDeleteInvoice={handleDeleteClick} // ✅ This shows the modal
            />
          ))}
        </tbody>
      </table>

      {/* Delete Modal */}
      <DeleteConfirmModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onConfirm={handleDeleteInvoice}
        invoiceId={selectedInvoiceId}
      />
    </div>
  );
};

export default InvoicesTable;
