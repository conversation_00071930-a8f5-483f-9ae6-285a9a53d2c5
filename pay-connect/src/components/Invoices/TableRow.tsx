import React from 'react';
import { Invoice } from '../../services/invoices';
import { Eye, Send, Download, Edit, Trash2, CheckCircle } from 'lucide-react';

interface TableRowProps extends Invoice {
  onStatusUpdate: (id: string, status: Invoice['status']) => void;
  onViewDetails: (id: string) => void;
  onSendInvoice?: (id: string) => void;
  onDownloadInvoice?: (id: string) => void;
  onEditInvoice?: (id: string) => void;
  onDeleteInvoice?: (id: string) => void;
}

const TableRow: React.FC<TableRowProps> = ({
  id,
  invoiceNumber,
  clientName,
  clientEmail,
  clientPhone,
  league,
  season,
  amount,
  issueDate,
  dueDate,
  status,
  avatar,
  onStatusUpdate,
  onViewDetails,
  onSendInvoice,
  onDownloadInvoice,
  onEditInvoice,
  onDeleteInvoice
}) => {
  const getStatusColor = (status: Invoice['status']) => {
    switch (status) {
      case 'Paid':
        return 'bg-green-100 text-green-800';
      case 'Sent':
        return 'bg-blue-100 text-blue-800';
      case 'Draft':
        return 'bg-gray-100 text-gray-800';
      case 'Overdue':
        return 'bg-red-100 text-red-800';
      case 'Cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handleStatusChange = (newStatus: Invoice['status']) => {
    onStatusUpdate(id, newStatus);
  };

  const getAvailableActions = () => {
    const actions = [];
    
    // View Details - always available
    actions.push(
      <button
        key="view"
        onClick={() => onViewDetails(id)}
        className="text-orange-500 hover:text-orange-500   ml-3 mt-1 p-2 rounded-full bg-orange-50 hover:bg-orange-100  text-orange-500 transition"
        title="View Details"
      >
        <Eye size={16} />
      </button>
    );

    // Status-specific actions
    if (status === 'Draft') {
      actions.push(
        <button
          key="send"
          onClick={() => onSendInvoice?.(id)}
          className="text-green-500 hover:text-green-500   ml-3 mt-1 p-2 rounded-full bg-green-50 hover:bg-green-100  text-green-500 transition"
          title="Send Invoice"
        >
          <Send size={16} />
        </button>
      );
      actions.push(
        <button
          key="edit"
          onClick={() => onEditInvoice?.(id)}
          className="text-blue-500 hover:text-blue-500   ml-3 mt-1 p-2 rounded-full bg-blue-50 hover:bg-blue-100  text-blue-500 transition"
          title="Edit Invoice"
        >
          <Edit size={16} />
        </button>
      );
    }

    if (status === 'Sent') {
      actions.push(
        <button
          key="mark-paid"
          onClick={() => handleStatusChange('Paid')}
          className="text-green-500 hover:text-green-500   ml-3 mt-1 p-2 rounded-full bg-green-50 hover:bg-green-100  text-green-500 transition"
          title="Mark as Paid"
        >
          <CheckCircle size={16} />
        </button>
      );
    }

    // Download - available for sent, paid, and overdue invoices
    if (['Sent', 'Paid', 'Overdue'].includes(status)) {
      actions.push(
        <button
          key="download"
          onClick={() => onDownloadInvoice?.(id)}
          className="text-purple-500 hover:text-purple-500   ml-3 mt-1 p-2 rounded-full bg-purple-50 hover:bg-purple-100  text-purple-500 transition"
          title="Download PDF"
        >
          <Download size={16} />
        </button>
      );
    }

    // Delete - available for draft and cancelled invoices
    if (['Draft', 'Cancelled'].includes(status)) {
      actions.push(
        <button
          key="delete"
          onClick={() => onDeleteInvoice?.(id)}
          className="text-red-500 hover:text-red-500   ml-3 mt-1 p-2 rounded-full bg-red-50 hover:bg-red-100  text-red-500 transition"
          title="Delete Invoice"
        >
          <Trash2 size={16} />
        </button>
      );
    }

    return actions;
  };

  return (
    <tr className="hover:bg-gray-50 transition-colors">
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="flex items-center">
          <img className="h-10 w-10 rounded-full object-cover" src={avatar} alt={clientName} />
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">{invoiceNumber}</div>
            <div className="text-sm text-gray-500">{formatDate(issueDate)}</div>
          </div>
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm font-medium text-gray-900">{clientName}</div>
        <div className="text-sm text-gray-500">{clientEmail}</div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {clientPhone}
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm text-gray-900">{league}</div>
        <div className="text-sm text-gray-500">{season}</div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
        ${amount.toLocaleString()}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {formatDate(dueDate)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(status)}`}>
          {status}
        </span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
        <div className="flex items-center space-x-2">
          {getAvailableActions()}
        </div>
      </td>
    </tr>
  );
};

export default TableRow;
