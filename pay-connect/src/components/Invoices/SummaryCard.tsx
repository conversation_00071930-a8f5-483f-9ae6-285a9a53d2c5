import React from 'react';

interface SummaryCardProps {
  title: string;
  value: string | number;
  count?: number;
  className?: string;
  onClick?: () => void;
  selected?: boolean;
  icon?: React.ReactElement; // <- Add this line
}

const SummaryCard: React.FC<SummaryCardProps> = ({
  title,
  value,
  count,
  className = '',
  onClick,
  selected = false,
  icon, // <- Add this
}) => {
  const formatValue = (val: string | number) => {
    if (typeof val === 'number') {
      return `$${val.toLocaleString()}`;
    }
    return val;
  };

  return (
    <div
      className={`
        p-6 rounded-[20px] shadow-sm border transition-all duration-200 cursor-pointer flex justify-between items-center h[120px]  
        ${selected 
          ? 'border-blue-300 bg-blue-50 shadow-md transform scale-105' 
          : 'border-gray-200 bg-white hover:shadow-md hover:border-gray-300'
        }
        ${className}
      `}
      onClick={onClick}
    >
      <div className="flex-1">
        <h3 className={`text-sm font-medium mb-2 ${
          selected ? 'text-blue-700' : 'text-gray-600'
        }`}>
          {title}
        </h3>
        <div className="flex items-baseline space-x-2">
          <p className={`text-2xl font-bold ${
            selected ? 'text-blue-900' : 'text-gray-900'
          }`}>
            {formatValue(value)}
          </p>
          {count !== undefined && (
            <span className={`text-sm font-medium ${
              selected ? 'text-blue-600' : 'text-gray-500'
            }`}>
              ({count} {count === 1 ? 'invoice' : 'invoices'})
            </span>
          )}
        </div>
      </div>

      <div className="flex items-center">
        {icon && (
          <div className={`w-10 h-10 rounded-full flex items-center justify-center bg-white shadow-inner ${
            selected ? 'text-blue-500' : 'text-gray-500'
          }`}>
            {React.cloneElement(icon, { size: 24 })}
          </div>
        )}

        {selected && (
          <div className="ml-2 mt-1">
            <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SummaryCard;
