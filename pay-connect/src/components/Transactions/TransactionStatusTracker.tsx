import React, { useState, useEffect } from 'react';
import { 
  Check<PERSON><PERSON><PERSON>, 
  Clock, 
  AlertCircle, 
  XCircle, 
  RefreshCw,
  ArrowRight,
  Info
} from 'lucide-react';
import { getTransactionStatus } from '../../services/transactions';

interface TransactionStep {
  key: string;
  title: string;
  description: string;
  status: 'pending' | 'completed' | 'failed' | 'skipped';
  completedAt?: string;
  notes?: string;
  order: number;
}

interface TransactionStatusTrackerProps {
  transactionId: string;
  transactionType: 'withdraw' | 'add_money' | 'transfer' | 'payment';
  className?: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

const TransactionStatusTracker: React.FC<TransactionStatusTrackerProps> = ({
  transactionId,
  transactionType,
  className = '',
  autoRefresh = false,
  refreshInterval = 30000, // 30 seconds
}) => {
  const [steps, setSteps] = useState<TransactionStep[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  useEffect(() => {
    fetchTransactionStatus();
    
    let interval: NodeJS.Timeout;
    if (autoRefresh) {
      interval = setInterval(fetchTransactionStatus, refreshInterval);
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [transactionId, autoRefresh, refreshInterval]);

  const fetchTransactionStatus = async () => {
    try {
      setLoading(true);
      const statusData = await getTransactionStatus(transactionId);
      
      if (statusData.timeline) {
        setSteps(statusData.timeline);
      } else {
        // Create default steps based on transaction type
        setSteps(getDefaultSteps(transactionType));
      }
      
      setLastUpdated(new Date());
      setError(null);
    } catch (err) {
      setError('Failed to fetch transaction status');
      console.error('Transaction status fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const getDefaultSteps = (type: string): TransactionStep[] => {
    switch (type) {
      case 'withdraw':
        return [
          {
            key: 'initiated',
            title: 'Withdrawal Initiated',
            description: 'Your withdrawal request has been received',
            status: 'completed',
            order: 1
          },
          {
            key: 'processing',
            title: 'Processing Payment',
            description: 'We are processing your withdrawal',
            status: 'pending',
            order: 2
          },
          {
            key: 'bank_transfer',
            title: 'Bank Transfer',
            description: 'Funds are being transferred to your bank account',
            status: 'pending',
            order: 3
          },
          {
            key: 'completed',
            title: 'Transfer Complete',
            description: 'Funds have been successfully transferred',
            status: 'pending',
            order: 4
          }
        ];
      case 'add_money':
        return [
          {
            key: 'initiated',
            title: 'Deposit Initiated',
            description: 'Your deposit request has been received',
            status: 'completed',
            order: 1
          },
          {
            key: 'bank_verification',
            title: 'Bank Verification',
            description: 'Verifying your bank account details',
            status: 'pending',
            order: 2
          },
          {
            key: 'funds_transfer',
            title: 'Funds Transfer',
            description: 'Transferring funds from your bank account',
            status: 'pending',
            order: 3
          },
          {
            key: 'completed',
            title: 'Deposit Complete',
            description: 'Funds have been added to your wallet',
            status: 'pending',
            order: 4
          }
        ];
      default:
        return [
          {
            key: 'initiated',
            title: 'Transaction Initiated',
            description: 'Your transaction has been started',
            status: 'completed',
            order: 1
          },
          {
            key: 'processing',
            title: 'Processing',
            description: 'Your transaction is being processed',
            status: 'pending',
            order: 2
          },
          {
            key: 'completed',
            title: 'Completed',
            description: 'Your transaction has been completed',
            status: 'pending',
            order: 3
          }
        ];
    }
  };

  const getStepIcon = (status: TransactionStep['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'failed':
        return <XCircle className="w-5 h-5 text-red-600" />;
      case 'pending':
        return <Clock className="w-5 h-5 text-yellow-600" />;
      case 'skipped':
        return <AlertCircle className="w-5 h-5 text-gray-400" />;
      default:
        return <Clock className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStepColor = (status: TransactionStep['status']) => {
    switch (status) {
      case 'completed':
        return 'border-green-200 bg-green-50';
      case 'failed':
        return 'border-red-200 bg-red-50';
      case 'pending':
        return 'border-yellow-200 bg-yellow-50';
      case 'skipped':
        return 'border-gray-200 bg-gray-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading && steps.length === 0) {
    return (
      <div className={`bg-white rounded-lg border p-6 ${className}`}>
        <div className="flex items-center justify-center py-8">
          <RefreshCw className="w-6 h-6 animate-spin text-blue-600 mr-2" />
          <span className="text-gray-600">Loading transaction status...</span>
        </div>
      </div>
    );
  }

  if (error && steps.length === 0) {
    return (
      <div className={`bg-white rounded-lg border p-6 ${className}`}>
        <div className="text-center py-8">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Unable to Load Status</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={fetchTransactionStatus}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-medium text-gray-900">Transaction Progress</h3>
        <div className="flex items-center space-x-2">
          {lastUpdated && (
            <span className="text-xs text-gray-500">
              Updated {formatDate(lastUpdated.toISOString())}
            </span>
          )}
          <button
            onClick={fetchTransactionStatus}
            disabled={loading}
            className="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-600 hover:text-gray-900 disabled:opacity-50"
          >
            <RefreshCw className={`w-3 h-3 mr-1 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      <div className="space-y-4">
        {steps
          .sort((a, b) => a.order - b.order)
          .map((step, index) => (
            <div key={step.key} className="flex items-start space-x-4">
              {/* Step Icon */}
              <div className="flex-shrink-0">
                {getStepIcon(step.status)}
              </div>

              {/* Step Content */}
              <div className="flex-1 min-w-0">
                <div className={`rounded-lg border p-4 ${getStepColor(step.status)}`}>
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium text-gray-900">{step.title}</h4>
                    {step.completedAt && (
                      <span className="text-xs text-gray-500">
                        {formatDate(step.completedAt)}
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{step.description}</p>
                  {step.notes && (
                    <div className="mt-2 flex items-start space-x-2">
                      <Info className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" />
                      <p className="text-xs text-gray-600">{step.notes}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Connector Line */}
              {index < steps.length - 1 && (
                <div className="flex-shrink-0 w-px h-8 bg-gray-200 ml-2 mt-8" />
              )}
            </div>
          ))}
      </div>

      {autoRefresh && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-center text-xs text-gray-500">
            <RefreshCw className="w-3 h-3 mr-1" />
            Auto-refreshing every {refreshInterval / 1000} seconds
          </div>
        </div>
      )}
    </div>
  );
};

export default TransactionStatusTracker;
