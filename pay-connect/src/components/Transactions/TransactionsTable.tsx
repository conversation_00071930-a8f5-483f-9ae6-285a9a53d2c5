import React, { useState, useEffect } from 'react';
import TableRow from './TableRow';
import { getTransactions, Transaction, TransactionFilter } from '../../services/transactions';
import { ChevronLeft, ChevronRight, Download, Filter, RefreshCw } from 'lucide-react';
import EmptyState from '../common/EmptyState';
import PaymentLoader from '../common/PaymentLoader';

interface TransactionsTableProps {
  className?: string;
  search: string;
  statusFilter: string;
  typeFilter?: string;
  dateFilter?: { from?: string; to?: string };
  amountFilter?: { min?: number; max?: number };
  onExport?: () => void;
  onRefresh?: () => void;
}

const TransactionsTable: React.FC<TransactionsTableProps> = ({
  className,
  search,
  statusFilter,
  typeFilter,
  dateFilter,
  amountFilter,
  onExport,
  onRefresh,
}) => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedTransactions, setSelectedTransactions] = useState<string[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const itemsPerPage = 10;

  useEffect(() => {
    fetchTransactions();
  }, [search, statusFilter, typeFilter, dateFilter, amountFilter, currentPage]);

  const fetchTransactions = async (refresh = false) => {
    try {
      if (refresh) {
        setIsRefreshing(true);
      } else {
        setLoading(true);
      }

      const filters: TransactionFilter = {
        search,
        status: statusFilter !== 'All' ? statusFilter : undefined,
        type: typeFilter !== 'All' ? typeFilter : undefined,
        dateFrom: dateFilter?.from,
        dateTo: dateFilter?.to,
        amountMin: amountFilter?.min,
        amountMax: amountFilter?.max,
        limit: itemsPerPage,
        offset: (currentPage - 1) * itemsPerPage,
      };

      const { data, totalPages: newTotalPages } = await getTransactions(filters);
      setTransactions(data);
      setTotalPages(newTotalPages);
      setError(null);
    } catch (err) {
      setError('Failed to fetch transactions');
      console.error('Transaction fetch error:', err);
    } finally {
      setLoading(false);
      setIsRefreshing(false);
    }
  };

  const handleRefresh = () => {
    fetchTransactions(true);
    onRefresh?.();
  };

  const handleSelectTransaction = (id: string) => {
    setSelectedTransactions(prev =>
      prev.includes(id)
        ? prev.filter(txId => txId !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedTransactions.length === transactions.length) {
      setSelectedTransactions([]);
    } else {
      setSelectedTransactions(transactions.map(t => t.id));
    }
  };

  if (loading && !isRefreshing) {
    return (
       <PaymentLoader
           type="transaction"
           showQuotes={true}
         />
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-xl p-4">
        <div className="flex items-center justify-between">
          <div className="text-red-700">{error}</div>
          <button
            onClick={() => fetchTransactions()}
            className="text-red-600 hover:text-red-800 underline"
          >
            Try again
          </button>
        </div>
      </div>
    );
  }

  const startIndex = (currentPage - 1) * 10;
  const endIndex = Math.min(startIndex + 10, transactions.length);
  const currentTransactions = transactions.slice(startIndex, endIndex);

  // Check if we should show empty state
  const hasSearchOrFilter = search.trim() !== '' || statusFilter !== 'All';
  const showEmptyState = transactions.length === 0;

  if (showEmptyState) {
    return (
      <div className={`rounded-md border border-gray-200 ${className}`}>
        <EmptyState
          type={hasSearchOrFilter ? 'search' : 'transactions'}
          title={hasSearchOrFilter ? 'No transactions found' : undefined}
          description={hasSearchOrFilter ? 'Try adjusting your search criteria or filters.' : undefined}
          onAction={() => window.location.reload()}
        />
      </div>
    );
  }

  return (
    <div className={`bg-white shadow-sm rounded-lg overflow-hidden ${className}`}>
      {/* Table Header with Actions */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h3 className="text-lg font-medium text-gray-900">
              Transactions ({transactions.length})
            </h3>
            {selectedTransactions.length > 0 && (
              <span className="text-sm text-gray-500">
                {selectedTransactions.length} selected
              </span>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              
              {isRefreshing ? 'Refreshing...' : 'Refresh'}
              <RefreshCw size={16} className={`ml-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            </button>
            {onExport && (
              <button
                onClick={onExport}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
               
                Export
                 <Download size={16} className="ml-2" />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Loading Overlay */}
      {isRefreshing && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
          <div className="flex items-center space-x-2 text-blue-600">
            <RefreshCw size={20} className="animate-spin" />
            <span className="text-sm font-medium">Refreshing transactions...</span>
          </div>
        </div>
      )}

      <div className="overflow-x-auto border rounded-xl ">
        <table className="min-w-full divide-y  divide-gray-200">
          <thead className="bg-[#404040]">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <input
                  type="checkbox"
                  className="form-checkbox h-4 w-4 text-indigo-600 transition duration-150 ease-in-out"
                  checked={selectedTransactions.length === transactions.length && transactions.length > 0}
                  onChange={handleSelectAll}
                />
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Transaction ID
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Date
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Paid By / Received From
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Paid To
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Type
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Amount
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Status
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Action
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {currentTransactions.map((transaction) => (
              <TableRow
                key={transaction.id}
                transaction={transaction}
                isSelected={selectedTransactions.includes(transaction.id)}
                onSelect={handleSelectTransaction}
              />
            ))}
          </tbody>
        </table>
      </div>
      <nav
        className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"
        aria-label="Pagination"
      >
        <div className="flex-1 flex justify-between sm:justify-end items-center">
          <p className="text-sm text-gray-700 mr-4">
            Showing <span className="font-medium">{startIndex + 1}</span> to <span className="font-medium">{endIndex}</span> of {' '}
            <span className="font-medium">{transactions.length}</span> results
          </p>
          <div className="flex">
            <button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span className="sr-only">Previous</span>
              <ChevronLeft className="h-5 w-5" aria-hidden="true" />
            </button>
            <button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className="ml-3 relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span className="sr-only">Next</span>
              <ChevronRight className="h-5 w-5" aria-hidden="true" />
            </button>
          </div>
        </div>
      </nav>
    </div>
  );
};

export default TransactionsTable; 