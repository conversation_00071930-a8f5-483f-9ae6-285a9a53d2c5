import React from 'react';
import { Download } from 'lucide-react';

interface FilterAndExportControlsProps {
  className?: string;
  onFilterChange: (status: string) => void;
  onExport: () => void;
  currentFilter: string;
}

const FilterAndExportControls: React.FC<FilterAndExportControlsProps> = ({
  className, 
  onFilterChange, 
  onExport, 
  currentFilter
}) => {
  return (
    <div className={`flex items-center gap-3  ${className}`}>
      <label htmlFor="filter" className="text-gray-700  w-full md:w-1/2 text-center text-sm font-medium ">Filter By:</label>
      <select
        id="filter"
        className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
        value={currentFilter}
        onChange={(e) => onFilterChange(e.target.value)}
      >
        <option value="All">All</option>
        <option value="Pending">Pending</option>
        <option value="Received">Received</option>
        <option value="Refund">Refund</option>
        <option value="Failed">Failed</option>
        <option value="Withdrawal">Withdrawal</option>
      </select>
      <button
        onClick={onExport}
        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
      >
       
        Export
         <Download className="ml-2 h-5 w-5" aria-hidden="true" />
      </button>
    </div>
  );
};

export default FilterAndExportControls; 