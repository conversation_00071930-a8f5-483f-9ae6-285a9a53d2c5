import React from 'react';
import { Transaction } from '../../services/transactions';
import { Eye } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface TableRowProps {
  transaction: Transaction;
  isSelected?: boolean;
  onSelect?: (id: string) => void;
}

const TableRow: React.FC<TableRowProps> = ({ transaction, isSelected = false, onSelect }) => {
  const navigate = useNavigate();

  const getStatusColor = (status: Transaction['status']) => {
    switch (status) {
      case 'Received':
        return 'text-green-600';
      case 'Pending':
        return 'text-yellow-600';
      case 'Refund':
        return 'text-blue-600';
      case 'Failed':
        return 'text-red-600';
      case 'Withdrawal':
        return 'text-orange-600';
      default:
        return 'text-gray-600';
    }
  };

  const handleViewDetails = () => {
    navigate(`/transactions/${transaction.id}`);
  };

  return (
   <tr className={`hover:bg-gray-50 ${isSelected ? 'bg-blue-50' : ''}`}>
  <td className="px-6 py-4">
    <input
      type="checkbox"
      className="form-checkbox h-4 w-4 text-indigo-600 transition duration-150 ease-in-out"
      checked={isSelected}
      onChange={() => onSelect?.(transaction.id)}
    />
  </td>

  <td className="px-6 py-4 text-sm font-medium text-gray-900 max-w-[200px] whitespace-normal break-words">
    {transaction.transactionId}
  </td>

  <td className="px-6 py-4 text-sm text-gray-900 max-w-[100px] whitespace-normal break-words">
    {new Date(transaction.date).toLocaleDateString('en-US', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    })}
  </td>

  <td className="px-6 py-4 text-sm text-gray-900 max-w-[80px] whitespace-normal break-words">
    {transaction.paidByReceivedFrom}
  </td>

  <td className="px-6 py-4 text-sm text-gray-900 max-w-[80px] whitespace-normal break-words">
    {transaction.paidTo}
  </td>

  <td className="px-6 py-4 text-sm text-gray-900 max-w-[180px] whitespace-normal break-words">
    {transaction.type}
  </td>

  <td className="px-6 py-4 text-sm text-gray-900 max-w-[100px] whitespace-normal break-words">
    ${transaction.amount.toFixed(2)}
  </td>

  <td className="px-6 py-4 text-sm font-medium max-w-[100px] whitespace-normal break-words">
    <span className={getStatusColor(transaction.status)}>
      {transaction.status}
    </span>
  </td>

  <td className="px-6 py-4 text-center text-sm font-medium max-w-[60px] whitespace-normal break-words">
    <button
      onClick={handleViewDetails}
      className="text-orange-500 hover:text-orange-500   ml-3 mt-1 p-2 rounded-full bg-orange-50 hover:bg-orange-100  text-orange-500 transition"
    >
      <Eye size={18} />
    </button>
  </td>
</tr>

  );
};

export default TableRow; 