import React, { useState } from 'react';
import { 
  Filter, 
  X, 
  Calendar, 
  DollarSign, 
  Tag, 
  CheckCircle,
  Clock,
  XCircle,
  AlertCircle,
  Download
} from 'lucide-react';
import { TransactionFilter } from '../../services/transactions';

interface TransactionFiltersProps {
  filters: TransactionFilter;
  onFiltersChange: (filters: TransactionFilter) => void;
  onExport?: () => void;
  className?: string;
}

const TransactionFilters: React.FC<TransactionFiltersProps> = ({
  filters,
  onFiltersChange,
  onExport,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const statusOptions = [
    { value: 'All', label: 'All Status', icon: Filter, color: 'text-gray-600' },
    { value: 'Received', label: 'Completed', icon: CheckCircle, color: 'text-green-600' },
    { value: 'Pending', label: 'Pending', icon: Clock, color: 'text-yellow-600' },
    { value: 'Failed', label: 'Failed', icon: XCircle, color: 'text-red-600' },
    { value: 'Refund', label: 'Refunded', icon: AlertCircle, color: 'text-blue-600' }
  ];

  const typeOptions = [
    { value: 'All', label: 'All Types' },
    { value: 'add_money', label: 'Add Money' },
    { value: 'withdraw', label: 'Withdraw' },
    { value: 'payment_sent', label: 'Payment Sent' },
    { value: 'payment_received', label: 'Payment Received' },
    { value: 'transfer', label: 'Transfer' },
    { value: 'refund', label: 'Refund' }
  ];

  const handleFilterChange = (key: keyof TransactionFilter, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value === 'All' ? undefined : value
    });
  };

  const handleDateRangeChange = (type: 'from' | 'to', value: string) => {
    onFiltersChange({
      ...filters,
      [type === 'from' ? 'dateFrom' : 'dateTo']: value
    });
  };

  const handleAmountRangeChange = (type: 'min' | 'max', value: string) => {
    const numValue = value ? parseFloat(value) : undefined;
    onFiltersChange({
      ...filters,
      [type === 'min' ? 'amountMin' : 'amountMax']: numValue
    });
  };

  const clearFilters = () => {
    onFiltersChange({
      limit: filters.limit,
      offset: filters.offset
    });
  };

  const hasActiveFilters = () => {
    return !!(filters.status || filters.type || filters.search || 
              filters.dateFrom || filters.dateTo || 
              filters.amountMin || filters.amountMax);
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.status) count++;
    if (filters.type) count++;
    if (filters.search) count++;
    if (filters.dateFrom || filters.dateTo) count++;
    if (filters.amountMin || filters.amountMax) count++;
    return count;
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg ${className}`}>
      {/* Filter Header */}
      <div className="px-4 py-3 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
          
            <h3 className="text-sm font-medium text-gray-900">Filters</h3>
            {hasActiveFilters() && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {getActiveFilterCount()} active
              </span>
            )}
              <Filter size={20} className="text-gray-500" />
          </div>
          <div className="flex items-center space-x-2">
            {hasActiveFilters() && (
              <button
                onClick={clearFilters}
                className="text-sm text-gray-500 hover:text-gray-700"
              >
                Clear all
              </button>
            )}
            {onExport && (
              <button
                onClick={onExport}
                className="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                
                Export
                <Download size={14} className="ml-1" />
              </button>
            )}
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              {isExpanded ? 'Less' : 'More'} filters
            </button>
          </div>
        </div>
      </div>

      {/* Quick Filters */}
      <div className="px-4 py-3">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Status Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              value={filters.status || 'All'}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              {statusOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Type Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
            <select
              value={filters.type || 'All'}
              onChange={(e) => handleFilterChange('type', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              {typeOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Search */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Search</label>
            <input
              type="text"
              placeholder="Transaction ID, description..."
              value={filters.search || ''}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>
        </div>
      </div>

      {/* Advanced Filters */}
      {isExpanded && (
        <div className="px-4 py-3 border-t border-gray-200 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Date Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Calendar size={16} className="inline mr-1" />
                Date Range
              </label>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="block text-xs text-gray-500 mb-1">From</label>
                  <input
                    type="date"
                    value={filters.dateFrom || ''}
                    onChange={(e) => handleDateRangeChange('from', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-500 mb-1">To</label>
                  <input
                    type="date"
                    value={filters.dateTo || ''}
                    onChange={(e) => handleDateRangeChange('to', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
              </div>
            </div>

            {/* Amount Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <DollarSign size={16} className="inline mr-1" />
                Amount Range
              </label>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="block text-xs text-gray-500 mb-1">Min ($)</label>
                  <input
                    type="number"
                    placeholder="0.00"
                    value={filters.amountMin || ''}
                    onChange={(e) => handleAmountRangeChange('min', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-500 mb-1">Max ($)</label>
                  <input
                    type="number"
                    placeholder="1000.00"
                    value={filters.amountMax || ''}
                    onChange={(e) => handleAmountRangeChange('max', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Active Filters Display */}
      {hasActiveFilters() && (
        <div className="px-4 py-3 border-t border-gray-200">
          <div className="flex flex-wrap gap-2">
            {filters.status && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Status: {statusOptions.find(s => s.value === filters.status)?.label}
                <button
                  onClick={() => handleFilterChange('status', undefined)}
                  className="ml-1 text-blue-600 hover:text-blue-800"
                >
                  <X size={12} />
                </button>
              </span>
            )}
            {filters.type && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Type: {typeOptions.find(t => t.value === filters.type)?.label}
                <button
                  onClick={() => handleFilterChange('type', undefined)}
                  className="ml-1 text-green-600 hover:text-green-800"
                >
                  <X size={12} />
                </button>
              </span>
            )}
            {filters.search && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                Search: "{filters.search}"
                <button
                  onClick={() => handleFilterChange('search', undefined)}
                  className="ml-1 text-purple-600 hover:text-purple-800"
                >
                  <X size={12} />
                </button>
              </span>
            )}
            {(filters.dateFrom || filters.dateTo) && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                Date: {filters.dateFrom || '...'} to {filters.dateTo || '...'}
                <button
                  onClick={() => {
                    handleDateRangeChange('from', '');
                    handleDateRangeChange('to', '');
                  }}
                  className="ml-1 text-yellow-600 hover:text-yellow-800"
                >
                  <X size={12} />
                </button>
              </span>
            )}
            {(filters.amountMin || filters.amountMax) && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                Amount: ${filters.amountMin || '0'} - ${filters.amountMax || '∞'}
                <button
                  onClick={() => {
                    handleAmountRangeChange('min', '');
                    handleAmountRangeChange('max', '');
                  }}
                  className="ml-1 text-red-600 hover:text-red-800"
                >
                  <X size={12} />
                </button>
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default TransactionFilters;
