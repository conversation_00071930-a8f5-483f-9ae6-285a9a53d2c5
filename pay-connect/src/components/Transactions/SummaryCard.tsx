import React from 'react';
import { CreditCard, ArrowDownCircle, ArrowUpCircle } from 'lucide-react';

interface SummaryCardProps {
  title: string;
  value: string;
}

const SummaryCard: React.FC<SummaryCardProps> = ({ title, value }) => {
  const getIcon = () => {
    switch (title) {
      case 'Total Payment Received':
        return <ArrowDownCircle size={24} className="text-white" />;
      case 'Pending Payments':
        return <CreditCard size={24} className="text-gray-600" />;
      case 'Refund Payments':
        return <ArrowUpCircle size={24} className="text-green-600" />;
      case 'Total Withdrawals':
        return <ArrowUpCircle size={24} className="text-blue-600" />;
      default:
        return null;
    }
  };

  const isPaymentReceived = title === 'Total Payment Received';

  return (
    <div
      className={`w-full h-36 rounded-[20px] shadow-sm border  hover:shadow-md hover:border-gray-300  p-5 flex items-center justify-between ${isPaymentReceived ? '' : 'bg-white'
        }`}
      style={{
        background: isPaymentReceived
          ? '#2F80ED   '
          :'hover:shadow-md hover:border-blue-300',
      }}
    >
      {/* <div className="flex items-center  justify-between space-x-4"> */}

      <div>
        <p
          className={`text-2xl font-bold ${isPaymentReceived ? 'text-white' : 'text-gray-900'
            }`}
        >
          {value}
        </p>
        <p className={`${isPaymentReceived ? 'text-white' : 'text-gray-500'}`}>
          {title}
        </p>
      </div>

      <div
        className={`w-12 h-12 rounded-full flex items-center justify-center ${isPaymentReceived ? 'bg-white/30' : 'bg-gray-100'
          }`}
      >
        {getIcon()}
      </div>

    </div>
    // </div>
  );
};

export default SummaryCard;
