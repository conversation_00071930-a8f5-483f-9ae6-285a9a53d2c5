import React from 'react';
import { Loader2 } from 'lucide-react';

interface SpinnerLoaderProps {
  size?: number | string; // e.g., 24, '2rem', etc.
  className?: string;
}

const SpinnerLoader: React.FC<SpinnerLoaderProps> = ({ size = 32, className = '' }) => (
  <div className={`flex items-center justify-center ${className}`}>
    <Loader2
      size={size}
      className="animate-spin text-gray-white"
      aria-label="Loading..."
    />
  </div>
);

export default SpinnerLoader;
