import React, { useState, useRef } from 'react';
import { <PERSON>, EyeOff, ArrowRight } from 'lucide-react';
import { calculatePayment } from '../../utils/paymentUtils';

interface SetupPinProps {
  onPinSet: () => void;
}

const SetupPin: React.FC<SetupPinProps> = ({ onPinSet }) => {
  const [pin, setPin] = useState(['', '', '', '']);
  const [confirmPin, setConfirmPin] = useState(['', '', '', '']);
  const [showPin, setShowPin] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);
  const [step, setStep] = useState(1); // 1: enter, 2: confirm
  const [error, setError] = useState('');
  const pinInputs = [useRef(null), useRef(null), useRef(null), useRef(null)];
  const confirmInputs = [useRef(null), useRef(null), useRef(null), useRef(null)];

  const handlePinChange = (idx: number, value: string) => {
    if (!/^[0-9]?$/.test(value)) return;
    const newPin = [...pin];
    newPin[idx] = value;
    setPin(newPin);
    setError('');
    if (value && idx < 3) {
      (pinInputs[idx + 1].current as any)?.focus();
    }
    if (!value && idx > 0) {
      (pinInputs[idx - 1].current as any)?.focus();
    }
  };
  const handlePinKeyDown = (idx: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Backspace' && !pin[idx] && idx > 0) {
      (pinInputs[idx - 1].current as any)?.focus();
    }
  };
  const handleConfirmChange = (idx: number, value: string) => {
    if (!/^[0-9]?$/.test(value)) return;
    const newPin = [...confirmPin];
    newPin[idx] = value;
    setConfirmPin(newPin);
    setError('');
    if (value && idx < 3) {
      (confirmInputs[idx + 1].current as any)?.focus();
    }
    if (!value && idx > 0) {
      (confirmInputs[idx - 1].current as any)?.focus();
    }
  };
  const handleConfirmKeyDown = (idx: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Backspace' && !confirmPin[idx] && idx > 0) {
      (confirmInputs[idx - 1].current as any)?.focus();
    }
  };
  const handleContinue = () => {
    if (pin.some(p => p === '')) {
      setError('Please enter your 4-digit PIN');
      return;
    }
    setStep(2);
  };
  const handleSetPin = () => {
    if (confirmPin.some(p => p === '')) {
      setError('Please confirm your 4-digit PIN');
      return;
    }
    if (pin.join('') !== confirmPin.join('')) {
      setError('PINs do not match');
      return;
    }
    // Save PIN (mock: localStorage)
    localStorage.setItem('userPin', pin.join(''));
    onPinSet();
  };

  const result = calculatePayment({
    items: [
      { label: 'Game Charges', value: 800 },
      { label: 'Payconnect Charges', value: 8 },
    ],
    taxPercent: 1.25,
    discountPercent: 0,
    additionalFees: [{ label: 'Service Fee', value: 10 }],
  });

  const breakdown = calculatePayment({
    items: [
      { label: 'Gross Amount', value: result.total },
      { label: 'Pay connect(1%)', value: result.total * 0.01 },
      { label: 'Tax', value: result.tax },
    ],
  });

  return (
    <div className="max-w-xs mx-auto py-10">
      <div className="font-semibold text-gray-900 text-lg mb-2">Set up your 4-digit PIN</div>
      <div className="text-xs text-gray-500 mb-6">For your security, please create a 4-digit transaction PIN.</div>
      {/* Step 1: Enter PIN */}
      {step === 1 && (
        <>
          <div className="flex space-x-3 justify-center mb-2 relative">
            {[0, 1, 2, 3].map((idx) => (
              <input
                key={idx}
                ref={pinInputs[idx]}
                type={showPin ? 'text' : 'password'}
                inputMode="numeric"
                maxLength={1}
                className={`w-12 h-12 rounded-lg border text-center text-2xl font-mono bg-gray-50 focus:ring-2 focus:ring-red-400 focus:border-red-400 transition ${error ? 'border-red-400' : 'border-gray-300'}`}
                value={pin[idx]}
                onChange={e => handlePinChange(idx, e.target.value)}
                onKeyDown={e => handlePinKeyDown(idx, e)}
                autoFocus={idx === 0}
              />
            ))}
            <button
              type="button"
              aria-label={showPin ? 'Hide PIN' : 'Show PIN'}
              onClick={() => setShowPin((v) => !v)}
              className="absolute right-[-40px] top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none"
              tabIndex={-1}
            >
              {showPin ? <EyeOff size={22} /> : <Eye size={22} />}
            </button>
          </div>
          {error && <div className="text-xs text-red-600 text-center mb-2">{error}</div>}
          <button
            onClick={handleContinue}
            disabled={pin.some(p => p === '')}
            className="w-full bg-red-500 text-white py-3 px-4 rounded-lg font-semibold hover:bg-red-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2 text-base mt-4"
          >
            <span>Continue</span>
            <ArrowRight size={20} />
          </button>
        </>
      )}
      {/* Step 2: Confirm PIN */}
      {step === 2 && (
        <>
          <div className="font-semibold text-gray-900 text-base mb-1 text-center">Confirm your 4-digit PIN</div>
          <div className="flex space-x-3 justify-center mb-2 relative mt-4">
            {[0, 1, 2, 3].map((idx) => (
              <input
                key={idx}
                ref={confirmInputs[idx]}
                type={showConfirm ? 'text' : 'password'}
                inputMode="numeric"
                maxLength={1}
                className={`w-12 h-12 rounded-lg border text-center text-2xl font-mono bg-gray-50 focus:ring-2 focus:ring-red-400 focus:border-red-400 transition ${error ? 'border-red-400' : 'border-gray-300'}`}
                value={confirmPin[idx]}
                onChange={e => handleConfirmChange(idx, e.target.value)}
                onKeyDown={e => handleConfirmKeyDown(idx, e)}
                autoFocus={idx === 0}
              />
            ))}
            <button
              type="button"
              aria-label={showConfirm ? 'Hide PIN' : 'Show PIN'}
              onClick={() => setShowConfirm((v) => !v)}
              className="absolute right-[-40px] top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none"
              tabIndex={-1}
            >
              {showConfirm ? <EyeOff size={22} /> : <Eye size={22} />}
            </button>
          </div>
          {error && <div className="text-xs text-red-600 text-center mb-2">{error}</div>}
          <button
            onClick={handleSetPin}
            disabled={confirmPin.some(p => p === '')}
            className="w-full bg-red-500 text-white py-3 px-4 rounded-lg font-semibold hover:bg-red-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2 text-base mt-4"
          >
            <span>Set PIN</span>
            <ArrowRight size={20} />
          </button>
        </>
      )}
      <div className="mt-4">
        {breakdown.breakdown.map(item => (
          <React.Fragment key={item.label}>
            <p>{item.label}:</p>
            <p className="text-right">${item.value}</p>
          </React.Fragment>
        ))}
        <p className="text-lg font-bold">Total:</p>
        <p className="text-lg font-bold text-right">${breakdown.total}</p>
      </div>
    </div>
  );
};

export default SetupPin; 