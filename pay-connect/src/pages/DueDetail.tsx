import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { getDueById, type Due } from '../services/dues';
import Breadcrumb from '../components/common/Breadcrumb';

const DueDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [due, setDue] = useState<Due | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      fetchDueDetails(id);
    }
  }, [id]);

  const fetchDueDetails = async (dueId: string) => {
    try {
      setLoading(true);
      const data = await getDueById(dueId);
      setDue(data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch due details');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      </div>
    );
  }

  if (!due) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-gray-600">Due item not found.</div>
      </div>
    );
  }

  const breadcrumbItems = [
    { label: 'Dashboard', path: '/dashboard' },
    { label: 'My Dues', path: '/dues' },
    { label: due. firstname + ' - ' + due.game_title },
  ];

  const getStatusColor = (status: Due['status']) => {
    switch (status) {
      case 'Paid':
        return 'text-green-600 bg-green-100';
      case 'Pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'Overdue':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="space-y-6">
      <Breadcrumb items={breadcrumbItems} />

      <div className="bg-white rounded-xl shadow-sm border ">
       <div className='bg-[#2F94ED] rounded-t-xl'>
         <h2 className="text-2xl font-bold   p-6   text-white mb-6">Due Details: {due. firstname} - {due.game_title}</h2>
       </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-y-4 gap-x-6 text-gray-700 p-6">
          <div>
            <p className="text-sm font-medium text-gray-500">Payer Name</p>
            <p className="text-lg font-semibold border-b border-dashed border-gray-300">{due. firstname}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Email</p>
            <p className="text-lg font-semibold border-b border-dashed border-gray-300">{due.email}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Contact</p>
            <p className="text-lg font-semibold border-b border-dashed border-gray-300">{due.contact}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">League</p>
            <p className="text-lg font-semibold border-b border-dashed border-gray-300">{due.game_title}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Season</p>
            <p className="text-lg font-semibold border-b border-dashed border-gray-300">{due.season_name}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Amount</p>
            <p className="text-lg font-semibold border-b border-dashed border-gray-300">${due.amount.toFixed(2)}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Due Date</p>
            <p className="text-lg font-semibold border-b border-dashed border-gray-300">{new Date(due.end_date).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500">Status</p>
            <span className={`px-3 py-1 rounded-full text-sm font-semibold ${getStatusColor(due.status)}`}>
              {due.status}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DueDetail; 