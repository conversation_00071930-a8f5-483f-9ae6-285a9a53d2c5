import React, { useState, useEffect } from 'react';
import SummaryCard from '../components/Transactions/SummaryCard';
import TransactionsTable from '../components/Transactions/TransactionsTable';
import TransactionFilters from '../components/Transactions/TransactionFilters';
import {
  getTransactionSummary,
  exportAndDownloadTransactions,
  TransactionFilter,
  TransactionSummary,
  // debugTransactionAPI
} from '../services/transactions';
import PaymentLoader from '../components/common/PaymentLoader';
import { RefreshCw, Download, TrendingUp } from 'lucide-react';

const Transactions: React.FC = () => {
  const [filters, setFilters] = useState<TransactionFilter>({
    limit: 10,
    offset: 0
  });
  const [summary, setSummary] = useState<TransactionSummary>({
    totalPaymentReceived: 0,
    pendingPayments: 0,
    refundPayments: 0,
    totalWithdrawals: 0,
    totalDeposits: 0,
    transactionCount: 0
  });
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchSummary();
  }, []);

  const fetchSummary = async () => {
    try {
      setLoading(true);
      const data = await getTransactionSummary();
      setSummary(data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch transaction summary');
      console.error('Summary fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      await fetchSummary();
    } finally {
      setRefreshing(false);
    }
  };

  const handleExport = async (format: 'csv' | 'json' = 'csv') => {
    try {
      setExporting(true);
      await exportAndDownloadTransactions(format, filters);
    } catch (err) {
      console.error('Export error:', err);
      alert('Failed to export transactions. Please try again.');
    } finally {
      setExporting(false);
    }
  };

  const handleFiltersChange = (newFilters: TransactionFilter) => {
    setFilters(newFilters);
  };

  // const handleDebugAPI = async () => {
  //   console.log('🔍 Running API debug test...');
  //   const result = await debugTransactionAPI();

  //   if (result.success) {
  //     alert('✅ API is working! Check console for details.');
  //   } else {
  //     alert(`❌ API Error: ${result.error}\n\nCheck console for more details.`);
  //   }
  // };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <PaymentLoader
          type="transaction"
          message="Loading transaction history..."
          size="large"
          showQuotes={true}
        />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      </div>
    );
  }

  return (
    <div className=" ">
      <div className=" ">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Transactions</h1>
              <p className="text-gray-600">Manage and track all your payment transactions</p>
            </div>
            <div className="flex items-center space-x-3">
              {/* <button
                onClick={handleDebugAPI}
                className="inline-flex items-center px-3 py-2 border border-yellow-300 shadow-sm text-sm font-medium rounded-md text-yellow-700 bg-yellow-50 hover:bg-yellow-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
              >
                🔍 Debug API
              </button> */}
              <button
                onClick={handleRefresh}
                disabled={refreshing}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
               
                {refreshing ? 'Refreshing...' : 'Refresh'}
                 <RefreshCw size={16} className={`ml-2 ${refreshing ? 'animate-spin' : ''}`} />
              </button>
              <button
                onClick={() => handleExport('csv')}
                disabled={exporting}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
              
                {exporting ? 'Exporting...' : 'Export'}
                  <Download size={16} className="ml-2" />
              </button>
            </div>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <SummaryCard
            title="Total Payment Received"
            value={`$${summary.totalPaymentReceived.toFixed(2)}`}
          />
          <SummaryCard
            title="Pending Payments"
            value={`$${summary.pendingPayments.toFixed(2)}`}
          />
          <SummaryCard
            title="Total Withdrawals"
            value={`$${summary.totalWithdrawals.toFixed(2)}`}
          />
          <div className="text-gray-500 bg-white  hover:shadow-md hover:border-gray-300 rounded-[20px] shadow-sm border p-6">
            <div className="flex items-center justify-between">
              <div>
              
                <p className="text-2xl font-bold text-gray-900">{summary.transactionCount}</p>
                  <p className="text-sm font-medium text-gray-500">Total Transactions</p>
              </div>
              <div className="flex items-center text-green-600">
                <TrendingUp size={20} />
              </div>
            </div>
          </div>
        </div>


        {/* Filters */}
        <div className="mb-6">
          <TransactionFilters
            filters={filters}
            onFiltersChange={handleFiltersChange}
            onExport={() => handleExport('csv')}
          />
        </div>

        {/* Transactions Table */}
        <TransactionsTable
          search={filters.search || ''}
          statusFilter={filters.status || 'All'}
          typeFilter={filters.type}
          dateFilter={{
            from: filters.dateFrom,
            to: filters.dateTo
          }}
          amountFilter={{
            min: filters.amountMin,
            max: filters.amountMax
          }}
          onExport={() => handleExport('csv')}
          onRefresh={handleRefresh}
        />
      </div>
    </div>
  );
};

export default Transactions;