import React, { useState, useEffect } from 'react';
import { Settings as SettingsIcon, RotateCcw, Loader2 } from 'lucide-react';
import PaymentRolesSettings from '../components/Settings/PaymentRolesSettings';
import PaymentSettings from '../components/Settings/PaymentSettings';
import NotificationSettings from '../components/Settings/NotificationSettings';
import SecuritySettings from '../components/Settings/SecuritySettings';
import {
  AppSettings,
  PaymentRole,
  PaymentSettings as PaymentSettingsType,
  NotificationSettings as NotificationSettingsType,
  SecuritySettings as SecuritySettingsType,
  getAppSettings,
  updatePaymentRoles,
  updatePaymentSettings,
  updateNotificationSettings,
  updateSecuritySettings,
  resetToDefaults,
} from '../services/settings';
import PaymentLoader from '../components/common/PaymentLoader';
import ConfirmModal from '../components/common/ConfirmModal';

const Settings: React.FC = () => {
  const [settings, setSettings] = useState<AppSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('payment-roles');
  const [error, setError] = useState<string | null>(null);
  const [showResetModal, setShowResetModal] = useState(false);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setIsLoading(true);
      setError(null);
      // Add artificial delay to show the engaging loader
      await new Promise(resolve => setTimeout(resolve, 2000));
      const data = await getAppSettings();
      setSettings(data);
    } catch (err) {
      console.error('Error fetching settings:', err);
      setError('Failed to load settings. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdatePaymentRoles = async (roles: PaymentRole[]) => {
    if (!settings) return;

    setIsSaving(true);
    try {
      const result = await updatePaymentRoles(roles);
      if (result.success) {
        setSettings({ ...settings, paymentRoles: roles });
        alert(result.message);
      } else {
        alert(result.message);
      }
    } catch {
      alert('Failed to update payment roles');
    } finally {
      setIsSaving(false);
    }
  };

  const handleUpdatePaymentSettings = async (paymentSettings: PaymentSettingsType) => {
    if (!settings) return;

    setIsSaving(true);
    try {
      const result = await updatePaymentSettings(paymentSettings);
      if (result.success) {
        setSettings({ ...settings, paymentSettings });
        alert(result.message);
      } else {
        alert(result.message);
      }
    } catch {
      alert('Failed to update payment settings');
    } finally {
      setIsSaving(false);
    }
  };

  const handleUpdateNotificationSettings = async (notificationSettings: NotificationSettingsType) => {
    if (!settings) return;

    setIsSaving(true);
    try {
      const result = await updateNotificationSettings(notificationSettings);
      if (result.success) {
        setSettings({ ...settings, notificationSettings });
        alert(result.message);
      } else {
        alert(result.message);
      }
    } catch {
      alert('Failed to update notification settings');
    } finally {
      setIsSaving(false);
    }
  };

  const handleUpdateSecuritySettings = async (securitySettings: SecuritySettingsType) => {
    if (!settings) return;

    setIsSaving(true);
    try {
      const result = await updateSecuritySettings(securitySettings);
      if (result.success) {
        setSettings({ ...settings, securitySettings });
        alert(result.message);
      } else {
        alert(result.message);
      }
    } catch {
      alert('Failed to update security settings');
    } finally {
      setIsSaving(false);
    }
  };

  const handleResetToDefaults = async () => {
    setShowResetModal(false);
    setIsSaving(true);
    try {
      const result = await resetToDefaults();
      if (result.success) {
        await fetchSettings(); // Reload settings
        alert(result.message);
      } else {
        alert(result.message);
      }
    } catch (error) {
      alert('Failed to reset settings');
    } finally {
      setIsSaving(false);
    }
  };

  const tabs = [
    { id: 'payment-roles', label: 'Payment Roles', icon: '👥', disabled: false },
    { id: 'payment-settings', label: 'Payment Settings', icon: '💳', disabled: false },
    { id: 'notifications', label: 'Notifications', icon: '🔔', disabled: false },
    { id: 'security', label: 'Security', icon: '🔒', disabled: false },
  ];

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12 [min-height:765px]">
          <PaymentLoader
            type="setup"
            message="Loading application settings..."
            size="large"
            showQuotes={true}
          />
        </div>
      </div>
    );
  }

  if (error || !settings) {
    return (
      <div className="space-y-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <p className="text-red-600 mb-4">{error || 'Failed to load settings'}</p>
          <button
            onClick={fetchSettings}
            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white mb-7">
        <div className="flex items-center justify-between">
          <div className="">
            
            <div className="flex items-center space-x-3">
              <h2 className="text-2xl font-bold text-gray-900">Settings</h2>
               <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
              <SettingsIcon className="w-5 h-5 text-gray-600" />
            </div>
           
            </div>
              <p className="text-gray-500">Configure your application preferences and security</p>
          </div>
          <button
            onClick={() => setShowResetModal(true)}
            disabled={isSaving}
            className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:opacity-50 transition-colors"
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            Reset to Defaults
          </button>
        </div>
      </div>

      {/* Statistics Section */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold">4</div>
          <div className="text-gray-500">Total Settings Tabs</div>
        </div>
        <div className="bg-green-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold">3</div>
          <div className="text-gray-500">Admins</div>
        </div>
        <div className="bg-yellow-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold">12</div>
          <div className="text-gray-500">Notifications Sent</div>
        </div>
        <div className="bg-red-50 p-4 rounded-lg text-center">
          <div className="text-2xl font-bold">2</div>
          <div className="text-gray-500">Security Alerts</div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-xl shadow-sm border">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            {tabs.map((tab) => (
              <button
                disabled={tab.disabled}
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
                <span className="ml-2">{tab.icon}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'payment-roles' && (
            settings.paymentRoles ? (
              <PaymentRolesSettings
                roles={settings.paymentRoles}
                onUpdate={handleUpdatePaymentRoles}
                isLoading={isSaving}
              />
            ) : (
              <div className="text-gray-500">No payment roles found. (Static: Admin, Manager, User)</div>
            )
          )}

          {activeTab === 'payment-settings' && (
            settings.paymentSettings ? (
              <PaymentSettings
                settings={settings.paymentSettings}
                onUpdate={handleUpdatePaymentSettings}
                isLoading={isSaving}
              />
            ) : (
              <div className="text-gray-500">No payment settings found. (Static: Max transaction: $1000, Currency: USD)</div>
            )
          )}

          {activeTab === 'notifications' && (
            settings.notificationSettings ? (
              <NotificationSettings
                settings={settings.notificationSettings}
                onUpdate={handleUpdateNotificationSettings}
                isLoading={isSaving}
              />
            ) : (
              <div className="text-gray-500">No notification settings found. (Static: Email: Enabled, SMS: Disabled)</div>
            )
          )}

          {activeTab === 'security' && (
            settings.securitySettings ? (
              <SecuritySettings
                settings={settings.securitySettings}
                onUpdate={handleUpdateSecuritySettings}
                isLoading={isSaving}
              />
            ) : (
              <div className="text-gray-500">No security settings found. (Static: 2FA: Enabled, Last password change: 10 days ago)</div>
            )
          )}
        </div>
      </div>
      {/* Reset Confirmation Modal */}
      <ConfirmModal
        isOpen={showResetModal}
        onClose={() => setShowResetModal(false)}
        onConfirm={handleResetToDefaults}
        title="Reset All Settings"
        message="Are you sure you want to reset all settings to their default values? This action cannot be undone."
        confirmText="Reset"
        cancelText="Cancel"
        isLoading={isSaving}
      />
    </div>
  );
};

export default Settings;