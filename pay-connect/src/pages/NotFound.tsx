import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Home, RefreshCw, Search, Coffee } from 'lucide-react';

const NotFound: React.FC = () => {
  const navigate = useNavigate();
  const [currentTextIndex, setCurrentTextIndex] = useState(0);

  // Array of sarcastic/humorous texts to keep users entertained
  const sarcasticTexts = [
    "Well, this is awkward. The page you're looking for decided to take a vacation without telling anyone.",
    "404: Page not found. But hey, at least you found this delightfully unhelpful message!",
    "Congratulations! You've discovered our secret 404 page. Unfortunately, that's all you've discovered.",
    "The page you're looking for is playing hide and seek. Spoiler alert: it's really good at hiding.",
    "Error 404: Page not found. Our developers are probably debugging this with coffee and tears.",
    "Oops! Looks like this page went on a coffee break and forgot to come back.",
    "This page is like my motivation on Monday morning - nowhere to be found.",
    "404: The page you're looking for is in another castle. Wait, wrong game.",
    "We've looked everywhere for this page. Under the couch, in the fridge, even asked the cat. Nothing.",
    "This page has gone to live on a farm upstate where all the missing pages go.",
    "Error 404: Page not found. But look on the bright side - you're not lost, you're exploring!",
    "The page you requested has been abducted by aliens. We're negotiating for its return.",
    "404: This page is currently social distancing from the internet.",
    "Roses are red, violets are blue, 404 error, this page we can't find for you.",
    "This page is like a unicorn - everyone talks about it, but nobody can find it.",
    "The page you're looking for is probably stuck in traffic. Try again in 5 minutes.",
    "404: Page not found. It's probably binge-watching Netflix instead of doing its job.",
    "This page went to get milk and never came back.",
    "Error 404: The page you're looking for is currently updating its LinkedIn profile.",
    "We'd show you the page you want, but it's currently in a meeting that could have been an email."
  ];

  // Show a random sarcastic text on component mount
  useEffect(() => {
    const randomIndex = Math.floor(Math.random() * sarcasticTexts.length);
    setCurrentTextIndex(randomIndex);
  }, [sarcasticTexts.length]);

  const handleGoHome = () => {
    navigate('/dashboard', { replace: true });
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl w-full space-y-8">
        <div className="text-center">
          {/* 404 Large Text */}
          <div className="mb-8">
            <h1 className="text-9xl font-bold text-blue-600 opacity-20 select-none p-6">
              404
            </h1>
            <div className="-mt-16">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                {/* Hmm. */}
              </h2>
              <p className="text-lg text-blue-600 mb-6">
                {/* It seems that you're lost in a perpetual black hole. Let us help guide you out and get you back home. */}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-2xl shadow-xl p-6 sm:p-8">
          {/* Rotating Sarcastic Text */}
          <div className="text-center mb-8">
            <div className="p-6 bg-blue-50 rounded-lg border border-blue-200 min-h-[120px] flex items-center justify-center">
              <div className="flex items-center justify-center space-x-3 text-blue-800">
                <Coffee className="w-6 h-6 flex-shrink-0" />
                <p className="text-sm font-medium leading-relaxed">
                  {sarcasticTexts[currentTextIndex]}
                </p>
              </div>
            </div>

          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <button
              onClick={handleGoHome}
              className="w-full flex items-center justify-center space-x-2 py-3 px-4 border border-transparent text-base font-medium rounded-xl text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              <Home className="w-5 h-5" />
              <span>Take Me Home</span>
            </button>
            
            <div className="grid grid-cols-2 gap-3">
              <button
                onClick={handleGoBack}
                className="flex items-center justify-center space-x-2 py-3 px-4 border border-gray-300 text-base font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
              >
                <Search className="w-5 h-5" />
                <span>Go Back</span>
              </button>
              
              <button
                onClick={handleRefresh}
                className="flex items-center justify-center space-x-2 py-3 px-4 border border-gray-300 text-base font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
              >
                <RefreshCw className="w-5 h-5" />
                <span>Refresh</span>
              </button>
            </div>
          </div>
        </div>

        {/* Footer Text */}
        <div className="text-center">
          <p className="text-sm text-gray-500">
            Don't worry, even the best explorers get lost sometimes. 
            <br />
            <span className="text-blue-600 font-medium">Pro tip:</span> Try checking the URL for typos!
          </p>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
