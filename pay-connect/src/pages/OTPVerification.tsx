import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Shield, Mail } from 'lucide-react';
import { verifyOTP as verifyOTPAPI, resendOTP as resendOTPAPI, storeUserData, storeAuthToken } from '../services/authService';
import { useAuth } from '../contexts/AuthContext';
import logoImage from '../assets/images/giu_pay_connect.png';
import PaymentLoader from '../components/common/PaymentLoader';

const OTPVerification: React.FC = () => {
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [error, setError] = useState('');
  const [isResending, setIsResending] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [email, setEmail] = useState('');
  const navigate = useNavigate();
  const location = useLocation();
  const { refreshUser } = useAuth();

  // Note: We'll use page reload to trigger AuthContext to reload user from localStorage

  useEffect(() => {
    if (!location.state?.email) {
      console.log('No email found in state. Navigating to login...');
      // navigate('/login');
    } else {
      setEmail(location.state.email);
    }
  }, [location.state]);

  const maskEmail = useCallback((email: string) => {
    const [localPart, domain] = email.split('@');
    const visibleChars = Math.ceil(localPart.length * 0.2);
    const maskedChars = localPart.length - visibleChars;
    const maskedSection = '*'.repeat(maskedChars);
    const visibleSection = localPart.slice(-visibleChars);
    return `${maskedSection}${visibleSection}@${domain}`;
  }, []);

  const handleOtpChange = useCallback((index: number, value: string) => {
    if (!/^\d*$/.test(value)) return;

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);
    setError('');

    // Auto-focus next input
    if (value && index < 5) {
      const nextInput = document.querySelector(`input[name=otp-${index + 1}]`) as HTMLInputElement;
      if (nextInput) nextInput.focus();
    } else if (!value && index > 0) {
      const prevInput = document.querySelector(`input[name=otp-${index - 1}]`) as HTMLInputElement;
      if (prevInput) prevInput.focus();
    }
  }, [otp]);

  const handlePaste = useCallback((e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').trim();

    // Check if pasted data is exactly 6 digits
    if (/^\d{6}$/.test(pastedData)) {
      const digits = pastedData.split('');
      setOtp(digits);
      setError('');

      // Focus the last input and trigger verification if all fields are filled
      setTimeout(() => {
        const lastInput = document.querySelector(`input[name=otp-5]`) as HTMLInputElement;
        if (lastInput) lastInput.focus();
      }, 0);
    } else if (/^\d+$/.test(pastedData) && pastedData.length !== 6) {
      // Show error for invalid length but valid digits
      setError('Please paste a valid 6-digit OTP code');
    } else if (pastedData.length > 0) {
      // Show error for non-numeric paste
      setError('Please paste only numeric digits');
    }
  }, []);

  const handleKeyDown = useCallback((index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    // Handle backspace
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      const prevInput = document.querySelector(`input[name=otp-${index - 1}]`) as HTMLInputElement;
      if (prevInput) prevInput.focus();
    }

    // Handle arrow keys
    if (e.key === 'ArrowLeft' && index > 0) {
      const prevInput = document.querySelector(`input[name=otp-${index - 1}]`) as HTMLInputElement;
      if (prevInput) prevInput.focus();
    }

    if (e.key === 'ArrowRight' && index < 5) {
      const nextInput = document.querySelector(`input[name=otp-${index + 1}]`) as HTMLInputElement;
      if (nextInput) nextInput.focus();
    }
  }, [otp]);

  const verifyOTP = useCallback(async () => {
    const otpString = otp.join('');

    if (otpString.length !== 6) {
      setError('Please enter a valid 6-digit OTP');
      return;
    }

    // Prevent multiple simultaneous calls
    if (isVerifying) {
      console.log('Already verifying, skipping...');
      return;
    }

    console.log('Starting OTP verification...');
    setIsVerifying(true);
    try {
      const verificationResponse = await verifyOTPAPI({
        otpCode: otpString,
        email: email,
        timestamp: new Date().toISOString()
      });

      console.log('OTP verification response:', verificationResponse);

      if (verificationResponse.success) {
        const responseData = verificationResponse.data;
        const requiresPinSetup = responseData.requiresPinSetup;

        console.log('Response data:', responseData);
        console.log('Requires PIN setup:', requiresPinSetup);

        // Determine target route first
        const targetRoute = requiresPinSetup ? '/setup-pin' : '/dashboard';
        console.log('Target route:', targetRoute);

        // Store user data and token if provided
        if (responseData.user || responseData.userId || responseData.email) {
          // Handle new response structure with user object
          const userInfo = responseData.user;
          const userData = {
            id: userInfo?.id?.toString() || responseData.userId?.toString() || '1',
            email: userInfo?.email || responseData.email || email,
            name: userInfo?.fullName || userInfo?.name || 'User',
            organizerId: userInfo?.organizerId
          };

          // Store user data using auth service
          storeUserData(userData);
          console.log('User data stored:', userData);

          // Store new token if provided
          if (responseData.token) {
            storeAuthToken(responseData.token);
            console.log('New token stored');
          }

          // Refresh user in AuthContext and navigate
          refreshUser();
          console.log('User refreshed in AuthContext');
          navigate(targetRoute, { replace: true });
          return;
        }

        // Use immediate navigation without delay
        try {
          navigate(targetRoute, { replace: true });
          console.log('Navigation completed successfully');
        } catch (navError) {
          console.error('Navigation error:', navError);
          setError('Navigation failed. Please try again.');
        }
      } else {
        setError(verificationResponse.message || 'OTP verification failed. Please try again.');
      }
    } catch (error) {
      console.error('OTP verification error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Invalid OTP. Please try again.';
      setError(errorMessage);
    } finally {
      setIsVerifying(false);
    }
  }, [otp, email, navigate, isVerifying, refreshUser]);

  const handleResendOTP = useCallback(async () => {
    setIsResending(true);
    setError('');

    try {
      const resendResponse = await resendOTPAPI();

      if (resendResponse.success) {
        alert(resendResponse.message);
      } else {
        setError(resendResponse.message);
      }
    } catch (error) {
      console.error('Resend OTP error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to resend OTP. Please try again.';
      setError(errorMessage);
    } finally {
      setIsResending(false);
    }
  }, []);

  // Handle button click with event prevention
  const handleVerifyClick = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('Verify button clicked');
    verifyOTP();
  }, [verifyOTP]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-orange-100 flex items-center justify-center py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <div className="w-32 h-32 bg-white rounded-2xl flex items-center justify-center shadow-lg p-4">
              <img
                src={logoImage}
                alt="Pay Connect Logo"
                className="w-full h-full object-contain"
              />
            </div>
          </div>
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Verify your email
          </h2>
          <p className="text-gray-600 text-sm sm:text-base mb-2">
            Enter the 6-digit code sent to your email
          </p>
          <p className="text-gray-500 text-xs mb-4">
            You can paste the code directly into any field
          </p>
          {email && (
            <div className="flex items-center justify-center space-x-2 bg-white rounded-lg p-3 shadow-sm">
              <Mail className="w-5 h-5 text-orange-500" />
              <p className="text-sm font-medium text-gray-900">
                {maskEmail(email)}
              </p>
            </div>
          )}
        </div>

        <div className="space-y-4">
          <div className="flex justify-center space-x-2 sm:space-x-3">
            {otp.map((digit, index) => (
              <input
                key={index}
                name={`otp-${index}`}
                type="text"
                inputMode="numeric"
                pattern="[0-9]*"
                maxLength={1}
                value={digit}
                onChange={(e) => handleOtpChange(index, e.target.value)}
                onPaste={handlePaste}
                onKeyDown={(e) => handleKeyDown(index, e)}
                className="w-12 h-12 sm:w-14 sm:h-14 text-center text-2xl font-bold border-2 border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 bg-gray-50"
                autoFocus={index === 0}
                disabled={isVerifying}
              />
            ))}
          </div>

          {error && (
            <div className="text-red-600 text-sm text-center bg-red-50 p-3 rounded-lg">
              {error}
            </div>
          )}

          <div className="flex flex-col space-y-4">
            <button
              type="button"
              onClick={handleVerifyClick}
              disabled={isVerifying || otp.some(digit => digit === '')}
              className="w-full bg-orange-600 text-white py-4 px-6 rounded-xl font-semibold hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              {isVerifying ? (
                <div className="flex items-center justify-center space-x-3">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Verifying your code...</span>
                </div>
              ) : (
                'Verify & Continue'
              )}
            </button>

            <button
              type="button"
              onClick={handleResendOTP}
              disabled={isResending}
              className="w-full bg-orange-50 text-orange-600 py-3 px-4 rounded-xl font-medium hover:bg-orange-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 transition-all duration-200"
            >
              {isResending ? 'Resending...' : 'Resend OTP'}
            </button>
          </div>
        </div>

        <div className="text-center">
          <p className="text-xs text-gray-500">
            Didn't receive the code? Check your spam folder
          </p>
        </div>
      </div>
    </div>
  );
};

export default OTPVerification;
