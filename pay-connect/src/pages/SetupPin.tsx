import React, { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Eye, EyeOff, ArrowRight, Shield, Lock } from 'lucide-react';
import { createWallet } from '../services/wallet';
import { setPinSetup } from '../services/pinService';
import logoImage from '../assets/images/giu_pay_connect.png';
import PaymentLoader from '../components/common/PaymentLoader';

const SetupPin: React.FC = () => {
  const [pin, setPin] = useState(['', '', '', '']);
  const [confirmPin, setConfirmPin] = useState(['', '', '', '']);
  const [showPin, setShowPin] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);
  const [step, setStep] = useState(1); // 1: enter, 2: confirm
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  
  const pinInputs = [useRef<HTMLInputElement>(null), useRef<HTMLInputElement>(null), useRef<HTMLInputElement>(null), useRef<HTMLInputElement>(null)];
  const confirmInputs = [useRef<HTMLInputElement>(null), useRef<HTMLInputElement>(null), useRef<HTMLInputElement>(null), useRef<HTMLInputElement>(null)];

  const handlePinChange = (idx: number, value: string) => {
    if (!/^[0-9]?$/.test(value)) return;
    const newPin = [...pin];
    newPin[idx] = value;
    setPin(newPin);
    setError('');
    
    // Auto-focus next input
    if (value && idx < 3) {
      pinInputs[idx + 1].current?.focus();
    }
    // Auto-focus previous input on backspace
    if (!value && idx > 0) {
      pinInputs[idx - 1].current?.focus();
    }
  };

  const handlePinKeyDown = (idx: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Backspace' && !pin[idx] && idx > 0) {
      pinInputs[idx - 1].current?.focus();
    }
  };

  const handleConfirmChange = (idx: number, value: string) => {
    if (!/^[0-9]?$/.test(value)) return;
    const newPin = [...confirmPin];
    newPin[idx] = value;
    setConfirmPin(newPin);
    setError('');
    
    // Auto-focus next input
    if (value && idx < 3) {
      confirmInputs[idx + 1].current?.focus();
    }
    // Auto-focus previous input on backspace
    if (!value && idx > 0) {
      confirmInputs[idx - 1].current?.focus();
    }
  };

  const handleConfirmKeyDown = (idx: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Backspace' && !confirmPin[idx] && idx > 0) {
      confirmInputs[idx - 1].current?.focus();
    }
  };

  const handleContinue = () => {
    if (pin.some(p => p === '')) {
      setError('Please enter your 4-digit PIN');
      return;
    }
    setStep(2);
    // Focus first confirm input after a brief delay
    setTimeout(() => {
      confirmInputs[0].current?.focus();
    }, 100);
  };

  const handleSetPin = async () => {
    if (confirmPin.some(p => p === '')) {
      setError('Please confirm your 4-digit PIN');
      return;
    }
    if (pin.join('') !== confirmPin.join('')) {
      setError('PINs do not match');
      return;
    }

    setIsLoading(true);
    try {
      const pinString = pin.join('');

      // Create wallet using the wallet service
      console.log('Creating wallet with PIN...');
      const result = await createWallet(pinString);

      console.log('Wallet creation result:', result);

      if (result.success) {
        console.log('Wallet created successfully');

        // Set PIN as configured in local storage
        setPinSetup();

        // Update user data in localStorage with wallet info
        const existingUser = JSON.parse(localStorage.getItem('user') || '{}');
        const updatedUser = {
          ...existingUser,
          walletId: result.walletId,
          pinSetup: true
        };

        localStorage.setItem('user', JSON.stringify(updatedUser));
        console.log('Updated user data:', updatedUser);

        // Navigate to dashboard using page reload to ensure AuthContext updates
        console.log('Navigating to dashboard...');
        window.location.href = '/dashboard';
      } else {
        setError(result.message || 'Failed to create wallet. Please try again.');
      }
    } catch (error) {
      console.error('Error setting PIN or creating wallet:', error);
      setError('Failed to set PIN. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    if (step === 2) {
      setStep(1);
      setConfirmPin(['', '', '', '']);
      setError('');
      // Focus first pin input
      setTimeout(() => {
        pinInputs[0].current?.focus();
      }, 100);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-orange-100 flex items-center justify-center py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <div className="w-32 h-32 bg-white rounded-2xl flex items-center justify-center shadow-lg p-4">
              <img
                src={logoImage}
                alt="Pay Connect Logo"
                className="w-full h-full object-contain"
              />
            </div>
          </div>
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            {step === 1 ? 'Set Up Your PIN' : 'Confirm Your PIN'}
          </h2>
          <p className="text-gray-600 text-sm sm:text-base">
            {step === 1 
              ? 'Create a 4-digit PIN for secure transactions'
              : 'Please confirm your 4-digit PIN to continue'
            }
          </p>
        </div>

        <div className="bg-white rounded-2xl shadow-xl p-6 sm:p-8">
          {step === 1 && (
            <div className="space-y-6">
              <div className="text-center">
                <Lock className="w-12 h-12 text-orange-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Enter Your PIN</h3>
                <p className="text-sm text-gray-600">This PIN will be used for all transactions</p>
              </div>

              <div className="flex justify-center space-x-3 sm:space-x-4 mb-6 relative">
                {[0, 1, 2, 3].map((idx) => (
                  <input
                    key={idx}
                    ref={pinInputs[idx]}
                    type={showPin ? 'text' : 'password'}
                    inputMode="numeric"
                    maxLength={1}
                    className={`w-14 h-14 sm:w-16 sm:h-16 rounded-xl border-2 text-center text-2xl font-bold bg-gray-50 focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 ${
                      error ? 'border-red-400' : 'border-gray-300'
                    }`}
                    value={pin[idx]}
                    onChange={e => handlePinChange(idx, e.target.value)}
                    onKeyDown={e => handlePinKeyDown(idx, e)}
                    autoFocus={idx === 0}
                  />
                ))}
                <button
                  type="button"
                  aria-label={showPin ? 'Hide PIN' : 'Show PIN'}
                  onClick={() => setShowPin(!showPin)}
                  className="absolute -right-12 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none p-2"
                  tabIndex={-1}
                >
                  {showPin ? <EyeOff size={20} /> : <Eye size={20} />}
                </button>
              </div>

              {error && (
                <div className="text-red-600 text-sm text-center bg-red-50 p-3 rounded-lg">
                  {error}
                </div>
              )}

              <button
                onClick={handleContinue}
                disabled={pin.some(p => p === '')}
                className="w-full bg-orange-600 text-white py-4 px-6 rounded-xl font-semibold hover:bg-orange-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center space-x-2 text-base shadow-lg hover:shadow-xl"
              >
                <span>Continue</span>
                <ArrowRight size={20} />
              </button>
            </div>
          )}

          {step === 2 && (
            <div className="space-y-6">
              <div className="text-center">
                <Lock className="w-12 h-12 text-orange-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Confirm Your PIN</h3>
                <p className="text-sm text-gray-600">Please re-enter your PIN to confirm</p>
              </div>

              <div className="flex justify-center space-x-3 sm:space-x-4 mb-6 relative">
                {[0, 1, 2, 3].map((idx) => (
                  <input
                    key={idx}
                    ref={confirmInputs[idx]}
                    type={showConfirm ? 'text' : 'password'}
                    inputMode="numeric"
                    maxLength={1}
                    className={`w-14 h-14 sm:w-16 sm:h-16 rounded-xl border-2 text-center text-2xl font-bold bg-gray-50 focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 ${
                      error ? 'border-red-400' : 'border-gray-300'
                    }`}
                    value={confirmPin[idx]}
                    onChange={e => handleConfirmChange(idx, e.target.value)}
                    onKeyDown={e => handleConfirmKeyDown(idx, e)}
                    autoFocus={idx === 0}
                  />
                ))}
                <button
                  type="button"
                  aria-label={showConfirm ? 'Hide PIN' : 'Show PIN'}
                  onClick={() => setShowConfirm(!showConfirm)}
                  className="absolute -right-12 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none p-2"
                  tabIndex={-1}
                >
                  {showConfirm ? <EyeOff size={20} /> : <Eye size={20} />}
                </button>
              </div>

              {error && (
                <div className="text-red-600 text-sm text-center bg-red-50 p-3 rounded-lg">
                  {error}
                </div>
              )}

              <div className="flex space-x-3">
                <button
                  onClick={handleBack}
                  className="flex-1 bg-gray-100 text-gray-700 py-4 px-6 rounded-xl font-semibold hover:bg-gray-200 transition-all duration-200"
                >
                  Back
                </button>
                <button
                  onClick={handleSetPin}
                  disabled={confirmPin.some(p => p === '') || isLoading}
                  className="flex-1 bg-orange-600 text-white py-4 px-6 rounded-xl font-semibold hover:bg-orange-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center space-x-2 text-base shadow-lg hover:shadow-xl"
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center space-x-3">
                      <PaymentLoader
                        type="setup"
                        size="small"
                        showQuotes={false}
                      />
                      <span>Setting up your secure PIN...</span>
                    </div>
                  ) : (
                    <>
                      <span>Set PIN</span>
                      <ArrowRight size={20} />
                    </>
                  )}
                </button>
              </div>
            </div>
          )}
        </div>

        <div className="text-center">
          <p className="text-xs text-gray-500">
            Your PIN is encrypted and stored securely
          </p>
        </div>
      </div>
    </div>
  );
};

export default SetupPin; 