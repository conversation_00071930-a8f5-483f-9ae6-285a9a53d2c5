import React, { useState, useEffect } from 'react';
import { Shield, AlertCircle, RefreshCw, Banknote, CheckCircle, Clock } from 'lucide-react';
import PlaidLink from '../components/BankAccount/PlaidLink';
import ConnectedAccounts from '../components/BankAccount/ConnectedAccounts';
import BankAccountSummary from '../components/BankAccount/BankAccountSummary';
// import AuthDebug from '../components/Debug/AuthDebug';
// import PlaidDemo from '../components/BankAccount/PlaidDemo';
import { getConnectedAccounts, ConnectedBankAccount } from '../services/plaid';
import PaymentLoader from '../components/common/PaymentLoader';
import { bankBalanceService } from '../services/bankBalance';
 
/**
 * Modern Banking App Behavior:
 *
 * 1. **Smart Caching**: Balance data is cached for 5 minutes to reduce API calls
 * 2. **Real-time Updates**: Manual refresh fetches fresh data from banks via Plaid
 * 3. **Auto-refresh**: Automatically checks for stale data every minute
 * 4. **Visual Indicators**: Shows when data is outdated and needs refresh
 * 5. **Instance-based Totals**: Total balance calculated from live bank data
 *
 * This mimics how modern banking apps like Chase, Bank of America work:
 * - Show cached balances for quick loading
 * - Allow manual refresh to get latest transactions
 * - Indicate when balances might be outdated
 */
const BankAccount: React.FC = () => {
  const [connectedAccounts, setConnectedAccounts] = useState<ConnectedBankAccount[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [refreshSuccess, setRefreshSuccess] = useState(false);
 
  const fetchConnectedAccounts = async (isRefresh = false, forceRefresh = false) => {
    try {
      if (isRefresh) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }
      setError(null);
 
      // Check if we can use cached data (unless force refresh)
      if (!forceRefresh && !isRefresh) {
        const cachedBalance = bankBalanceService.getCachedBalance();
        if (cachedBalance) {
          console.log('📊 Using cached balance data');
          setConnectedAccounts(cachedBalance.accounts);
          setLastUpdated(cachedBalance.timestamp);
          setIsLoading(false);
          return;
        }
      }
 
      // Add artificial delay to show the engaging loader (only for initial load)
      if (!isRefresh) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
 
      // Fetch fresh data from Plaid API (real-time balance checking)
      console.log('🔄 Fetching fresh balance data from banks...');
      const accounts = await getConnectedAccounts();
 
      // Update cache with fresh data
      bankBalanceService.updateCache(accounts);
 
      // Store accounts with timestamp for UI
      setConnectedAccounts(accounts);
      setLastUpdated(new Date());
 
      // Show success message for refresh operations
      if (isRefresh) {
        setRefreshSuccess(true);
        setTimeout(() => setRefreshSuccess(false), 3000); // Hide after 3 seconds
      }
 
    } catch (err) {
      console.error('Error fetching connected accounts:', err);
      setError('Failed to load connected accounts. Please try again.');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };
 
  useEffect(() => {
    fetchConnectedAccounts();
 
    // Set up auto-refresh check (like modern banking apps)
    const autoRefreshInterval = setInterval(() => {
      if (bankBalanceService.needsRefresh() && connectedAccounts.length > 0) {
        console.log('🔄 Auto-refreshing stale balance data...');
        fetchConnectedAccounts(false, false); // Silent refresh
      }
    }, 60000); // Check every minute
 
    return () => clearInterval(autoRefreshInterval);
  }, [connectedAccounts.length]);
 
  const handlePlaidSuccess = () => {
    // Refresh the connected accounts list
    fetchConnectedAccounts();
  };
 
  const handlePlaidError = (error: any) => {
    console.error('Plaid connection error:', error);
    setError('Failed to connect bank account. Please try again.');
  };
 
  const handleAccountUpdate = () => {
    // Refresh the connected accounts list
    fetchConnectedAccounts();
  };
 
  const handleRefreshBalances = () => {
    // Force refresh bank account balances (bypass cache)
    bankBalanceService.clearCache();
    fetchConnectedAccounts(true, true);
  };
 
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="bg-white rounded-xl  p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Bank Account</h2>
          <div className="text-center py-12 flex items-center justify-center [min-height:670px]">
            <PaymentLoader
              type="wallet"
              message="Loading your bank accounts..."
              size="large"
              showQuotes={true}
            />
          </div>
        </div>
      </div>
    );
  }
 
  return (
    <div className="space-y-6">
      {/* Header */}
      {/* <div className="bg-white rounded-xl shadow-sm border p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Bank Account</h2>
        <p className="text-gray-600">Securely connect and manage your bank accounts</p>
      </div> */}
 
      {/* Debug Component - Remove this in production */}
      {/* <AuthDebug /> */}
 
      {/* Success Message */}
      {refreshSuccess && (
        <div className="bg-green-50 border border-green-200 rounded-xl p-4">
          <div className="flex items-start space-x-3">
            <CheckCircle className="text-green-600 mt-0.5" size={20} />
            <div>
              <h3 className="font-medium text-green-900">Balances Updated</h3>
              <p className="text-sm text-green-700 mt-1">Your bank account balances have been refreshed successfully.</p>
            </div>
          </div>
        </div>
      )}
 
      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-xl p-4">
          <div className="flex items-start space-x-3">
            <AlertCircle className="text-red-600 mt-0.5" size={20} />
            <div>
              <h3 className="font-medium text-red-900">Connection Error</h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
              <button
                // onClick={fetchConnectedAccounts}
                className="mt-2 text-sm text-red-600 hover:text-red-700 underline"
              >
                Try again
              </button>
            </div>
          </div>
        </div>
      )}
 
      {/* Account Summary */}
      {connectedAccounts.length > 0 && (
        <div className="space-y-4">
          {/* Header with Refresh Button */}
          <div className="bg-white rounded-xl shadow-sm border p-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">Bank Account Summary</h2>
                <div className="flex items-center space-x-4">
                  <p className="text-gray-600">Overview of all your connected accounts</p>
                  {lastUpdated && (
                    <div className="flex items-center space-x-2">
                      <p className="text-sm text-gray-500">
                        Last updated: {lastUpdated.toLocaleTimeString()}
                      </p>
                      <span className="text-xs text-gray-400">•</span>
                      <p className="text-xs text-gray-400">
                        {bankBalanceService.getCacheStatus()}
                      </p>
                    </div>
                  )}
                </div>
              </div>
              <div className="flex items-center space-x-3">
                {/* Stale data indicator */}
                {bankBalanceService.needsRefresh() && connectedAccounts.length > 0 && (
                  <div className="flex items-center space-x-1 text-amber-600 bg-amber-50 px-2 py-1 rounded-md">
                    <Clock size={14} />
                    <span className="text-xs font-medium">Data may be outdated</span>
                  </div>
                )}
 
                <button
                  onClick={handleRefreshBalances}
                  disabled={isRefreshing}
                  className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <RefreshCw
                    size={16}
                    className={`${isRefreshing ? 'animate-spin' : ''}`}
                  />
                  <span>{isRefreshing ? 'Refreshing...' : 'Refresh Balances'}</span>
                </button>
              </div>
            </div>
          </div>
 
          <BankAccountSummary accounts={connectedAccounts} isRefreshing={isRefreshing} />
        </div>
      )}

        {/* Security Notice */}
      <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
        <div className="flex items-start space-x-3">
          <Shield className="text-blue-600 mt-0.5" size={20} />
          <div>
            <h3 className="font-medium text-blue-900">Bank-level Security</h3>
            <p className="text-sm text-blue-700 mt-1">
              Your bank account information is protected with 256-bit encryption and is never stored on our servers.
              We use Plaid, a trusted financial technology company, to securely connect to your bank.
            </p>
          </div>
        </div>
      </div>
 
      {/* Connected Accounts */}
      {connectedAccounts.length > 0 && (
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <ConnectedAccounts
            accounts={connectedAccounts}
            onAccountUpdate={handleAccountUpdate}
          />
        </div>
      )}
    
 
      {/* Plaid <PlaidDemo /> Demo */}
 
 
      {/* Add Bank Account */}
      <div className="bg-white rounded-xl shadow-sm border p-6">
        {connectedAccounts.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-gray-100 rounded-full mx-auto mb-6 flex items-center justify-center">
              <Banknote className="text-gray-400 w-12 h-12" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Bank Accounts Connected</h3>
            <p className="text-gray-500 mb-6">Connect your bank account to start managing your finances</p>
            <PlaidLink
              onSuccess={handlePlaidSuccess}
              onError={handlePlaidError}
            />
          </div>
        ) : (
          <div className="text-center py-8">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Add Another Bank Account</h3>
            <p className="text-gray-500 mb-6">Connect additional bank accounts for better financial management</p>
            <PlaidLink
              onSuccess={handlePlaidSuccess}
              onError={handlePlaidError}
            />
          </div>
        )}
      </div>
    </div>
  );
};
 
export default BankAccount;