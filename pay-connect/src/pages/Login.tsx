import React, { useEffect, useState, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { Shield, AlertTriangle, RefreshCw } from 'lucide-react';
import { login as loginAPI, storeAuthToken } from '../services/authService';
import logoImage from '../assets/images/giu_pay_connect.png';
import PaymentLoader from '../components/common/PaymentLoader';

const Login: React.FC = () => {
  const [isAuthenticating, setIsAuthenticating] = useState(true);
  const [authError, setAuthError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const { user } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const from = location.state?.from?.pathname || '/dashboard';
  const hasTriggeredLogin = useRef(false);

  useEffect(() => {
    // If user is already authenticated, redirect
    if (user) {
      navigate(from, { replace: true });
      return;
    }

    // Prevent multiple API calls
    if (hasTriggeredLogin.current) {
      return;
    }

    const params = new URLSearchParams(location.search);
    let auth = params.get('auth');

    // Handle URL decoding issues where + characters might be converted to spaces
    if (auth) {
      // Replace spaces back to + characters for base64 decoding
      // This handles cases where the URL wasn't properly encoded and + became spaces
      auth = auth.replace(/ /g, '+');

      // Ensure proper base64 padding if needed
      while (auth.length % 4) {
        auth += '=';
      }
    }

    console.log('Original auth from URL:', params.get('auth'));
    console.log('Processed auth:', auth);

    // Trigger login API on page load
    const triggerLoginAPI = async () => {
      try {
        hasTriggeredLogin.current = true;
        setIsAuthenticating(true);
        console.log('Triggering login API on page load...');

        const loginResponse = await loginAPI({
          timestamp: new Date().toISOString(),
          auth: auth || undefined,
          source: 'page_load'
        });

        console.log('Login API response:', loginResponse);

        if (loginResponse.success) {
          const { token, user } = loginResponse.data;

          console.log(token, "token", user);
          storeAuthToken(token);

          navigate('/verify-otp', { state: { user, email: user.email }, replace: true });
        } else {
          setAuthError(true);
          setErrorMessage(loginResponse.message || 'Authentication failed. Please try again.');
          setIsAuthenticating(false);
        }
      } catch (error) {
        console.error('Login API error:', error);
        setAuthError(true);

        const errorMessage = error instanceof Error ? error.message : 'Authentication failed. You are not authorized to access this application.';
        setErrorMessage(errorMessage);
        setIsAuthenticating(false);
      }
    };

    // Call the API on component mount
    triggerLoginAPI();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Empty dependency array to run only once on mount and prevent duplicate API calls


  const handleRetry = () => {
    setAuthError(false);
    setErrorMessage('');
    setIsAuthenticating(true);
    hasTriggeredLogin.current = false; // Reset the flag
    // Reload the page to trigger the authentication flow again
    window.location.reload();
  };

  const handleGoHome = () => {
    navigate('/', { replace: true });
  };

  // Show loading screen while authenticating
  if (isAuthenticating) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-2">
              Welcome to Pay Connect
            </h2>
            <p className="text-gray-600 text-sm sm:text-base">
              Preparing your secure financial dashboard
            </p>
          </div>

          <div className="bg-white rounded-2xl shadow-xl p-8">
            <PaymentLoader
              type="authentication"
              message="Authenticating your access..."
              size="large"
              showQuotes={true}
            />
          </div>
        </div>
      </div>
    );
  }

  // Show unauthorized screen if authentication failed
  if (authError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="w-32 h-32 bg-white rounded-2xl flex items-center justify-center shadow-lg p-4">
                <img
                  src={logoImage}
                  alt="Pay Connect Logo"
                  className="w-full h-full object-contain"
                />
              </div>
            </div>
            <h2 className="text-3xl font-bold text-gray-900 mb-2">
              Access Denied
            </h2>
            <p className="text-gray-600 text-sm sm:text-base mb-6">
              You are not authorized to access this application.
            </p>
          </div>

          <div className="bg-white rounded-2xl shadow-xl p-6 sm:p-8">
            <div className="text-center space-y-4">
              <div className="p-4 bg-red-50 rounded-lg border border-red-200">
                <div className="flex items-center justify-center space-x-2 text-red-800">
                  <Shield className="w-5 h-5" />
                  <span className="text-sm font-medium">Authentication Failed</span>
                </div>
                <p className="text-sm text-red-600 mt-2">
                  {errorMessage}
                </p>
              </div>

              <div className="space-y-3">
                <button
                  onClick={handleRetry}
                  className="w-full flex items-center justify-center space-x-2 py-3 px-4 border border-transparent text-base font-medium rounded-xl text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  <RefreshCw className="w-5 h-5" />
                  <span>Try Again</span>
                </button>

                <button
                  onClick={handleGoHome}
                  className="w-full py-3 px-4 border border-gray-300 text-base font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200"
                >
                  Go to Home
                </button>
              </div>
            </div>
          </div>

          <div className="text-center">
            <p className="text-xs text-gray-500">
              If you continue to experience issues, please contact support
            </p>
          </div>
        </div>
      </div>
    );
  }

  // This should not be reached as we either show loading or error
  return null;
};

export default Login;