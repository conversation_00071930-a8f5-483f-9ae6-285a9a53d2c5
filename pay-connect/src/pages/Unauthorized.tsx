import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Shield, AlertTriangle, RefreshCw } from 'lucide-react';

const Unauthorized: React.FC = () => {
  const navigate = useNavigate();

  const handleRetry = () => {
    // Navigate back to login to retry the authentication
    navigate('/login', { replace: true });
  };

  const handleGoHome = () => {
    // Navigate to home/dashboard
    navigate('/', { replace: true });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <div className="w-16 h-16 bg-red-500 rounded-2xl flex items-center justify-center shadow-lg">
              <AlertTriangle className="w-8 h-8 text-white" />
            </div>
          </div>
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Access Denied
          </h2>
          <p className="text-gray-600 text-sm sm:text-base mb-6">
            You are not authorized to access this application. Please check your credentials or contact support.
          </p>
        </div>
        
        <div className="bg-white rounded-2xl shadow-xl p-6 sm:p-8">
          <div className="text-center space-y-4">
            <div className="p-4 bg-red-50 rounded-lg border border-red-200">
              <div className="flex items-center justify-center space-x-2 text-red-800">
                <Shield className="w-5 h-5" />
                <span className="text-sm font-medium">Authentication Failed</span>
              </div>
              <p className="text-sm text-red-600 mt-2">
                The server could not verify your identity. This could be due to:
              </p>
              <ul className="text-sm text-red-600 mt-2 text-left list-disc list-inside space-y-1">
                <li>Invalid or expired authentication token</li>
                <li>Account access has been revoked</li>
                <li>Server authentication error</li>
              </ul>
            </div>

            <div className="space-y-3">
              <button
                onClick={handleRetry}
                className="w-full flex items-center justify-center space-x-2 py-3 px-4 border border-transparent text-base font-medium rounded-xl text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                <RefreshCw className="w-5 h-5" />
                <span>Try Again</span>
              </button>
              
              <button
                onClick={handleGoHome}
                className="w-full py-3 px-4 border border-gray-300 text-base font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200"
              >
                Go to Home
              </button>
            </div>
          </div>
        </div>

        <div className="text-center">
          <p className="text-xs text-gray-500">
            If you continue to experience issues, please contact support
          </p>
        </div>
      </div>
    </div>
  );
};

export default Unauthorized;
