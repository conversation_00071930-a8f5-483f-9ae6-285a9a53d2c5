# Pay Connect

## Project Description

Pay Connect is a web application that facilitates payment processing and management. It provides a dashboard for monitoring transactions, managing dues, and handling staff payments.

## Installation

1.  Clone the repository:

    ```bash
    git clone <repository_url>
    ```
2.  Install dependencies:

    ```bash
    npm install
    ```

## Usage

1.  Start the development server:

    ```bash
    npm run dev
    ```
2.  Open the application in your browser at `http://localhost:5173`.

## Features

*   Dashboard with summary cards and transaction history
*   Awaiting payments management
*   Dues management
*   Staff management
*   Payment processing
*   User authentication

## Contributing

Contributions are welcome! Please fork the repository and submit a pull request with your changes.

## License

[MIT](LICENSE)
